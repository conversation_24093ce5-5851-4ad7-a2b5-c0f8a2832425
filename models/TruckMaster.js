const mongoose = require('mongoose');

const TruckMasterSchema = new mongoose.Schema({
    truckType: { type: String, required: true },
    truckNumber: { type: String, required: true },
    status:{type: String, enum: ['Available', 'NotAvailable'], default: 'Available'},
    owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster', required: true },
    lastUsed:{type:mongoose.Schema.Types.ObjectId, ref:'StaffMaster',default:null}
});

module.exports = mongoose.model('TruckMaster', TruckMasterSchema);