const mongoose = require('mongoose');

const CustomerTypeEnum = ['Hospital', 'Individual', 'Shop', 'Factory', 'Workshop'];
const PaymentEnum = ['Credit', 'Cash'];

const CustomerMasterSchema = new mongoose.Schema({

owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster' },
  
  name: { 
    type: String, 
    required: true,
    index: true 
  },
  customerType: { 
    type: String, 
    enum: CustomerTypeEnum, 
    required: true,
    index: true 
  },
  address: {
    street: String,
    city: String,
    state: String,
    postalCode: String
  },
  contact: {
    phone: String,
    email: String
  },
  payment: { 
    type: String, 
    enum: PaymentEnum, 
    required: true 
  },
  purchasedHistory: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'SellMaster' 
  }],
  returnedHistory: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'CylinderMaster' 
  }],
  priceList: {type: mongoose.Schema.Types.ObjectId, ref: 'PriceMaster'},

}, { 
  timestamps: true 
});

// // Custom validation for unique combination of size and pressure in prices
// CustomerMasterSchema.pre('save', function (next) {
//   const seen = new Set();
  
//   for (const price of this.prices) {
//     const key = `${price.size}-${price.pressure}`;
//     if (seen.has(key)) {
//       return next(new Error('Duplicate combination of size and pressure found in prices array.'));
//     }
//     seen.add(key);
//   }
//   next();
// });

module.exports = mongoose.model('CustomerMaster', CustomerMasterSchema);