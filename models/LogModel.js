const mongoose = require('mongoose');
const LogModelSchema = new mongoose.Schema({
    logType: { type: String, required: true },
    logDate: { type: Date, required: true },
    logBy: { type: mongoose.Schema.Types.ObjectId, ref: 'StaffMaster' },
    remarks: { type: String, default: '' },
    owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster' },
    
}, { timestamps: true });
module.exports = mongoose.model('LogModel', LogModelSchema);