const mongoose = require('mongoose');

const MaintainMasterSchema = new mongoose.Schema({
    errorType: { type: String, required: true },
    cylinder: { type: mongoose.Schema.Types.ObjectId, ref: 'CylinderMaster', required: true },
    fromCustomer:{type: mongoose.Schema.Types.ObjectId, ref:'CustomerMaster',default:null},
    fixedDate: { type: Date, default: null },
    startedDate: { type: Date, required:true},
    fixedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'StaffMaster' },
    remarks: { type: String, default: '' },
    owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster' },
}, { timestamps: true
    
});

module.exports = mongoose.model('MaintainMaster', MaintainMasterSchema);