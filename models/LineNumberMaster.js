const mongoose = require('mongoose');

const LineNumberSchema = new mongoose.Schema({
    lineNumber: { type: Number, required: true },
    status: { type: String, enum: ['Available', 'NotAvailable'], default: 'Available' },
    owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster' },
    type: { type: String, enum: ['Medical', 'Industrial'], default: 'Industrial' }
});

module.exports = mongoose.model('LineNumberMaster', LineNumberSchema);