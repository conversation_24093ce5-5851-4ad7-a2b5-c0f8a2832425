const mongoose = require("mongoose");
const Schema = mongoose.Schema;
const moment = require("moment-timezone");

const SellMasterSchema = new Schema(
  {
    owner: { type: mongoose.Schema.Types.ObjectId, ref: "FactoryMaster" },
    sellId: {
      type: String,
      required: true,
      unique: true, // Ensure uniqueness
      index: true, // Improve query performance
    },
    cylinders: [
      {
        cylinder: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "CylinderMaster",
          required: true,
        },
        price: { type: Number, required: true },
      },
    ],
    customer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CustomerMaster",
      required: true,
    },
    returnedCylinders: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "CylinderMaster",
        required: true,
      },
    ],
    quantity: {
      type: Number,
      required: true,
    },
    round: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "RoundMaster",
      default: null,
    },
    staff: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "StaffMaster",
      required: true,
    },
    discount: {
      type: Number,
      default: 0,
    },
    payment: {
      type: String,
      enum: ["Credit", "Cash"],
    },
    total: {
      type: Number,
      required: true,
    },
    date: {
      type: Date,
      default: () => moment().tz("Asia/Yangon").toDate(),
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("SellMaster", SellMasterSchema);
