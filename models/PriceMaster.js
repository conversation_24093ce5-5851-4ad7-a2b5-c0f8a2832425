const mongoose = require('mongoose');

const PriceMasterSchema = new mongoose.Schema({
    
        priceName: { type: String, required: true },
        owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster', required: true },
        prices:[{
            pressure: { 
                type: Number, 
                required: true,
                min: 1 
              },
              size: { 
                type: Number, 
                required: true,
                min: 1 
              },
              price: { 
                type: Number, 
                required: true,
                min: 0 
              },
              factoryPrice: { 
                type: Number, 
                required: true,
                min: 0
            }}
        ]
});

module.exports = mongoose.model('PriceMaster', PriceMasterSchema);

