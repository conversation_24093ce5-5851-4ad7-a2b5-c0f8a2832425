const mongoose = require("mongoose");

const StatusEnum = [
  "Full",
  "Available",
  "Empty",
  "In Transit",
  "Delivered",
  "Filling",
  "Taken",
  "Return",
  "Leakage Error",
  "Valve Error",
  "Key Error",
  "Damaged Error",
  "Other Error",
];
const ErrorEnum = [
  "Value_Leakage",
  "Value_Thread_Error",
  "Value_Key_Error",
  "Other",
];
const ValueTypeEnum = ["China", "Japan"];
const CylinderSizeEnum = ["47L", "40L", "15L", "10L"];

const CylinderMasterSchema = new mongoose.Schema({
  cylinderSize: { type: String, enum: CylinderSizeEnum, required: true },
  importDate: { type: Date, required: true },
  valueType: { type: String, enum: ValueTypeEnum, required: true },
  productionDate: { type: Date, required: true },
  originalNumber: { type: String, required: true, unique: true },
  serialNumber: { type: String },
  workingPressure: { type: Number, required: true },
  // designPressure: { type: Number, required: true },
  status: { type: String, enum: StatusEnum, required: true },
  error: { type: String, enum: ErrorEnum, default: null },
  owner: { type: mongoose.Schema.Types.ObjectId, ref: "FactoryMaster" },
  takenCustomer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "CustomerMaster",
  },
  owned: { type: Boolean, default: true },
  takenTruck: { type: mongoose.Schema.Types.ObjectId, ref: "TruckMaster" },
  latestDelivery: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "RoundMaster",
    default: null,
  },
  latestFilling: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FillingProcess",
    default: null,
  },
  usedCustomerList: [
    { type: mongoose.Schema.Types.ObjectId, ref: "CustomerMaster" },
  ],
  remarks: { type: String, default: "" },
});

// Helper function to generate next serial number in format A0001, A0002, etc.
const generateNextSerialNumber = async () => {
  try {
    // Find the latest cylinder with the new format (starts with a letter)
    const latestCylinder = await mongoose
      .model("CylinderMaster")
      .findOne({
        serialNumber: { $regex: /^[A-Z]\d{4}$/ },
      })
      .sort({ serialNumber: -1 })
      .exec();

    if (!latestCylinder) {
      // No cylinders with new format exist, start with A0001
      return "A0001";
    }

    const currentSerial = latestCylinder.serialNumber;
    const currentLetter = currentSerial.charAt(0);
    const currentNumber = parseInt(currentSerial.substring(1));

    // Calculate next serial number
    if (currentNumber < 9999) {
      // Increment number within same letter
      const nextNumber = (currentNumber + 1).toString().padStart(4, "0");
      return `${currentLetter}${nextNumber}`;
    } else {
      // Move to next letter, reset to 0001
      const nextLetterCode = currentLetter.charCodeAt(0) + 1;
      if (nextLetterCode > 90) {
        // Beyond 'Z'
        throw new Error("Serial number limit exceeded (Z9999)");
      }
      const nextLetter = String.fromCharCode(nextLetterCode);
      return `${nextLetter}0001`;
    }
  } catch (error) {
    console.error("Error generating serial number:", error);
    throw error;
  }
};

CylinderMasterSchema.pre("save", async function (next) {
  if (this.isNew && !this.serialNumber) {
    try {
      this.serialNumber = await generateNextSerialNumber();
      console.log("Generated Serial Number:", this.serialNumber);
    } catch (err) {
      console.error("Error in pre-save hook:", err);
      return next(err);
    }
  }
  next();
});

module.exports = mongoose.model("CylinderMaster", CylinderMasterSchema);
