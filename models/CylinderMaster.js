const mongoose = require('mongoose');

const StatusEnum = ['Full', 'Available', 'Empty','In Transit','Delivered', 'Filling','Taken','Return' ,'Leakage Error', 'Valve Error', 'Key Error', 'Damaged Error', 'Other Error'];
const ErrorEnum = ['Value_Leakage', 'Value_Thread_Error', 'Value_Key_Error', 'Other'];
const ValueTypeEnum = ['China', 'Japan'];
const CylinderSizeEnum = ['47L', '40L', '15L', '10L'];

const CylinderMasterSchema = new mongoose.Schema({
    cylinderSize: { type: String, enum: CylinderSizeEnum, required: true },
    importDate: { type: Date, required: true },
    valueType: { type: String, enum: ValueTypeEnum, required: true },
    productionDate: { type: Date, required: true },
    originalNumber: { type: String, required: true, unique: true },
    serialNumber: { type: String },
    workingPressure: { type: Number, required: true },
    // designPressure: { type: Number, required: true },
    status: { type: String, enum: StatusEnum, required: true },
    error: { type: String, enum: ErrorEnum, default: null },
    owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster' },
    takenCustomer: { type: mongoose.Schema.Types.ObjectId, ref: 'CustomerMaster' },
    owned: { type: Boolean, default: true },
    takenTruck : { type: mongoose.Schema.Types.ObjectId, ref: 'TruckMaster' },
    latestDelivery:{type:mongoose.Schema.Types.ObjectId, ref:'RoundMaster',default:null},
    latestFilling: { type: mongoose.Schema.Types.ObjectId, ref: 'FillingProcess' ,default:null},
    usedCustomerList: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CustomerMaster' }],
    remarks: { type: String, default: '' },

});

CylinderMasterSchema.pre('save', async function(next) {
    if (this.isNew) {
        try {
            const date = new Date().toISOString().split('T')[0].replace(/-/g, '');
            const count = await mongoose.model('CylinderMaster').countDocuments();
            console.log('Count:', count);
            this.serialNumber = `CYD${date}${count + 1}`;
            console.log('Generated Serial Number:', this.serialNumber);
        } catch (err) {
            return next(err);
        }
    }
    next();
});

module.exports = mongoose.model('CylinderMaster', CylinderMasterSchema);
