const mongoose = require("mongoose");
const AutoIncrement = require("mongoose-sequence")(mongoose);

const StaffMasterSchema = new mongoose.Schema({
  name: { type: String, required: true },
  workingPlace: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FactoryMaster",
    required: true,
  },
  contact: { type: String, required: true },
  address: { type: String, required: true },
  emergencyContact: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  banned: { type: <PERSON>olean, default: false },
  role: {
    type: String,
    enum: ["root", "admin", "manager", "sale", "fill"],
    required: true,
  },
  verified: { type: <PERSON>olean, default: false },
  resetToken: { type: String },
  resetTokenExpires: { type: Date },
});

// Add auto-increment plugin for idNo
StaffMasterSchema.plugin(AutoIncrement, { inc_field: "idNo" });

module.exports = mongoose.model("StaffMaster", StaffMasterSchema);
