const mongoose = require('mongoose');
const CylinderMaster = require('./CylinderMaster');
const moment = require('moment-timezone');

const StatusEnum = ['Wait Filling', 'Filling Started', 'Filling Finished', 'Filling Cancelled'];
const CylinderStatusEnum = ['Full', 'Available', 'Empty', 'Filling', 'Leakage Error', 'Valve Error', 'Key Error', 'Damaged Error', 'Other Error'];

const fillingProcessSchema = new mongoose.Schema({
    date: { type: Date, required: true, default: () => new Date() },
    fillingStartedAt: { type: Date, default: () => new Date().toISOString() },
    fillingEndedAt: { type: String, default: null },
    lineNumber: { type: mongoose.Schema.Types.ObjectId, ref: 'LineNumberMaster', required: true },
    cylinders: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CylinderMaster', default: [] }],
    cylinderQty: { type: Number, default: 0 },
    filledCylinders: { type: Number, default: 0 },
    rejectedCylinders: { type: Number, default: 0 },
    startedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'StaffMaster', required: true },
    endedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'StaffMaster', default: null },
    status: { type: String, enum: StatusEnum, default: StatusEnum[1] },
    remark: { type: String, trim: true, default: null },
    owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster', required: true },
}, { timestamps: true });

fillingProcessSchema.methods.startProcess = function() {
    this.status = 'Wait Filling';
    this.fillingStartedAt = new Date().toISOString();
    return this.save();
};

fillingProcessSchema.methods.addCylinder = async function(serialNumber) {
    const hasCylinder = await CylinderMaster.findOne({serialNumber: serialNumber});
    const cylinderId = hasCylinder._id;

    if (!hasCylinder) {
        throw new Error('Cylinder not found');
    }
    if (hasCylinder.status === 'Filling') {
        throw new Error('Cylinder is already in filling process');
    }
    if (hasCylinder.status === 'Full') {
        throw new Error('Cylinder is already full');
    }

    if (!this.cylinders.includes(cylinderId)) {
        this.cylinders.push(cylinderId);
        this.cylinderQty = this.cylinders.length;

        const cylinder = await CylinderMaster.findById(cylinderId);
        if (cylinder) {
            cylinder.status = 'Filling';
            cylinder.latestFilling = this._id;
            await cylinder.save();
        }
    }
    return this.save();
};

fillingProcessSchema.methods.removeCylinder = async function(cylinderId, status) {
    const index = this.cylinders.indexOf(cylinderId);
    if (index > -1) {
        this.cylinders.splice(index, 1);
        this.cylinderQty = this.cylinders.length;

        const cylinder = await CylinderMaster.findById(cylinderId);
        if (cylinder) {
            cylinder.status = status; // or any other appropriate status
            cylinder.latestFilling = null;
            await cylinder.save();
        }
    }
    return this.save();
};

fillingProcessSchema.methods.updateCylinderStatus = async function(cylinderId, status) {
    if (!CylinderStatusEnum.includes(status)) {
        throw new Error('Invalid status');
    }

    const cylinder = await CylinderMaster.findById(cylinderId);
    if (cylinder) {
        if (status === 'Full') {
            this.filledCylinders += 1;
        } else if (status === 'Error') {
            this.rejectedCylinders += 1;
        }
        cylinder.status = status;
        await cylinder.save();
    }
    return this.save();
};

fillingProcessSchema.methods.endProcess = async function(endedBy, remark = null, rejectedCylinders = []) {
    this.status = 'Filling Finished';
    this.fillingEndedAt = new Date().toISOString();

    console.log(this.fillingEndedAt);
    this.endedBy = endedBy;
    this.remark = remark;

    for (const cylinderId of this.cylinders) {
        const cylinder = await CylinderMaster.findById(cylinderId);
        if (cylinder) {
            cylinder.latestFilling = this._id;
            const rejectedCylinder = rejectedCylinders.find(rjCyd => rjCyd.id.toString() === cylinderId.toString());
            if (rejectedCylinder) {
                cylinder.status = rejectedCylinder.status;
                this.rejectedCylinders += 1;
            } else {
                cylinder.status = 'Full';
                this.filledCylinders += 1;
            }
            await cylinder.save();
        }
    }

    return this.save();
};

module.exports = mongoose.model('FillingProcess', fillingProcessSchema);