const mongoose = require('mongoose');

const RoundMasterSchema = new mongoose.Schema({
   date: { type: String, required: true, default: () => new Date().toISOString().split('T')[0] },
   roundTime: { type: String, required: true, default: () => new Date().toISOString().split('T')[1].split('.')[0] },
   serialNumber: { type: String },
   owner: { type: mongoose.Schema.Types.ObjectId, ref: 'FactoryMaster', required: true },
   truck: { type: mongoose.Schema.Types.ObjectId, ref: 'TruckMaster', required: true },
   deliveringCylinders: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CylinderMaster' }],
   sell: [ {type: mongoose.Schema.Types.ObjectId, ref: 'SellMaster' }],
   returnedCylinders: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CylinderMaster' }],
   customers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CustomerMaster' }],
   status: { type: String, enum: ['Loading', 'Delivery Started', 'Delivery Completed'], default:'Loading'}
}, { timestamps: true });

RoundMasterSchema.pre('save', async function(next) {
   

    if (this.isNew) {
           try {
               const date = new Date().toISOString().split('T')[0].replace(/-/g, '');
               const count = await mongoose.model('RoundMaster').countDocuments();
               console.log('Count:', count);
               this.serialNumber =  `R${date}${count + 1}`;
               console.log('Generated Serial Number:', this.serialNumber);
           } catch (err) {
               return next(err);
           }
       }
       next();
});

RoundMasterSchema.methods.sellCylinder = async function(cylinderId) {
    const cylinder = await mongoose.model('CylinderMaster').findById(cylinderId)
    if (!cylinder) throw new Error('Cylinder not found')
    if (cylinder.status !== 'Delivered') throw new Error('Cylinder is not delivered')



}

module.exports = mongoose.model('RoundMaster', RoundMasterSchema);