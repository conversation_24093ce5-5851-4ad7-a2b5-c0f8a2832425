{"name": "cylinder-api", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.21.1", "jsonwebtoken": "^9.0.2", "moment-timezone": "^0.5.46", "mongodb": "^6.12.0", "mongoose": "^8.8.3", "mongoose-sequence": "^6.0.1", "uuid": "^11.0.5"}, "devDependencies": {"nodemon": "^3.1.9"}}