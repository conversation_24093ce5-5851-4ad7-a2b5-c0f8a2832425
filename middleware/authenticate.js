const jwt = require('jsonwebtoken');
const StaffMaster = require('../models/StaffMaster');

module.exports = async (req, res, next) => {
    const token = req.headers['authorization'];
    if (!token) {
        return res.status(401).json({ message: 'Access denied. No token provided.' });
    }

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET).staff;
        console.log('Decoded token:', decoded);
        const user = await StaffMaster.findById(decoded.id);
        if (!user) {
            return res.status(401).json({ message: 'Invalid token.' });
        }
        req.user = user;
        next();
    } catch (error) {
        res.status(400).json({ message: 'Invalid token.' });
    }
};
