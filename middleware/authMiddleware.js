const jwt = require("jsonwebtoken");
const StaffMaster = require("../models/StaffMaster");

module.exports = (roles) => {
  return async (req, res, next) => {
    const token = req.headers["authorization"];
    if (!token) {
      console.log("No token provided");
      return res
        .status(401)
        .json({ message: "Access denied. No token provided." });
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET).staff;
      const staff = await StaffMaster.findOne({
        email: decoded.email,
        password: decoded.password,
      });
      if (!staff) {
        console.log("Staff not found");
        return res.status(404).json({ message: "Invalid user!" });
      }
      console.log(decoded._id);

      // Root role has access to everything
      if (staff.role === "root") {
        // Allow access for root users
      }
      // For non-root users, check if their role is in the allowed roles list
      else if (!roles.includes(staff.role) && decoded._id !== req.params.id) {
        console.log("Insufficient permissions");
        return res
          .status(403)
          .json({ message: "Access denied. Insufficient permissions." });
      }
      if (!staff.verified) {
        console.log("Unverified user");
        return res.status(403).json({ message: "Unverified!" });
      }
      if (staff.password !== decoded.password) {
        console.log("Password changed");
        return res.status(403).json({ message: "Password changed!" });
      }
      if (staff.banned) {
        console.log("Banned user");
        return res.status(403).json({ message: "Banned user!" });
      }
      req.user = decoded;

      next();
    } catch (error) {
      console.log("Invalid token");
      res.status(400).json({ message: "Invalid token." });
    }
  };
};
