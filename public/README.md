# Public Reports Interface

This directory contains a web-based interface for viewing cylinder management reports with login authentication.

## Features

- **Secure Login**: Staff members can log in using their existing credentials
- **Daily Overview Reports**: View delivery, return, and balance data across date ranges
- **Customer Reports**: Detailed breakdown by individual customers
- **Customer Type Reports**: Analysis by customer categories (Hospital, Individual, Shop, etc.)
- **Excel Export**: Download reports in Excel format
- **Responsive Design**: Works on desktop and mobile devices

## How to Access

1. Start the server: `npm start` or `npm run dev`
2. Open your browser and go to: `http://localhost:3000`
3. Login with your staff credentials
4. Select date range and view reports

## Available Reports

### 1. Daily Overview

- Shows daily delivery and return counts
- Displays balance in circulation and at factory
- Total cylinder counts

### 2. Daily by Customer

- Individual customer breakdown
- Separate rows for Delivered/Returned/Balance per customer
- Running balance calculations

### 3. Daily by Customer Type

- Grouped by customer types (Hospital, Individual, Shop, Factory, Workshop)
- Delivered/Returned/Balance sub-rows for each type
- Overall totals showing circulation and factory balances

### 4. Filling Process Overview

- Total filling processes and cylinders processed
- Success rates and rejection analysis
- Completed vs in-progress processes
- Daily filling trends and metrics

### 5. Filling by Line

- Performance analysis for each filling line
- Line utilization and efficiency metrics
- Success rates per line
- Line type categorization (Industrial, Medical, etc.)

### 6. Filling by Staff

- Individual staff performance tracking
- Process completion rates per staff member
- Cylinder handling efficiency
- Role-based performance analysis

## Export Functionality

Each report can be exported to Excel format with:

- Professional styling and formatting
- Proper headers and data organization
- Date range information included

## Authentication

- Uses existing staff authentication system
- Supports all user roles (root, admin, manager, sale, fill)
- 24-hour token expiry for security
- Automatic logout on token expiration

## File Structure

```
public/
├── index.html          # Main interface
├── css/
│   └── style.css      # Styling
├── js/
│   ├── auth.js        # Authentication logic
│   └── reports.js     # Report functionality
└── README.md          # This file
```

## API Endpoints

- `POST /public/login` - User authentication
- `GET /public/verify-token` - Token verification
- `GET /public/daily-overview` - Daily overview data
- `GET /public/daily-by-customer` - Customer breakdown data
- `GET /public/daily-by-customer-type` - Customer type breakdown data
- `GET /public/export/*` - Excel export endpoints

## Security Features

- JWT token-based authentication
- Token verification on each request
- Automatic session management
- Secure logout functionality
- Role-based access (all authenticated staff can view reports)

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- JavaScript required for functionality
