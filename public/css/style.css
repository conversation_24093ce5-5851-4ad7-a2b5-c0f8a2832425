/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    min-height: 100vh;
    position: relative;
}

/* Login Section */
.login-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.login-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header {
    margin-bottom: 30px;
}

.login-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.login-header h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.login-header p {
    color: #666;
    font-size: 1rem;
}

.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.form-group label i {
    margin-right: 8px;
    color: #667eea;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
}

.error-message {
    background: #fee;
    color: #c33;
    padding: 10px;
    border-radius: 5px;
    margin-top: 15px;
    border: 1px solid #fcc;
}

/* Dashboard Section */
.dashboard-section {
    background: #f8f9fa;
    min-height: 100vh;
}

.dashboard-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    color: #333;
    font-size: 1.5rem;
}

.header-left i {
    color: #667eea;
    margin-right: 10px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    color: #666;
    font-weight: 500;
}

.logout-btn {
    padding: 8px 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background: #c82333;
}

/* Dashboard Main */
.dashboard-main {
    padding: 30px;
}

.report-controls {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.date-controls {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.date-controls .form-group {
    margin-bottom: 0;
}

.date-controls .form-group label {
    font-size: 0.9rem;
    color: #555;
}

.date-controls .form-group input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

.load-btn {
    padding: 10px 20px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.load-btn:hover {
    background: #218838;
}

/* Reports Container */
.reports-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.report-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #333;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

.tab-btn i {
    margin-right: 8px;
}

.tab-content {
    padding: 0;
}

.tab-pane {
    display: none;
    padding: 30px;
}

.tab-pane.active {
    display: block;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.report-header h2 {
    color: #333;
    font-size: 1.3rem;
}

.export-btn {
    padding: 8px 16px;
    background: #17a2b8;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.export-btn:hover {
    background: #138496;
}

.report-content {
    min-height: 300px;
}

.no-data {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 50px 20px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 15px;
}

.loading-spinner p {
    color: #666;
    font-weight: 500;
}

/* Report Tables */
.report-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    font-size: 0.9rem;
}

.report-table th,
.report-table td {
    padding: 8px 12px;
    text-align: center;
    border: 1px solid #ddd;
}

.report-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.report-table tbody tr:nth-child(even) {
    background: #f9f9f9;
}

.report-table tbody tr:hover {
    background: #f0f8ff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .date-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .report-tabs {
        flex-direction: column;
    }
    
    .report-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .dashboard-main {
        padding: 15px;
    }
}
