<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cylinder Management - Cylinder System</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cylinder-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .cylinder-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .cylinder-controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .controls-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .cylinder-table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .cylinder-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .cylinder-table th,
        .cylinder-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .cylinder-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .cylinder-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-full { background: #d4edda; color: #155724; }
        .status-empty { background: #f8d7da; color: #721c24; }
        .status-available { background: #d1ecf1; color: #0c5460; }
        .status-delivered { background: #fff3cd; color: #856404; }
        .status-filling { background: #cce5ff; color: #004085; }
        .status-error { background: #f5c6cb; color: #721c24; }
        
        .size-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            background: #e9ecef;
            color: #495057;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-view { background: #17a2b8; color: white; }
        .btn-edit { background: #28a745; color: white; }
        .btn-delete { background: #dc3545; color: white; }
        
        .btn-small:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: white;
            border-radius: 0 0 10px 10px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover:not(:disabled) {
            background: #f8f9fa;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .pagination .current-page {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            min-width: 120px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 768px) {
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-group,
            .filter-group {
                flex-direction: column;
                width: 100%;
            }
            
            .cylinder-table {
                font-size: 0.9rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="cylinder-container">
        <div class="cylinder-header">
            <div>
                <h1><i class="fas fa-gas-pump"></i> Cylinder Management</h1>
                <p>View, edit, and manage all cylinders</p>
            </div>
            <div>
                <span id="userInfo" class="user-info"></span>
                <button id="backToReports" class="btn btn-primary">
                    <i class="fas fa-chart-line"></i>
                    Back to Reports
                </button>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="stats-row" id="statsRow">
            <!-- Stats will be populated by JavaScript -->
        </div>

        <!-- Controls -->
        <div class="cylinder-controls">
            <div class="controls-row">
                <div class="search-group">
                    <input type="text" id="searchInput" placeholder="Search by serial number, original number..." style="width: 300px;">
                    <button id="searchBtn" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        Search
                    </button>
                    <button id="clearSearch" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Clear
                    </button>
                </div>
                
                <div class="filter-group">
                    <select id="statusFilter">
                        <option value="">All Status</option>
                        <option value="Full">Full</option>
                        <option value="Empty">Empty</option>
                        <option value="Available">Available</option>
                        <option value="Delivered">Delivered</option>
                        <option value="Filling">Filling</option>
                        <option value="In Transit">In Transit</option>
                        <option value="Return">Return</option>
                    </select>
                    
                    <select id="sizeFilter">
                        <option value="">All Sizes</option>
                        <option value="47L">47L</option>
                        <option value="40L">40L</option>
                        <option value="15L">15L</option>
                        <option value="10L">10L</option>
                    </select>
                    
                    <select id="formatFilter">
                        <option value="">All Formats</option>
                        <option value="new">New Format (A0001)</option>
                        <option value="old">Old Format (CYD...)</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <button id="loadCylinders" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        Load Cylinders
                    </button>
                    
                    <button id="addCylinder" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add Cylinder
                    </button>
                </div>
            </div>
        </div>

        <!-- Alert Container -->
        <div id="alertContainer"></div>

        <!-- Cylinder Table -->
        <div class="cylinder-table-container">
            <table class="cylinder-table">
                <thead>
                    <tr>
                        <th>Serial Number</th>
                        <th>Original Number</th>
                        <th>Size</th>
                        <th>Status</th>
                        <th>Value Type</th>
                        <th>Working Pressure</th>
                        <th>Production Date</th>
                        <th>Import Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="cylinderTableBody">
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 40px;">
                            Click "Load Cylinders" to view data
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <div class="pagination" id="pagination">
                <button id="prevPage" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <span id="pageInfo">Page 1 of 1</span>
                <button id="nextPage" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- View Cylinder Modal -->
    <div id="viewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-eye"></i> View Cylinder Details</h2>
                <span class="close" data-modal="viewModal">&times;</span>
            </div>
            <div id="viewContent">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Edit Cylinder Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Edit Cylinder</h2>
                <span class="close" data-modal="editModal">&times;</span>
            </div>
            <div id="editAlert"></div>
            <form id="editForm" class="form-grid">
                <div class="form-group">
                    <label for="editSerialNumber">Serial Number</label>
                    <input type="text" id="editSerialNumber" readonly>
                </div>
                
                <div class="form-group">
                    <label for="editOriginalNumber">Original Number *</label>
                    <input type="text" id="editOriginalNumber" required>
                </div>
                
                <div class="form-group">
                    <label for="editCylinderSize">Cylinder Size *</label>
                    <select id="editCylinderSize" required>
                        <option value="">Select Size</option>
                        <option value="47L">47L</option>
                        <option value="40L">40L</option>
                        <option value="15L">15L</option>
                        <option value="10L">10L</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="editStatus">Status *</label>
                    <select id="editStatus" required>
                        <option value="">Select Status</option>
                        <option value="Full">Full</option>
                        <option value="Empty">Empty</option>
                        <option value="Available">Available</option>
                        <option value="Delivered">Delivered</option>
                        <option value="Filling">Filling</option>
                        <option value="In Transit">In Transit</option>
                        <option value="Return">Return</option>
                        <option value="Taken">Taken</option>
                        <option value="Leakage Error">Leakage Error</option>
                        <option value="Valve Error">Valve Error</option>
                        <option value="Key Error">Key Error</option>
                        <option value="Damaged Error">Damaged Error</option>
                        <option value="Other Error">Other Error</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="editValueType">Value Type *</label>
                    <select id="editValueType" required>
                        <option value="">Select Type</option>
                        <option value="China">China</option>
                        <option value="Japan">Japan</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="editWorkingPressure">Working Pressure *</label>
                    <input type="number" id="editWorkingPressure" required>
                </div>
                
                <div class="form-group">
                    <label for="editProductionDate">Production Date *</label>
                    <input type="date" id="editProductionDate" required>
                </div>
                
                <div class="form-group">
                    <label for="editImportDate">Import Date *</label>
                    <input type="date" id="editImportDate" required>
                </div>
                
                <div class="form-group" style="grid-column: 1 / -1;">
                    <label for="editRemarks">Remarks</label>
                    <input type="text" id="editRemarks" placeholder="Optional remarks">
                </div>
            </form>
            
            <div style="margin-top: 20px; text-align: right;">
                <button type="button" class="btn btn-secondary" data-modal="editModal">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button type="submit" form="editForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Add Cylinder Modal -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-plus"></i> Add New Cylinder</h2>
                <span class="close" data-modal="addModal">&times;</span>
            </div>
            <div id="addAlert"></div>
            <form id="addForm" class="form-grid">
                <div class="form-group">
                    <label for="addOriginalNumber">Original Number *</label>
                    <input type="text" id="addOriginalNumber" required>
                </div>
                
                <div class="form-group">
                    <label for="addCylinderSize">Cylinder Size *</label>
                    <select id="addCylinderSize" required>
                        <option value="">Select Size</option>
                        <option value="47L">47L</option>
                        <option value="40L">40L</option>
                        <option value="15L">15L</option>
                        <option value="10L">10L</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="addStatus">Status *</label>
                    <select id="addStatus" required>
                        <option value="">Select Status</option>
                        <option value="Empty" selected>Empty</option>
                        <option value="Full">Full</option>
                        <option value="Available">Available</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="addValueType">Value Type *</label>
                    <select id="addValueType" required>
                        <option value="">Select Type</option>
                        <option value="Japan" selected>Japan</option>
                        <option value="China">China</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="addWorkingPressure">Working Pressure *</label>
                    <input type="number" id="addWorkingPressure" value="1000" required>
                </div>
                
                <div class="form-group">
                    <label for="addProductionDate">Production Date *</label>
                    <input type="date" id="addProductionDate" required>
                </div>
                
                <div class="form-group">
                    <label for="addImportDate">Import Date *</label>
                    <input type="date" id="addImportDate" required>
                </div>
                
                <div class="form-group">
                    <label for="addRemarks">Remarks</label>
                    <input type="text" id="addRemarks" placeholder="Optional remarks">
                </div>
            </form>
            
            <div style="margin-top: 20px; text-align: right;">
                <button type="button" class="btn btn-secondary" data-modal="addModal">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button type="submit" form="addForm" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Add Cylinder
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/cylinders.js"></script>
</body>
</html>
