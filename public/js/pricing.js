// Price Management JavaScript
class PricingManager {
  constructor() {
    this.currentUser = null;
    this.priceLists = [];
    this.customers = [];
    this.currentPriceList = null;
    this.currentCustomer = null;
    this.priceEntries = [];
    this.init();
  }

  init() {
    // Check authentication
    if (!window.authManager || !window.authManager.isAuthenticated()) {
      window.location.href = "/";
      return;
    }

    this.currentUser = window.authManager.getCurrentUser();

    // Check if user has appropriate privileges
    if (!["root", "admin", "manager"].includes(this.currentUser.role)) {
      alert("Access denied. Price management requires admin privileges.");
      window.location.href = "/";
      return;
    }

    this.setupEventListeners();
    this.updateUI();
  }

  setupEventListeners() {
    // Navigation
    document.getElementById("backToReports").addEventListener("click", () => {
      window.location.href = "/";
    });

    // Tab switching
    document.querySelectorAll(".pricing-tab").forEach((tab) => {
      tab.addEventListener("click", (e) =>
        this.switchTab(e.target.dataset.tab)
      );
    });

    // Price Lists Tab
    document.getElementById("loadPriceLists").addEventListener("click", () => {
      this.loadPriceLists();
    });

    document.getElementById("addPriceList").addEventListener("click", () => {
      this.showPriceListModal();
    });

    document.getElementById("priceListSearch").addEventListener("input", () => {
      this.filterPriceLists();
    });

    // Customer Pricing Tab
    document.getElementById("loadCustomers").addEventListener("click", () => {
      this.loadCustomers();
    });

    document.getElementById("customerSearch").addEventListener("input", () => {
      this.filterCustomers();
    });

    document
      .getElementById("customerTypeFilter")
      .addEventListener("change", () => {
        this.filterCustomers();
      });

    // Bulk Operations Tab
    document
      .getElementById("previewBulkOperation")
      .addEventListener("click", () => {
        this.previewBulkOperation();
      });

    document
      .getElementById("executeBulkOperation")
      .addEventListener("click", () => {
        this.executeBulkOperation();
      });

    // Price Analysis Tab
    document
      .getElementById("generateAnalysis")
      .addEventListener("click", () => {
        this.generateAnalysis();
      });

    document.getElementById("exportAnalysis").addEventListener("click", () => {
      this.exportAnalysis();
    });

    // Modal handlers
    document.getElementById("priceListForm").addEventListener("submit", (e) => {
      e.preventDefault();
      this.savePriceList();
    });

    document.getElementById("addPriceEntry").addEventListener("click", () => {
      this.addPriceEntry();
    });

    // Modal close handlers
    document.querySelectorAll(".close, [data-modal]").forEach((element) => {
      element.addEventListener("click", (e) => {
        const modalId =
          e.target.getAttribute("data-modal") || e.target.closest(".modal").id;
        if (modalId) {
          this.closeModal(modalId);
        }
      });
    });

    // Close modal when clicking outside
    window.addEventListener("click", (e) => {
      if (e.target.classList.contains("modal")) {
        this.closeModal(e.target.id);
      }
    });
  }

  updateUI() {
    // Update user info
    const userInfo = document.getElementById("userInfo");
    if (userInfo && this.currentUser) {
      userInfo.textContent = `${this.currentUser.name} (${this.currentUser.role})`;
    }
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll(".pricing-tab").forEach((tab) => {
      tab.classList.remove("active");
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add("active");

    // Update tab content
    document.querySelectorAll(".tab-content").forEach((content) => {
      content.classList.add("hidden");
    });

    const tabMap = {
      "price-lists": "priceListsTab",
      "customer-pricing": "customerPricingTab",
      "bulk-operations": "bulkOperationsTab",
      "price-analysis": "priceAnalysisTab",
    };

    document.getElementById(tabMap[tabName]).classList.remove("hidden");
  }

  async loadPriceLists() {
    try {
      this.showLoading(true);
      this.clearAlert();

      const response = await fetch("/public/prices", {
        headers: window.authManager.getAuthHeader(),
      });

      const result = await response.json();

      if (response.ok) {
        this.priceLists = Array.isArray(result) ? result : [];
        this.renderPriceLists();
        this.renderStats();
        this.showAlert("Price lists loaded successfully!", "success");
      } else {
        this.showAlert(result.message || "Failed to load price lists", "error");
      }
    } catch (error) {
      console.error("Load price lists error:", error);
      this.showAlert("Network error. Please try again.", "error");
    } finally {
      this.showLoading(false);
    }
  }

  renderPriceLists() {
    const container = document.getElementById("priceListsContainer");

    if (!this.priceLists || this.priceLists.length === 0) {
      container.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                    No price lists found. Click "Add Price List" to create one.
                </div>
            `;
      return;
    }

    container.innerHTML = this.priceLists
      .map(
        (priceList) => `
            <div class="price-list-card">
                <div class="price-list-header">
                    <div class="price-list-name">${priceList.priceName}</div>
                    <div class="price-list-actions">
                        <button class="btn-small btn-view" onclick="pricingManager.viewPriceList('${
                          priceList._id
                        }')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-small btn-edit" onclick="pricingManager.editPriceList('${
                          priceList._id
                        }')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-small btn-copy" onclick="pricingManager.copyPriceList('${
                          priceList._id
                        }')">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-small btn-delete" onclick="pricingManager.deletePriceList('${
                          priceList._id
                        }')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="price-entries">
                    ${
                      priceList.prices && priceList.prices.length > 0
                        ? priceList.prices
                            .map(
                              (price) => `
                            <div class="price-entry">
                                <div class="price-specs">
                                    <span class="spec-badge spec-pressure">${price.pressure} PSI</span>
                                    <span class="spec-badge spec-size">${price.size}L</span>
                                </div>
                                <div class="price-values">
                                    <div class="customer-price">$${price.price}</div>
                                    <div class="factory-price">Factory: $${price.factoryPrice}</div>
                                </div>
                            </div>
                        `
                            )
                            .join("")
                        : '<div style="text-align: center; color: #666; padding: 20px;">No price entries</div>'
                    }
                </div>
            </div>
        `
      )
      .join("");
  }

  filterPriceLists() {
    const searchTerm = document
      .getElementById("priceListSearch")
      .value.toLowerCase();
    const cards = document.querySelectorAll(".price-list-card");

    cards.forEach((card) => {
      const name = card
        .querySelector(".price-list-name")
        .textContent.toLowerCase();
      if (name.includes(searchTerm)) {
        card.style.display = "block";
      } else {
        card.style.display = "none";
      }
    });
  }

  async loadCustomers() {
    try {
      this.showLoading(true);
      this.clearAlert("customerAlertContainer");

      const response = await fetch("/public/customers", {
        headers: window.authManager.getAuthHeader(),
      });

      const result = await response.json();

      if (response.ok) {
        this.customers = Array.isArray(result) ? result : [];
        this.renderCustomers();
        this.showAlert(
          "Customers loaded successfully!",
          "success",
          "customerAlertContainer"
        );
      } else {
        this.showAlert(
          result.message || "Failed to load customers",
          "error",
          "customerAlertContainer"
        );
      }
    } catch (error) {
      console.error("Load customers error:", error);
      this.showAlert(
        "Network error. Please try again.",
        "error",
        "customerAlertContainer"
      );
    } finally {
      this.showLoading(false);
    }
  }

  renderCustomers() {
    const container = document.getElementById("customersContainer");

    if (!this.customers || this.customers.length === 0) {
      container.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                    No customers found.
                </div>
            `;
      return;
    }

    container.innerHTML = this.customers
      .map(
        (customer) => `
            <div class="customer-card" data-customer-type="${
              customer.customerType
            }">
                <div class="customer-header">
                    <div class="customer-name">${customer.customerName}</div>
                    <div class="customer-type type-${(
                      customer.customerType || ""
                    ).toLowerCase()}">
                        ${customer.customerType || "Unknown"}
                    </div>
                </div>
                <div style="margin-bottom: 10px;">
                    <small style="color: #666;">
                        Price List: ${
                          customer.priceList?.priceName || "Default"
                        }
                    </small>
                </div>
                <div style="margin-bottom: 10px;">
                    <small style="color: #666;">
                        Custom Prices: ${customer.prices?.length || 0}
                    </small>
                </div>
                <div style="display: flex; gap: 5px;">
                    <button class="btn-small btn-view" onclick="pricingManager.viewCustomerPricing('${
                      customer._id
                    }')">
                        <i class="fas fa-eye"></i>
                        View
                    </button>
                    <button class="btn-small btn-edit" onclick="pricingManager.editCustomerPricing('${
                      customer._id
                    }')">
                        <i class="fas fa-edit"></i>
                        Edit
                    </button>
                </div>
            </div>
        `
      )
      .join("");
  }

  filterCustomers() {
    const searchTerm = document
      .getElementById("customerSearch")
      .value.toLowerCase();
    const typeFilter = document.getElementById("customerTypeFilter").value;
    const cards = document.querySelectorAll(".customer-card");

    cards.forEach((card) => {
      const name = card
        .querySelector(".customer-name")
        .textContent.toLowerCase();
      const type = card.dataset.customerType;

      const matchesSearch = name.includes(searchTerm);
      const matchesType = !typeFilter || type === typeFilter;

      if (matchesSearch && matchesType) {
        card.style.display = "block";
      } else {
        card.style.display = "none";
      }
    });
  }

  renderStats() {
    const statsRow = document.getElementById("statsRow");

    if (!this.priceLists || this.priceLists.length === 0) {
      statsRow.innerHTML = "";
      return;
    }

    const stats = this.calculateStats();

    statsRow.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${stats.totalPriceLists}</div>
                <div class="stat-label">Price Lists</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalPriceEntries}</div>
                <div class="stat-label">Price Entries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgCustomerPrice}</div>
                <div class="stat-label">Avg Customer Price</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgFactoryPrice}</div>
                <div class="stat-label">Avg Factory Price</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgMargin}%</div>
                <div class="stat-label">Avg Margin</div>
            </div>
        `;
  }

  calculateStats() {
    let totalPriceEntries = 0;
    let totalCustomerPrice = 0;
    let totalFactoryPrice = 0;

    this.priceLists.forEach((priceList) => {
      if (priceList.prices) {
        totalPriceEntries += priceList.prices.length;
        priceList.prices.forEach((price) => {
          totalCustomerPrice += price.price;
          totalFactoryPrice += price.factoryPrice;
        });
      }
    });

    const avgCustomerPrice =
      totalPriceEntries > 0
        ? (totalCustomerPrice / totalPriceEntries).toFixed(2)
        : 0;
    const avgFactoryPrice =
      totalPriceEntries > 0
        ? (totalFactoryPrice / totalPriceEntries).toFixed(2)
        : 0;
    const avgMargin =
      totalFactoryPrice > 0
        ? (
            ((totalCustomerPrice - totalFactoryPrice) / totalFactoryPrice) *
            100
          ).toFixed(1)
        : 0;

    return {
      totalPriceLists: this.priceLists.length,
      totalPriceEntries,
      avgCustomerPrice: `$${avgCustomerPrice}`,
      avgFactoryPrice: `$${avgFactoryPrice}`,
      avgMargin,
    };
  }

  showPriceListModal(priceList = null) {
    this.currentPriceList = priceList;
    this.priceEntries = priceList ? [...priceList.prices] : [];

    const modal = document.getElementById("priceListModal");
    const title = document.getElementById("priceListModalTitle");
    const nameInput = document.getElementById("priceListName");

    if (priceList) {
      title.innerHTML = '<i class="fas fa-edit"></i> Edit Price List';
      nameInput.value = priceList.priceName;
    } else {
      title.innerHTML = '<i class="fas fa-plus"></i> Add Price List';
      nameInput.value = "";
    }

    this.renderPriceEntriesTable();
    this.showModal("priceListModal");
  }

  renderPriceEntriesTable() {
    const tbody = document.getElementById("priceEntriesTable");

    if (this.priceEntries.length === 0) {
      tbody.innerHTML = `
                <tr>
                    <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                        No price entries. Click "Add Price Entry" to add one.
                    </td>
                </tr>
            `;
      return;
    }

    tbody.innerHTML = this.priceEntries
      .map(
        (entry, index) => `
            <tr>
                <td>
                    <input type="number" class="editable-price" value="${entry.pressure}"
                           onchange="pricingManager.updatePriceEntry(${index}, 'pressure', this.value)">
                </td>
                <td>
                    <input type="number" class="editable-price" value="${entry.size}"
                           onchange="pricingManager.updatePriceEntry(${index}, 'size', this.value)">
                </td>
                <td>
                    <input type="number" class="editable-price" value="${entry.price}" step="0.01"
                           onchange="pricingManager.updatePriceEntry(${index}, 'price', this.value)">
                </td>
                <td>
                    <input type="number" class="editable-price" value="${entry.factoryPrice}" step="0.01"
                           onchange="pricingManager.updatePriceEntry(${index}, 'factoryPrice', this.value)">
                </td>
                <td>
                    <button class="btn-small btn-delete" onclick="pricingManager.removePriceEntry(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `
      )
      .join("");
  }

  addPriceEntry() {
    this.priceEntries.push({
      pressure: 1000,
      size: 47,
      price: 0,
      factoryPrice: 0,
    });
    this.renderPriceEntriesTable();
  }

  updatePriceEntry(index, field, value) {
    if (this.priceEntries[index]) {
      this.priceEntries[index][field] = parseFloat(value) || 0;
    }
  }

  removePriceEntry(index) {
    this.priceEntries.splice(index, 1);
    this.renderPriceEntriesTable();
  }

  async savePriceList() {
    try {
      this.showLoading(true);
      this.clearAlert("priceListAlert");

      const formData = new FormData(document.getElementById("priceListForm"));
      const priceListData = {
        priceName: formData.get("priceName"),
        prices: this.priceEntries,
      };

      const url = this.currentPriceList
        ? `/public/prices/${this.currentPriceList._id}`
        : "/public/prices";

      const method = this.currentPriceList ? "PUT" : "POST";

      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
          ...window.authManager.getAuthHeader(),
        },
        body: JSON.stringify(priceListData),
      });

      const result = await response.json();

      if (response.ok) {
        this.showAlert(
          "Price list saved successfully!",
          "success",
          "priceListAlert"
        );
        setTimeout(() => {
          this.closeModal("priceListModal");
          this.loadPriceLists();
        }, 1500);
      } else {
        this.showAlert(
          result.message || "Failed to save price list",
          "error",
          "priceListAlert"
        );
      }
    } catch (error) {
      console.error("Save price list error:", error);
      this.showAlert(
        "Network error. Please try again.",
        "error",
        "priceListAlert"
      );
    } finally {
      this.showLoading(false);
    }
  }

  showModal(modalId) {
    document.getElementById(modalId).style.display = "block";
  }

  closeModal(modalId) {
    document.getElementById(modalId).style.display = "none";
    this.clearAlert("priceListAlert");
    this.clearAlert("customerPricingAlert");
  }

  showAlert(message, type = "success", containerId = "alertContainer") {
    const container = document.getElementById(containerId);
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;

    // Auto-hide after 5 seconds
    setTimeout(() => {
      container.innerHTML = "";
    }, 5000);
  }

  clearAlert(containerId = "alertContainer") {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = "";
    }
  }

  async viewPriceList(priceListId) {
    const priceList = this.priceLists.find((p) => p._id === priceListId);
    if (priceList) {
      this.showPriceListModal(priceList);
    }
  }

  async editPriceList(priceListId) {
    const priceList = this.priceLists.find((p) => p._id === priceListId);
    if (priceList) {
      this.showPriceListModal(priceList);
    }
  }

  async copyPriceList(priceListId) {
    const priceList = this.priceLists.find((p) => p._id === priceListId);
    if (priceList) {
      const newPriceList = {
        ...priceList,
        priceName: `${priceList.priceName} (Copy)`,
        _id: undefined,
      };
      this.showPriceListModal(newPriceList);
    }
  }

  async deletePriceList(priceListId) {
    if (
      !confirm(
        "Are you sure you want to delete this price list? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      this.showLoading(true);

      const response = await fetch(`/public/prices/${priceListId}`, {
        method: "DELETE",
        headers: window.authManager.getAuthHeader(),
      });

      const result = await response.json();

      if (response.ok) {
        this.showAlert("Price list deleted successfully!", "success");
        this.loadPriceLists();
      } else {
        this.showAlert(
          result.message || "Failed to delete price list",
          "error"
        );
      }
    } catch (error) {
      console.error("Delete price list error:", error);
      this.showAlert("Network error. Please try again.", "error");
    } finally {
      this.showLoading(false);
    }
  }

  async viewCustomerPricing(customerId) {
    const customer = this.customers.find((c) => c._id === customerId);
    if (!customer) return;

    const content = document.getElementById("customerPricingContent");

    content.innerHTML = `
            <div style="margin-bottom: 20px;">
                <h3>${customer.customerName}</h3>
                <p><strong>Type:</strong> ${customer.customerType}</p>
                <p><strong>Default Price List:</strong> ${
                  customer.priceList?.priceName || "None"
                }</p>
            </div>

            <h4>Custom Prices</h4>
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Pressure (PSI)</th>
                        <th>Size (L)</th>
                        <th>Custom Price</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${
                      customer.prices && customer.prices.length > 0
                        ? customer.prices
                            .map(
                              (price) => `
                            <tr>
                                <td>${price.pressure}</td>
                                <td>${price.size}</td>
                                <td>$${price.price}</td>
                                <td>
                                    <button class="btn-small btn-delete" onclick="pricingManager.removeCustomerPrice('${customerId}', '${price._id}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `
                            )
                            .join("")
                        : '<tr><td colspan="4" style="text-align: center; padding: 20px;">No custom prices</td></tr>'
                    }
                </tbody>
            </table>

            <div style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="pricingManager.addCustomerPrice('${customerId}')">
                    <i class="fas fa-plus"></i>
                    Add Custom Price
                </button>
            </div>
        `;

    this.showModal("customerPricingModal");
  }

  async editCustomerPricing(customerId) {
    this.viewCustomerPricing(customerId);
  }

  async addCustomerPrice(customerId) {
    const pressure = prompt("Enter pressure (PSI):");
    const size = prompt("Enter size (L):");
    const price = prompt("Enter price:");

    if (!pressure || !size || !price) return;

    try {
      this.showLoading(true);

      const response = await fetch(`/public/customers/${customerId}/price`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...window.authManager.getAuthHeader(),
        },
        body: JSON.stringify({
          pressure: parseInt(pressure),
          size: parseInt(size),
          price: parseFloat(price),
        }),
      });

      const result = await response.json();

      if (response.ok) {
        this.showAlert(
          "Custom price added successfully!",
          "success",
          "customerPricingAlert"
        );
        this.loadCustomers();
        this.viewCustomerPricing(customerId);
      } else {
        this.showAlert(
          result.message || "Failed to add custom price",
          "error",
          "customerPricingAlert"
        );
      }
    } catch (error) {
      console.error("Add customer price error:", error);
      this.showAlert(
        "Network error. Please try again.",
        "error",
        "customerPricingAlert"
      );
    } finally {
      this.showLoading(false);
    }
  }

  async removeCustomerPrice(customerId, priceId) {
    if (!confirm("Are you sure you want to remove this custom price?")) {
      return;
    }

    try {
      this.showLoading(true);

      const response = await fetch(
        `/public/customers/${customerId}/price/${priceId}`,
        {
          method: "DELETE",
          headers: window.authManager.getAuthHeader(),
        }
      );

      const result = await response.json();

      if (response.ok) {
        this.showAlert(
          "Custom price removed successfully!",
          "success",
          "customerPricingAlert"
        );
        this.loadCustomers();
        this.viewCustomerPricing(customerId);
      } else {
        this.showAlert(
          result.message || "Failed to remove custom price",
          "error",
          "customerPricingAlert"
        );
      }
    } catch (error) {
      console.error("Remove customer price error:", error);
      this.showAlert(
        "Network error. Please try again.",
        "error",
        "customerPricingAlert"
      );
    } finally {
      this.showLoading(false);
    }
  }

  async previewBulkOperation() {
    const operationType = document.getElementById("bulkOperationType").value;
    const target = document.getElementById("bulkTarget").value;
    const value = parseFloat(document.getElementById("bulkValue").value);
    const sizeFilter = document.getElementById("bulkSizeFilter").value;

    if (!operationType || !target || !value) {
      this.showAlert(
        "Please fill all required fields",
        "error",
        "bulkAlertContainer"
      );
      return;
    }

    const preview = this.calculateBulkChanges(
      operationType,
      target,
      value,
      sizeFilter
    );
    this.renderBulkPreview(preview);
  }

  calculateBulkChanges(operationType, target, value, sizeFilter) {
    const changes = [];

    this.priceLists.forEach((priceList) => {
      if (target === "all" || target === "specific") {
        priceList.prices.forEach((price) => {
          if (!sizeFilter || price.size == sizeFilter) {
            const oldPrice = price.price;
            let newPrice = oldPrice;

            switch (operationType) {
              case "increase":
                newPrice = oldPrice * (1 + value / 100);
                break;
              case "decrease":
                newPrice = oldPrice * (1 - value / 100);
                break;
              case "set":
                newPrice = value;
                break;
            }

            changes.push({
              priceListName: priceList.priceName,
              pressure: price.pressure,
              size: price.size,
              oldPrice: oldPrice,
              newPrice: newPrice,
              change: newPrice - oldPrice,
            });
          }
        });
      }
    });

    return changes;
  }

  renderBulkPreview(changes) {
    const container = document.getElementById("bulkPreviewContainer");

    if (changes.length === 0) {
      container.innerHTML =
        "<p>No changes would be made with current settings.</p>";
      container.classList.remove("hidden");
      return;
    }

    container.innerHTML = `
            <h4>Preview of Changes (${changes.length} entries affected)</h4>
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Price List</th>
                        <th>Pressure</th>
                        <th>Size</th>
                        <th>Current Price</th>
                        <th>New Price</th>
                        <th>Change</th>
                    </tr>
                </thead>
                <tbody>
                    ${changes
                      .slice(0, 10)
                      .map(
                        (change) => `
                        <tr>
                            <td>${change.priceListName}</td>
                            <td>${change.pressure} PSI</td>
                            <td>${change.size}L</td>
                            <td>$${change.oldPrice.toFixed(2)}</td>
                            <td>$${change.newPrice.toFixed(2)}</td>
                            <td style="color: ${
                              change.change >= 0 ? "green" : "red"
                            }">
                                ${
                                  change.change >= 0 ? "+" : ""
                                }$${change.change.toFixed(2)}
                            </td>
                        </tr>
                    `
                      )
                      .join("")}
                    ${
                      changes.length > 10
                        ? `
                        <tr>
                            <td colspan="6" style="text-align: center; font-style: italic;">
                                ... and ${changes.length - 10} more entries
                            </td>
                        </tr>
                    `
                        : ""
                    }
                </tbody>
            </table>
        `;

    container.classList.remove("hidden");
  }

  async executeBulkOperation() {
    if (
      !confirm(
        "Are you sure you want to execute this bulk operation? This will affect multiple price entries."
      )
    ) {
      return;
    }

    const operationType = document.getElementById("bulkOperationType").value;
    const target = document.getElementById("bulkTarget").value;
    const value = parseFloat(document.getElementById("bulkValue").value);
    const sizeFilter = document.getElementById("bulkSizeFilter").value;

    try {
      this.showLoading(true);

      // This would need a dedicated bulk update API endpoint
      const response = await fetch("/api/prices/bulk-update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...window.authManager.getAuthHeader(),
        },
        body: JSON.stringify({
          operationType,
          target,
          value,
          sizeFilter,
        }),
      });

      if (response.ok) {
        this.showAlert(
          "Bulk operation completed successfully!",
          "success",
          "bulkAlertContainer"
        );
        this.loadPriceLists();
        document.getElementById("bulkPreviewContainer").classList.add("hidden");
      } else {
        this.showAlert(
          "Bulk operation failed. Please try again.",
          "error",
          "bulkAlertContainer"
        );
      }
    } catch (error) {
      console.error("Bulk operation error:", error);
      this.showAlert(
        "Network error. Please try again.",
        "error",
        "bulkAlertContainer"
      );
    } finally {
      this.showLoading(false);
    }
  }

  async generateAnalysis() {
    const analysisType = document.getElementById("analysisType").value;

    try {
      this.showLoading(true);
      this.clearAlert("analysisAlertContainer");

      let analysisData;

      switch (analysisType) {
        case "comparison":
          analysisData = this.generatePriceComparison();
          break;
        case "trends":
          analysisData = this.generatePriceTrends();
          break;
        case "margins":
          analysisData = this.generateMarginAnalysis();
          break;
        case "customer-analysis":
          analysisData = this.generateCustomerAnalysis();
          break;
        default:
          throw new Error("Invalid analysis type");
      }

      this.renderAnalysis(analysisType, analysisData);
      this.showAlert(
        "Analysis generated successfully!",
        "success",
        "analysisAlertContainer"
      );
    } catch (error) {
      console.error("Generate analysis error:", error);
      this.showAlert(
        "Failed to generate analysis",
        "error",
        "analysisAlertContainer"
      );
    } finally {
      this.showLoading(false);
    }
  }

  generatePriceComparison() {
    const comparison = {};

    this.priceLists.forEach((priceList) => {
      priceList.prices.forEach((price) => {
        const key = `${price.pressure}PSI-${price.size}L`;
        if (!comparison[key]) {
          comparison[key] = {
            pressure: price.pressure,
            size: price.size,
            prices: [],
          };
        }
        comparison[key].prices.push({
          priceListName: priceList.priceName,
          price: price.price,
          factoryPrice: price.factoryPrice,
        });
      });
    });

    return comparison;
  }

  generateMarginAnalysis() {
    const margins = [];

    this.priceLists.forEach((priceList) => {
      priceList.prices.forEach((price) => {
        const margin =
          ((price.price - price.factoryPrice) / price.factoryPrice) * 100;
        margins.push({
          priceListName: priceList.priceName,
          pressure: price.pressure,
          size: price.size,
          customerPrice: price.price,
          factoryPrice: price.factoryPrice,
          margin: margin,
        });
      });
    });

    return margins.sort((a, b) => b.margin - a.margin);
  }

  renderAnalysis(type, data) {
    const container = document.getElementById("analysisContainer");

    switch (type) {
      case "comparison":
        this.renderPriceComparisonAnalysis(container, data);
        break;
      case "margins":
        this.renderMarginAnalysis(container, data);
        break;
      default:
        container.innerHTML = "<p>Analysis type not implemented yet.</p>";
    }
  }

  renderPriceComparisonAnalysis(container, data) {
    const entries = Object.entries(data);

    container.innerHTML = `
            <h3>Price Comparison Analysis</h3>
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Specification</th>
                        <th>Price List</th>
                        <th>Customer Price</th>
                        <th>Factory Price</th>
                        <th>Margin</th>
                    </tr>
                </thead>
                <tbody>
                    ${entries
                      .map(([key, spec]) =>
                        spec.prices
                          .map(
                            (priceData, index) => `
                            <tr>
                                ${
                                  index === 0
                                    ? `<td rowspan="${spec.prices.length}">${spec.pressure} PSI - ${spec.size}L</td>`
                                    : ""
                                }
                                <td>${priceData.priceListName}</td>
                                <td>$${priceData.price.toFixed(2)}</td>
                                <td>$${priceData.factoryPrice.toFixed(2)}</td>
                                <td>${(
                                  ((priceData.price - priceData.factoryPrice) /
                                    priceData.factoryPrice) *
                                  100
                                ).toFixed(1)}%</td>
                            </tr>
                        `
                          )
                          .join("")
                      )
                      .join("")}
                </tbody>
            </table>
        `;
  }

  renderMarginAnalysis(container, data) {
    container.innerHTML = `
            <h3>Profit Margin Analysis</h3>
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Price List</th>
                        <th>Specification</th>
                        <th>Customer Price</th>
                        <th>Factory Price</th>
                        <th>Margin %</th>
                        <th>Profit</th>
                    </tr>
                </thead>
                <tbody>
                    ${data
                      .slice(0, 20)
                      .map(
                        (item) => `
                        <tr>
                            <td>${item.priceListName}</td>
                            <td>${item.pressure} PSI - ${item.size}L</td>
                            <td>$${item.customerPrice.toFixed(2)}</td>
                            <td>$${item.factoryPrice.toFixed(2)}</td>
                            <td style="color: ${
                              item.margin >= 20
                                ? "green"
                                : item.margin >= 10
                                ? "orange"
                                : "red"
                            }">
                                ${item.margin.toFixed(1)}%
                            </td>
                            <td>$${(
                              item.customerPrice - item.factoryPrice
                            ).toFixed(2)}</td>
                        </tr>
                    `
                      )
                      .join("")}
                </tbody>
            </table>
        `;
  }

  async exportAnalysis() {
    const analysisType = document.getElementById("analysisType").value;

    try {
      this.showLoading(true);

      // Generate CSV content based on current analysis
      let csvContent = "";

      switch (analysisType) {
        case "comparison":
          csvContent = this.generateComparisonCSV();
          break;
        case "margins":
          csvContent = this.generateMarginCSV();
          break;
        default:
          throw new Error("Export not available for this analysis type");
      }

      // Create and download file
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `price-analysis-${analysisType}-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      this.showAlert(
        "Analysis exported successfully!",
        "success",
        "analysisAlertContainer"
      );
    } catch (error) {
      console.error("Export error:", error);
      this.showAlert(
        "Failed to export analysis",
        "error",
        "analysisAlertContainer"
      );
    } finally {
      this.showLoading(false);
    }
  }

  generateMarginCSV() {
    const data = this.generateMarginAnalysis();
    const headers = [
      "Price List",
      "Pressure",
      "Size",
      "Customer Price",
      "Factory Price",
      "Margin %",
      "Profit",
    ];

    const csvContent = [
      headers.join(","),
      ...data.map((item) =>
        [
          `"${item.priceListName}"`,
          item.pressure,
          item.size,
          item.customerPrice.toFixed(2),
          item.factoryPrice.toFixed(2),
          item.margin.toFixed(1),
          (item.customerPrice - item.factoryPrice).toFixed(2),
        ].join(",")
      ),
    ].join("\n");

    return csvContent;
  }

  showLoading(show) {
    const loadingOverlay = document.getElementById("loadingOverlay");
    if (loadingOverlay) {
      loadingOverlay.style.display = show ? "flex" : "none";
    }
  }
}

// Initialize pricing manager when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.pricingManager = new PricingManager();
});
