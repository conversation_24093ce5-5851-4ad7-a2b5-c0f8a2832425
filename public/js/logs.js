// Logs Management JavaScript
class LogsManager {
  constructor() {
    this.currentUser = null;
    this.logs = [];
    this.filteredLogs = [];
    this.currentPage = 1;
    this.itemsPerPage = 25;
    this.totalPages = 1;
    this.init();
  }

  init() {
    // Check authentication
    if (!window.authManager || !window.authManager.isAuthenticated()) {
      window.location.href = "/";
      return;
    }

    this.currentUser = window.authManager.getCurrentUser();

    // Check if user has appropriate privileges
    if (!["root", "admin", "manager", "fill"].includes(this.currentUser.role)) {
      alert("Access denied. Insufficient privileges to view logs.");
      window.location.href = "/";
      return;
    }

    this.setupEventListeners();
    this.updateUI();
    this.setDefaultDates();
  }

  setupEventListeners() {
    // Navigation
    document.getElementById("backToReports").addEventListener("click", () => {
      window.location.href = "/";
    });

    // Load logs
    document.getElementById("loadLogs").addEventListener("click", () => {
      this.loadLogs();
    });

    // Filter changes
    document.getElementById("logTypeFilter").addEventListener("change", () => {
      this.applyFilters();
    });

    document.getElementById("sortOrder").addEventListener("change", () => {
      this.loadLogs();
    });

    document.getElementById("limitSelect").addEventListener("change", () => {
      this.itemsPerPage = parseInt(
        document.getElementById("limitSelect").value
      );
      this.loadLogs();
    });

    document.getElementById("startDate").addEventListener("change", () => {
      this.applyFilters();
    });

    document.getElementById("endDate").addEventListener("change", () => {
      this.applyFilters();
    });

    // Clear filters
    document.getElementById("clearFilters").addEventListener("click", () => {
      this.clearFilters();
    });

    // Export logs
    document.getElementById("exportLogs").addEventListener("click", () => {
      this.exportLogs();
    });

    // Pagination
    document.getElementById("prevPage").addEventListener("click", () => {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.renderTable();
      }
    });

    document.getElementById("nextPage").addEventListener("click", () => {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.renderTable();
      }
    });

    // Modal close
    document.querySelector(".close").addEventListener("click", () => {
      this.closeModal();
    });

    // Close modal when clicking outside
    window.addEventListener("click", (e) => {
      if (e.target.classList.contains("log-details-modal")) {
        this.closeModal();
      }
    });
  }

  updateUI() {
    // Update user info
    const userInfo = document.getElementById("userInfo");
    if (userInfo && this.currentUser) {
      userInfo.textContent = `${this.currentUser.name} (${this.currentUser.role})`;
    }
  }

  setDefaultDates() {
    const today = new Date();
    const lastWeek = new Date(today);
    lastWeek.setDate(today.getDate() - 7);

    document.getElementById("startDate").value = lastWeek
      .toISOString()
      .split("T")[0];
    document.getElementById("endDate").value = today
      .toISOString()
      .split("T")[0];
  }

  async loadLogs() {
    try {
      this.showLoading(true);
      this.clearAlert();

      const limit = document.getElementById("limitSelect").value;
      const sort = document.getElementById("sortOrder").value;

      const response = await fetch(`/public/logs?limit=${limit}&sort=${sort}`, {
        headers: window.authManager.getAuthHeader(),
      });

      const result = await response.json();

      if (response.ok) {
        this.logs = Array.isArray(result) ? result : [];
        this.filteredLogs = [...this.logs];
        this.currentPage = 1;
        this.applyFilters();
        this.renderStats();
        this.showAlert("Logs loaded successfully!", "success");
      } else {
        this.showAlert(result.message || "Failed to load logs", "error");
      }
    } catch (error) {
      console.error("Load logs error:", error);
      this.showAlert("Network error. Please try again.", "error");
    } finally {
      this.showLoading(false);
    }
  }

  applyFilters() {
    const logTypeFilter = document.getElementById("logTypeFilter").value;
    const startDate = document.getElementById("startDate").value;
    const endDate = document.getElementById("endDate").value;

    this.filteredLogs = this.logs.filter((log) => {
      // Type filter
      const matchesType =
        !logTypeFilter ||
        log.logType.toLowerCase() === logTypeFilter.toLowerCase();

      // Date filter
      let matchesDate = true;
      if (startDate || endDate) {
        const logDate = new Date(log.logDate);
        if (startDate) {
          const start = new Date(startDate);
          matchesDate = matchesDate && logDate >= start;
        }
        if (endDate) {
          const end = new Date(endDate);
          end.setHours(23, 59, 59, 999); // End of day
          matchesDate = matchesDate && logDate <= end;
        }
      }

      return matchesType && matchesDate;
    });

    this.currentPage = 1;
    this.renderTable();
  }

  clearFilters() {
    document.getElementById("logTypeFilter").value = "";
    document.getElementById("startDate").value = "";
    document.getElementById("endDate").value = "";
    document.getElementById("sortOrder").value = "desc";
    document.getElementById("limitSelect").value = "25";

    this.filteredLogs = [...this.logs];
    this.itemsPerPage = 25;
    this.currentPage = 1;
    this.renderTable();
  }

  renderTable() {
    const tbody = document.getElementById("logsTableBody");

    if (!this.filteredLogs || this.filteredLogs.length === 0) {
      tbody.innerHTML = `
                <tr>
                    <td colspan="5" style="text-align: center; padding: 40px;">
                        No logs found
                    </td>
                </tr>
            `;
      this.updatePagination();
      return;
    }

    // Calculate pagination
    this.totalPages = Math.ceil(this.filteredLogs.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageData = this.filteredLogs.slice(startIndex, endIndex);

    // Render table rows
    tbody.innerHTML = pageData
      .map(
        (log) => `
            <tr>
                <td>
                    <span class="log-type-badge log-type-${(
                      log.logType || "default"
                    ).toLowerCase()}">
                        ${log.logType || "Unknown"}
                    </span>
                </td>
                <td>
                    <div class="log-date">
                        ${new Date(log.logDate).toLocaleString()}
                    </div>
                </td>
                <td>
                    <div class="staff-info">
                        <div class="staff-avatar">
                            ${this.getInitials(log.logBy?.name || "Unknown")}
                        </div>
                        <div class="staff-details">
                            <div class="staff-name">${
                              log.logBy?.name || "Unknown User"
                            }</div>
                            <div class="staff-role">${
                              log.logBy?.role || "Unknown"
                            }</div>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="log-remarks" id="remarks-${log._id}">
                        ${log.remarks || "No remarks"}
                        ${
                          log.remarks && log.remarks.length > 50
                            ? `
                            <button class="expand-btn" onclick="logsManager.toggleRemarks('${log._id}')">
                                <i class="fas fa-expand-alt"></i>
                            </button>
                        `
                            : ""
                        }
                    </div>
                </td>
                <td>
                    <button class="btn-small btn-view" onclick="logsManager.viewLogDetails('${
                      log._id
                    }')">
                        <i class="fas fa-eye"></i>
                        View
                    </button>
                </td>
            </tr>
        `
      )
      .join("");

    this.updatePagination();
  }

  updatePagination() {
    const prevBtn = document.getElementById("prevPage");
    const nextBtn = document.getElementById("nextPage");
    const pageInfo = document.getElementById("pageInfo");

    prevBtn.disabled = this.currentPage <= 1;
    nextBtn.disabled = this.currentPage >= this.totalPages;

    pageInfo.textContent = `Page ${this.currentPage} of ${this.totalPages} (${this.filteredLogs.length} logs)`;
  }

  renderStats() {
    const statsRow = document.getElementById("statsRow");

    if (!this.logs || this.logs.length === 0) {
      statsRow.innerHTML = "";
      return;
    }

    const stats = this.calculateStats();

    statsRow.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${stats.total}</div>
                <div class="stat-label">Total Logs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.today}</div>
                <div class="stat-label">Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.errors}</div>
                <div class="stat-label">Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.warnings}</div>
                <div class="stat-label">Warnings</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.users}</div>
                <div class="stat-label">Active Users</div>
            </div>
        `;
  }

  calculateStats() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const stats = {
      total: this.logs.length,
      today: 0,
      errors: 0,
      warnings: 0,
      users: new Set(),
    };

    this.logs.forEach((log) => {
      // Count today's logs
      const logDate = new Date(log.logDate);
      logDate.setHours(0, 0, 0, 0);
      if (logDate.getTime() === today.getTime()) {
        stats.today++;
      }

      // Count by type
      const logType = (log.logType || "").toLowerCase();
      if (logType === "error") {
        stats.errors++;
      } else if (logType === "warning") {
        stats.warnings++;
      }

      // Count unique users
      if (log.logBy?._id) {
        stats.users.add(log.logBy._id);
      }
    });

    stats.users = stats.users.size;
    return stats;
  }

  getInitials(name) {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  }

  toggleRemarks(logId) {
    const remarksElement = document.getElementById(`remarks-${logId}`);
    remarksElement.classList.toggle("expanded");

    const button = remarksElement.querySelector(".expand-btn i");
    if (remarksElement.classList.contains("expanded")) {
      button.className = "fas fa-compress-alt";
    } else {
      button.className = "fas fa-expand-alt";
    }
  }

  viewLogDetails(logId) {
    const log = this.logs.find((l) => l._id === logId);
    if (!log) return;

    const content = document.getElementById("logDetailsContent");

    content.innerHTML = `
            <div class="detail-group">
                <label class="detail-label">Log Type</label>
                <div class="detail-value">
                    <span class="log-type-badge log-type-${(
                      log.logType || "default"
                    ).toLowerCase()}">
                        ${log.logType || "Unknown"}
                    </span>
                </div>
            </div>

            <div class="detail-group">
                <label class="detail-label">Date & Time</label>
                <div class="detail-value log-date">
                    ${new Date(log.logDate).toLocaleString()}
                </div>
            </div>

            <div class="detail-group">
                <label class="detail-label">User</label>
                <div class="detail-value">
                    <div class="staff-info">
                        <div class="staff-avatar">
                            ${this.getInitials(log.logBy?.name || "Unknown")}
                        </div>
                        <div class="staff-details">
                            <div class="staff-name">${
                              log.logBy?.name || "Unknown User"
                            }</div>
                            <div class="staff-role">${
                              log.logBy?.role || "Unknown Role"
                            }</div>
                            ${
                              log.logBy?.email
                                ? `<div style="font-size: 0.8rem; color: #666;">${log.logBy.email}</div>`
                                : ""
                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-group">
                <label class="detail-label">Remarks</label>
                <div class="detail-value">
                    ${log.remarks || "No remarks provided"}
                </div>
            </div>

            <div class="detail-group">
                <label class="detail-label">Log ID</label>
                <div class="detail-value" style="font-family: monospace; font-size: 0.9rem;">
                    ${log._id}
                </div>
            </div>

            <div class="detail-group">
                <label class="detail-label">Created At</label>
                <div class="detail-value log-date">
                    ${new Date(log.createdAt).toLocaleString()}
                </div>
            </div>

            ${
              log.updatedAt && log.updatedAt !== log.createdAt
                ? `
                <div class="detail-group">
                    <label class="detail-label">Updated At</label>
                    <div class="detail-value log-date">
                        ${new Date(log.updatedAt).toLocaleString()}
                    </div>
                </div>
            `
                : ""
            }
        `;

    this.showModal();
  }

  showModal() {
    document.getElementById("logDetailsModal").style.display = "block";
  }

  closeModal() {
    document.getElementById("logDetailsModal").style.display = "none";
  }

  async exportLogs() {
    try {
      this.showLoading(true);

      // Create CSV content
      const headers = ["Type", "Date", "User", "Role", "Remarks", "Log ID"];
      const csvContent = [
        headers.join(","),
        ...this.filteredLogs.map((log) =>
          [
            `"${log.logType || "Unknown"}"`,
            `"${new Date(log.logDate).toLocaleString()}"`,
            `"${log.logBy?.name || "Unknown User"}"`,
            `"${log.logBy?.role || "Unknown"}"`,
            `"${(log.remarks || "No remarks").replace(/"/g, '""')}"`,
            `"${log._id}"`,
          ].join(",")
        ),
      ].join("\n");

      // Create and download file
      const blob = new Blob([csvContent], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `system-logs-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      this.showAlert("Logs exported successfully!", "success");
    } catch (error) {
      console.error("Export error:", error);
      this.showAlert("Failed to export logs", "error");
    } finally {
      this.showLoading(false);
    }
  }

  showAlert(message, type = "success") {
    const container = document.getElementById("alertContainer");
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;

    // Auto-hide after 5 seconds
    setTimeout(() => {
      container.innerHTML = "";
    }, 5000);
  }

  clearAlert() {
    const container = document.getElementById("alertContainer");
    container.innerHTML = "";
  }

  showLoading(show) {
    const loadingOverlay = document.getElementById("loadingOverlay");
    if (loadingOverlay) {
      loadingOverlay.style.display = show ? "flex" : "none";
    }
  }
}

// Initialize logs manager when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.logsManager = new LogsManager();
});
