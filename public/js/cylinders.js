// Cylinder Management JavaScript
class CylinderManager {
  constructor() {
    this.currentUser = null;
    this.cylinders = [];
    this.filteredCylinders = [];
    this.currentPage = 1;
    this.itemsPerPage = 20;
    this.totalPages = 1;
    this.init();
  }

  init() {
    // Check authentication
    if (!window.authManager || !window.authManager.isAuthenticated()) {
      window.location.href = "/";
      return;
    }

    this.currentUser = window.authManager.getCurrentUser();

    // Check if user has appropriate privileges
    if (
      !["root", "admin", "manager", "fill", "sale"].includes(
        this.currentUser.role
      )
    ) {
      alert("Access denied. Insufficient privileges.");
      window.location.href = "/";
      return;
    }

    this.setupEventListeners();
    this.updateUI();
    this.setDefaultDates();
  }

  setupEventListeners() {
    // Navigation
    document.getElementById("backToReports").addEventListener("click", () => {
      window.location.href = "/";
    });

    // Load cylinders
    document.getElementById("loadCylinders").addEventListener("click", () => {
      this.loadCylinders();
    });

    // Search functionality
    document.getElementById("searchBtn").addEventListener("click", () => {
      this.searchCylinders();
    });

    document.getElementById("clearSearch").addEventListener("click", () => {
      this.clearSearch();
    });

    document.getElementById("searchInput").addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        this.searchCylinders();
      }
    });

    // Filter changes
    document.getElementById("statusFilter").addEventListener("change", () => {
      this.applyFilters();
    });

    document.getElementById("sizeFilter").addEventListener("change", () => {
      this.applyFilters();
    });

    document.getElementById("formatFilter").addEventListener("change", () => {
      this.applyFilters();
    });

    // Pagination
    document.getElementById("prevPage").addEventListener("click", () => {
      if (this.currentPage > 1) {
        this.currentPage--;
        this.renderTable();
      }
    });

    document.getElementById("nextPage").addEventListener("click", () => {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
        this.renderTable();
      }
    });

    // Add cylinder
    document.getElementById("addCylinder").addEventListener("click", () => {
      this.showAddModal();
    });

    // Form submissions
    document.getElementById("editForm").addEventListener("submit", (e) => {
      e.preventDefault();
      this.updateCylinder();
    });

    document.getElementById("addForm").addEventListener("submit", (e) => {
      e.preventDefault();
      this.addCylinder();
    });

    // Modal close handlers
    document.querySelectorAll(".close, [data-modal]").forEach((element) => {
      element.addEventListener("click", (e) => {
        const modalId =
          e.target.getAttribute("data-modal") || e.target.closest(".modal").id;
        if (modalId) {
          this.closeModal(modalId);
        }
      });
    });

    // Close modal when clicking outside
    window.addEventListener("click", (e) => {
      if (e.target.classList.contains("modal")) {
        this.closeModal(e.target.id);
      }
    });
  }

  updateUI() {
    // Update user info
    const userInfo = document.getElementById("userInfo");
    if (userInfo && this.currentUser) {
      userInfo.textContent = `${this.currentUser.name} (${this.currentUser.role})`;
    }
  }

  setDefaultDates() {
    const today = new Date().toISOString().split("T")[0];
    document.getElementById("addProductionDate").value = today;
    document.getElementById("addImportDate").value = today;
  }

  async loadCylinders() {
    try {
      this.showLoading(true);
      this.clearAlert();

      const response = await fetch("/api/cylinder-master", {
        headers: window.authManager.getAuthHeader(),
      });

      const result = await response.json();

      if (response.ok) {
        this.cylinders = Array.isArray(result) ? result : [];
        this.filteredCylinders = [...this.cylinders];
        this.currentPage = 1;
        this.renderTable();
        this.renderStats();
        this.showAlert("Cylinders loaded successfully!", "success");
      } else {
        this.showAlert(result.message || "Failed to load cylinders", "error");
      }
    } catch (error) {
      console.error("Load cylinders error:", error);
      this.showAlert("Network error. Please try again.", "error");
    } finally {
      this.showLoading(false);
    }
  }

  searchCylinders() {
    const searchTerm = document
      .getElementById("searchInput")
      .value.trim()
      .toLowerCase();

    if (!searchTerm) {
      this.filteredCylinders = [...this.cylinders];
    } else {
      this.filteredCylinders = this.cylinders.filter(
        (cylinder) =>
          cylinder.serialNumber?.toLowerCase().includes(searchTerm) ||
          cylinder.originalNumber?.toLowerCase().includes(searchTerm)
      );
    }

    this.currentPage = 1;
    this.renderTable();
  }

  clearSearch() {
    document.getElementById("searchInput").value = "";
    document.getElementById("statusFilter").value = "";
    document.getElementById("sizeFilter").value = "";
    document.getElementById("formatFilter").value = "";
    this.filteredCylinders = [...this.cylinders];
    this.currentPage = 1;
    this.renderTable();
  }

  applyFilters() {
    const statusFilter = document.getElementById("statusFilter").value;
    const sizeFilter = document.getElementById("sizeFilter").value;
    const formatFilter = document.getElementById("formatFilter").value;
    const searchTerm = document
      .getElementById("searchInput")
      .value.trim()
      .toLowerCase();

    this.filteredCylinders = this.cylinders.filter((cylinder) => {
      // Search filter
      const matchesSearch =
        !searchTerm ||
        cylinder.serialNumber?.toLowerCase().includes(searchTerm) ||
        cylinder.originalNumber?.toLowerCase().includes(searchTerm);

      // Status filter
      const matchesStatus = !statusFilter || cylinder.status === statusFilter;

      // Size filter
      const matchesSize = !sizeFilter || cylinder.cylinderSize === sizeFilter;

      // Format filter
      const matchesFormat =
        !formatFilter ||
        (formatFilter === "new" &&
          /^[A-Z]\d{4}$/.test(cylinder.serialNumber)) ||
        (formatFilter === "old" && /^CYD\d+$/.test(cylinder.serialNumber));

      return matchesSearch && matchesStatus && matchesSize && matchesFormat;
    });

    this.currentPage = 1;
    this.renderTable();
  }

  renderTable() {
    const tbody = document.getElementById("cylinderTableBody");

    if (!this.filteredCylinders || this.filteredCylinders.length === 0) {
      tbody.innerHTML = `
                <tr>
                    <td colspan="9" style="text-align: center; padding: 40px;">
                        No cylinders found
                    </td>
                </tr>
            `;
      this.updatePagination();
      return;
    }

    // Calculate pagination
    this.totalPages = Math.ceil(
      this.filteredCylinders.length / this.itemsPerPage
    );
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageData = this.filteredCylinders.slice(startIndex, endIndex);

    // Render table rows
    tbody.innerHTML = pageData
      .map(
        (cylinder) => `
            <tr>
                <td>
                    <span class="serial-number" style="font-weight: 600; color: #667eea;">
                        ${cylinder.serialNumber || "N/A"}
                    </span>
                </td>
                <td>${cylinder.originalNumber || "N/A"}</td>
                <td>
                    <span class="size-badge">${
                      cylinder.cylinderSize || "N/A"
                    }</span>
                </td>
                <td>
                    <span class="status-badge status-${(cylinder.status || "")
                      .toLowerCase()
                      .replace(/\s+/g, "-")}">
                        ${cylinder.status || "N/A"}
                    </span>
                </td>
                <td>${cylinder.valueType || "N/A"}</td>
                <td>${cylinder.workingPressure || "N/A"}</td>
                <td>${
                  cylinder.productionDate
                    ? new Date(cylinder.productionDate).toLocaleDateString()
                    : "N/A"
                }</td>
                <td>${
                  cylinder.importDate
                    ? new Date(cylinder.importDate).toLocaleDateString()
                    : "N/A"
                }</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-small btn-view" onclick="cylinderManager.viewCylinder('${
                          cylinder._id
                        }')">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${
                          this.canEdit()
                            ? `
                            <button class="btn-small btn-edit" onclick="cylinderManager.editCylinder('${cylinder._id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                        `
                            : ""
                        }
                        ${
                          this.canDelete()
                            ? `
                            <button class="btn-small btn-delete" onclick="cylinderManager.deleteCylinder('${cylinder._id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        `
                            : ""
                        }
                    </div>
                </td>
            </tr>
        `
      )
      .join("");

    this.updatePagination();
  }

  updatePagination() {
    const prevBtn = document.getElementById("prevPage");
    const nextBtn = document.getElementById("nextPage");
    const pageInfo = document.getElementById("pageInfo");

    prevBtn.disabled = this.currentPage <= 1;
    nextBtn.disabled = this.currentPage >= this.totalPages;

    pageInfo.textContent = `Page ${this.currentPage} of ${this.totalPages} (${this.filteredCylinders.length} cylinders)`;
  }

  renderStats() {
    const statsRow = document.getElementById("statsRow");

    if (!this.cylinders || this.cylinders.length === 0) {
      statsRow.innerHTML = "";
      return;
    }

    const stats = this.calculateStats();

    statsRow.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${stats.total}</div>
                <div class="stat-label">Total Cylinders</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.newFormat}</div>
                <div class="stat-label">New Format</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.full}</div>
                <div class="stat-label">Full</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.empty}</div>
                <div class="stat-label">Empty</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.available}</div>
                <div class="stat-label">Available</div>
            </div>
        `;
  }

  calculateStats() {
    const stats = {
      total: this.cylinders.length,
      newFormat: 0,
      full: 0,
      empty: 0,
      available: 0,
    };

    this.cylinders.forEach((cylinder) => {
      // Count new format
      if (/^[A-Z]\d{4}$/.test(cylinder.serialNumber)) {
        stats.newFormat++;
      }

      // Count by status
      switch (cylinder.status) {
        case "Full":
          stats.full++;
          break;
        case "Empty":
          stats.empty++;
          break;
        case "Available":
          stats.available++;
          break;
      }
    });

    return stats;
  }

  canEdit() {
    return ["root", "admin", "manager"].includes(this.currentUser.role);
  }

  canDelete() {
    return ["root", "admin"].includes(this.currentUser.role);
  }

  async viewCylinder(cylinderId) {
    try {
      this.showLoading(true);

      const response = await fetch(`/api/cylinder-master/${cylinderId}`, {
        headers: window.authManager.getAuthHeader(),
      });

      const cylinder = await response.json();

      if (response.ok) {
        this.showViewModal(cylinder);
      } else {
        this.showAlert(
          cylinder.message || "Failed to load cylinder details",
          "error"
        );
      }
    } catch (error) {
      console.error("View cylinder error:", error);
      this.showAlert("Network error. Please try again.", "error");
    } finally {
      this.showLoading(false);
    }
  }

  showViewModal(cylinder) {
    const content = document.getElementById("viewContent");

    content.innerHTML = `
            <div class="form-grid">
                <div class="form-group">
                    <label>Serial Number</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px; font-weight: 600; color: #667eea;">
                        ${cylinder.serialNumber || "N/A"}
                    </div>
                </div>

                <div class="form-group">
                    <label>Original Number</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${cylinder.originalNumber || "N/A"}
                    </div>
                </div>

                <div class="form-group">
                    <label>Cylinder Size</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${cylinder.cylinderSize || "N/A"}
                    </div>
                </div>

                <div class="form-group">
                    <label>Status</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        <span class="status-badge status-${(
                          cylinder.status || ""
                        )
                          .toLowerCase()
                          .replace(/\s+/g, "-")}">
                            ${cylinder.status || "N/A"}
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label>Value Type</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${cylinder.valueType || "N/A"}
                    </div>
                </div>

                <div class="form-group">
                    <label>Working Pressure</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${cylinder.workingPressure || "N/A"}
                    </div>
                </div>

                <div class="form-group">
                    <label>Production Date</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${
                          cylinder.productionDate
                            ? new Date(
                                cylinder.productionDate
                              ).toLocaleDateString()
                            : "N/A"
                        }
                    </div>
                </div>

                <div class="form-group">
                    <label>Import Date</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${
                          cylinder.importDate
                            ? new Date(cylinder.importDate).toLocaleDateString()
                            : "N/A"
                        }
                    </div>
                </div>

                ${
                  cylinder.remarks
                    ? `
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label>Remarks</label>
                        <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                            ${cylinder.remarks}
                        </div>
                    </div>
                `
                    : ""
                }

                <div class="form-group" style="grid-column: 1 / -1;">
                    <label>Created</label>
                    <div style="padding: 12px; background: #f8f9fa; border-radius: 4px;">
                        ${
                          cylinder.createdAt
                            ? new Date(cylinder.createdAt).toLocaleString()
                            : "N/A"
                        }
                    </div>
                </div>
            </div>
        `;

    this.showModal("viewModal");
  }

  showModal(modalId) {
    document.getElementById(modalId).style.display = "block";
  }

  closeModal(modalId) {
    document.getElementById(modalId).style.display = "none";
    this.clearAlert("editAlert");
    this.clearAlert("addAlert");
  }

  showAlert(message, type = "success", containerId = "alertContainer") {
    const container = document.getElementById(containerId);
    container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;

    // Auto-hide after 5 seconds
    setTimeout(() => {
      container.innerHTML = "";
    }, 5000);
  }

  clearAlert(containerId = "alertContainer") {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = "";
    }
  }

  async editCylinder(cylinderId) {
    try {
      this.showLoading(true);

      const response = await fetch(`/api/cylinder-master/${cylinderId}`, {
        headers: window.authManager.getAuthHeader(),
      });

      const cylinder = await response.json();

      if (response.ok) {
        this.populateEditForm(cylinder);
        this.showModal("editModal");
      } else {
        this.showAlert(
          cylinder.message || "Failed to load cylinder details",
          "error"
        );
      }
    } catch (error) {
      console.error("Edit cylinder error:", error);
      this.showAlert("Network error. Please try again.", "error");
    } finally {
      this.showLoading(false);
    }
  }

  populateEditForm(cylinder) {
    document.getElementById("editSerialNumber").value =
      cylinder.serialNumber || "";
    document.getElementById("editOriginalNumber").value =
      cylinder.originalNumber || "";
    document.getElementById("editCylinderSize").value =
      cylinder.cylinderSize || "";
    document.getElementById("editStatus").value = cylinder.status || "";
    document.getElementById("editValueType").value = cylinder.valueType || "";
    document.getElementById("editWorkingPressure").value =
      cylinder.workingPressure || "";
    document.getElementById("editProductionDate").value =
      cylinder.productionDate
        ? new Date(cylinder.productionDate).toISOString().split("T")[0]
        : "";
    document.getElementById("editImportDate").value = cylinder.importDate
      ? new Date(cylinder.importDate).toISOString().split("T")[0]
      : "";
    document.getElementById("editRemarks").value = cylinder.remarks || "";

    // Store cylinder ID for update
    document.getElementById("editForm").dataset.cylinderId = cylinder._id;
  }

  async updateCylinder() {
    try {
      this.showLoading(true);
      this.clearAlert("editAlert");

      const cylinderId = document.getElementById("editForm").dataset.cylinderId;
      const formData = {
        originalNumber: document.getElementById("editOriginalNumber").value,
        cylinderSize: document.getElementById("editCylinderSize").value,
        status: document.getElementById("editStatus").value,
        valueType: document.getElementById("editValueType").value,
        workingPressure: parseInt(
          document.getElementById("editWorkingPressure").value
        ),
        productionDate: document.getElementById("editProductionDate").value,
        importDate: document.getElementById("editImportDate").value,
        remarks: document.getElementById("editRemarks").value,
      };

      const response = await fetch(`/api/cylinder-master/${cylinderId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          ...window.authManager.getAuthHeader(),
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        this.showAlert(
          "Cylinder updated successfully!",
          "success",
          "editAlert"
        );
        setTimeout(() => {
          this.closeModal("editModal");
          this.loadCylinders(); // Reload the table
        }, 1500);
      } else {
        this.showAlert(
          result.message || "Failed to update cylinder",
          "error",
          "editAlert"
        );
      }
    } catch (error) {
      console.error("Update cylinder error:", error);
      this.showAlert("Network error. Please try again.", "error", "editAlert");
    } finally {
      this.showLoading(false);
    }
  }

  showAddModal() {
    // Reset form
    document.getElementById("addForm").reset();
    this.setDefaultDates();
    this.clearAlert("addAlert");
    this.showModal("addModal");
  }

  async addCylinder() {
    try {
      this.showLoading(true);
      this.clearAlert("addAlert");

      const formData = {
        originalNumber: document.getElementById("addOriginalNumber").value,
        cylinderSize: document.getElementById("addCylinderSize").value,
        status: document.getElementById("addStatus").value,
        valueType: document.getElementById("addValueType").value,
        workingPressure: parseInt(
          document.getElementById("addWorkingPressure").value
        ),
        productionDate: document.getElementById("addProductionDate").value,
        importDate: document.getElementById("addImportDate").value,
        remarks: document.getElementById("addRemarks").value,
      };

      const response = await fetch("/api/cylinder-master", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...window.authManager.getAuthHeader(),
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        this.showAlert("Cylinder added successfully!", "success", "addAlert");
        setTimeout(() => {
          this.closeModal("addModal");
          this.loadCylinders(); // Reload the table
        }, 1500);
      } else {
        this.showAlert(
          result.message || "Failed to add cylinder",
          "error",
          "addAlert"
        );
      }
    } catch (error) {
      console.error("Add cylinder error:", error);
      this.showAlert("Network error. Please try again.", "error", "addAlert");
    } finally {
      this.showLoading(false);
    }
  }

  async deleteCylinder(cylinderId) {
    try {
      // Find cylinder details for confirmation
      const cylinder = this.cylinders.find((c) => c._id === cylinderId);
      const serialNumber = cylinder ? cylinder.serialNumber : "Unknown";

      if (
        !confirm(
          `Are you sure you want to delete cylinder ${serialNumber}? This action cannot be undone.`
        )
      ) {
        return;
      }

      this.showLoading(true);

      const response = await fetch(`/api/cylinder-master/${cylinderId}`, {
        method: "DELETE",
        headers: window.authManager.getAuthHeader(),
      });

      const result = await response.json();

      if (response.ok) {
        this.showAlert("Cylinder deleted successfully!", "success");
        this.loadCylinders(); // Reload the table
      } else {
        this.showAlert(result.message || "Failed to delete cylinder", "error");
      }
    } catch (error) {
      console.error("Delete cylinder error:", error);
      this.showAlert("Network error. Please try again.", "error");
    } finally {
      this.showLoading(false);
    }
  }

  showLoading(show) {
    const loadingOverlay = document.getElementById("loadingOverlay");
    if (loadingOverlay) {
      loadingOverlay.style.display = show ? "flex" : "none";
    }
  }
}

// Initialize cylinder manager when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.cylinderManager = new CylinderManager();
});
