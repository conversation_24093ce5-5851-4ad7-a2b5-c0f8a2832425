// Reports management
class ReportsManager {
  constructor() {
    this.currentData = {
      overview: null,
      customer: null,
      customerType: null,
      filling: null,
      fillingLine: null,
      fillingStaff: null,
    };
    this.init();
  }

  init() {
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Tab switching
    const tabBtns = document.querySelectorAll(".tab-btn");
    tabBtns.forEach((btn) => {
      btn.addEventListener("click", (e) =>
        this.switchTab(e.target.dataset.tab)
      );
    });

    // Load reports button
    const loadReportsBtn = document.getElementById("loadReportsBtn");
    if (loadReportsBtn) {
      loadReportsBtn.addEventListener("click", () => this.loadAllReports());
    }

    // Export buttons
    document
      .getElementById("exportOverview")
      ?.addEventListener("click", () => this.exportReport("overview"));
    document
      .getElementById("exportCustomer")
      ?.addEventListener("click", () => this.exportReport("customer"));
    document
      .getElementById("exportCustomerType")
      ?.addEventListener("click", () => this.exportReport("customerType"));
    document
      .getElementById("exportFilling")
      ?.addEventListener("click", () => this.exportReport("filling"));
    document
      .getElementById("exportFillingLine")
      ?.addEventListener("click", () => this.exportReport("fillingLine"));
    document
      .getElementById("exportFillingStaff")
      ?.addEventListener("click", () => this.exportReport("fillingStaff"));
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll(".tab-btn").forEach((btn) => {
      btn.classList.remove("active");
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add("active");

    // Update tab content
    document.querySelectorAll(".tab-pane").forEach((pane) => {
      pane.classList.remove("active");
    });
    document.getElementById(`${tabName}Tab`).classList.add("active");
  }

  async loadAllReports() {
    if (!window.authManager.isAuthenticated()) {
      alert("Please login first");
      return;
    }

    const startDate = document.getElementById("startDate").value;
    const endDate = document.getElementById("endDate").value;

    if (!startDate || !endDate) {
      alert("Please select both start and end dates");
      return;
    }

    if (new Date(startDate) > new Date(endDate)) {
      alert("Start date cannot be after end date");
      return;
    }

    window.authManager.showLoading(true);

    try {
      // Load all reports concurrently
      const [
        overviewData,
        customerData,
        customerTypeData,
        fillingData,
        fillingLineData,
        fillingStaffData,
      ] = await Promise.all([
        this.fetchReport("daily-overview", startDate, endDate),
        this.fetchReport("daily-by-customer", startDate, endDate),
        this.fetchReport("daily-by-customer-type", startDate, endDate),
        this.fetchReport("filling-overview", startDate, endDate),
        this.fetchReport("filling-by-line", startDate, endDate),
        this.fetchReport("filling-by-staff", startDate, endDate),
      ]);

      this.currentData.overview = overviewData;
      this.currentData.customer = customerData;
      this.currentData.customerType = customerTypeData;
      this.currentData.filling = fillingData;
      this.currentData.fillingLine = fillingLineData;
      this.currentData.fillingStaff = fillingStaffData;

      // Render all reports
      this.renderOverviewReport(overviewData);
      this.renderCustomerReport(customerData);
      this.renderCustomerTypeReport(customerTypeData);
      this.renderFillingReport(fillingData);
      this.renderFillingLineReport(fillingLineData);
      this.renderFillingStaffReport(fillingStaffData);
    } catch (error) {
      console.error("Error loading reports:", error);
      alert("Error loading reports. Please try again.");
    } finally {
      window.authManager.showLoading(false);
    }
  }

  async fetchReport(endpoint, startDate, endDate) {
    const response = await fetch(
      `/public/${endpoint}?startDate=${startDate}&endDate=${endDate}`,
      {
        headers: window.authManager.getAuthHeader(),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch ${endpoint}`);
    }

    return await response.json();
  }

  renderOverviewReport(data) {
    const container = document.getElementById("overviewContent");

    if (!data || !data.headers || data.headers.length === 0) {
      container.innerHTML =
        '<p class="no-data">No data available for the selected period</p>';
      return;
    }

    const table = this.createOverviewTable(data);
    container.innerHTML = "";
    container.appendChild(table);
  }

  createOverviewTable(data) {
    const table = document.createElement("table");
    table.className = "report-table";

    // Create header
    const thead = document.createElement("thead");
    const headerRow1 = document.createElement("tr");
    const headerRow2 = document.createElement("tr");

    // Month headers
    headerRow1.appendChild(this.createCell("th", ""));
    let currentMonth = "";
    data.headers.forEach((header) => {
      const cell = this.createCell(
        "th",
        header.monthYear !== currentMonth ? header.monthYear : ""
      );
      headerRow1.appendChild(cell);
      currentMonth = header.monthYear;
    });

    // Day headers
    headerRow2.appendChild(this.createCell("th", ""));
    data.headers.forEach((header) => {
      headerRow2.appendChild(this.createCell("th", header.day));
    });

    thead.appendChild(headerRow1);
    thead.appendChild(headerRow2);
    table.appendChild(thead);

    // Create body
    const tbody = document.createElement("tbody");

    // Data rows
    const rows = [
      { label: "Deliver", data: data.dailyOverall.deliver },
      { label: "Return", data: data.dailyOverall.return },
      {
        label: "Balance in Circulation",
        data: data.dailyOverall.balanceInCirculation,
      },
      { label: "Balance at Factory", data: data.dailyOverall.balanceAtFactory },
      { label: "Total Cylinders", data: data.dailyOverall.totalCylinders },
    ];

    rows.forEach((row) => {
      const tr = document.createElement("tr");
      tr.appendChild(this.createCell("td", row.label, "font-weight: bold"));
      row.data.forEach((value) => {
        tr.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    return table;
  }

  renderCustomerReport(data) {
    const container = document.getElementById("customerContent");

    if (!data || !data.customerOverview || data.customerOverview.length === 0) {
      container.innerHTML =
        '<p class="no-data">No customer data available for the selected period</p>';
      return;
    }

    const table = this.createCustomerTable(data);
    container.innerHTML = "";
    container.appendChild(table);
  }

  createCustomerTable(data) {
    const table = document.createElement("table");
    table.className = "report-table";

    // Create header
    const thead = document.createElement("thead");
    const headerRow1 = document.createElement("tr");
    const headerRow2 = document.createElement("tr");

    // First row headers
    headerRow1.appendChild(this.createCell("th", "Sr"));
    headerRow1.appendChild(this.createCell("th", "Customer"));
    headerRow1.appendChild(this.createCell("th", ""));

    let currentMonth = "";
    data.headers.forEach((header) => {
      const cell = this.createCell(
        "th",
        header.monthYear !== currentMonth ? header.monthYear : ""
      );
      headerRow1.appendChild(cell);
      currentMonth = header.monthYear;
    });

    // Second row headers
    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", ""));
    data.headers.forEach((header) => {
      headerRow2.appendChild(this.createCell("th", header.day));
    });

    thead.appendChild(headerRow1);
    thead.appendChild(headerRow2);
    table.appendChild(thead);

    // Create body
    const tbody = document.createElement("tbody");

    data.customerOverview.forEach((customer) => {
      // Delivered row
      const deliveredRow = document.createElement("tr");
      deliveredRow.appendChild(
        this.createCell("td", customer.sr, "font-weight: bold")
      );
      deliveredRow.appendChild(
        this.createCell(
          "td",
          customer.customerName,
          "font-weight: bold; text-align: left"
        )
      );
      deliveredRow.appendChild(
        this.createCell("td", "Delivered", "text-align: left")
      );
      customer.delivered.forEach((value) => {
        deliveredRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(deliveredRow);

      // Returned row
      const returnedRow = document.createElement("tr");
      returnedRow.appendChild(this.createCell("td", ""));
      returnedRow.appendChild(this.createCell("td", ""));
      returnedRow.appendChild(
        this.createCell("td", "Returned", "text-align: left")
      );
      customer.returned.forEach((value) => {
        returnedRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(returnedRow);

      // Balance row
      const balanceRow = document.createElement("tr");
      balanceRow.appendChild(this.createCell("td", ""));
      balanceRow.appendChild(this.createCell("td", ""));
      balanceRow.appendChild(
        this.createCell("td", "Balance", "text-align: left; font-weight: bold")
      );
      customer.balance.forEach((value) => {
        balanceRow.appendChild(
          this.createCell("td", value, "font-weight: bold")
        );
      });
      tbody.appendChild(balanceRow);
    });

    table.appendChild(tbody);
    return table;
  }

  renderCustomerTypeReport(data) {
    const container = document.getElementById("customerTypeContent");

    if (
      !data ||
      !data.customerTypeOverview ||
      data.customerTypeOverview.length === 0
    ) {
      container.innerHTML =
        '<p class="no-data">No customer type data available for the selected period</p>';
      return;
    }

    const table = this.createCustomerTypeTable(data);
    container.innerHTML = "";
    container.appendChild(table);
  }

  createCustomerTypeTable(data) {
    const table = document.createElement("table");
    table.className = "report-table";

    // Create header (similar to customer table)
    const thead = document.createElement("thead");
    const headerRow1 = document.createElement("tr");
    const headerRow2 = document.createElement("tr");

    headerRow1.appendChild(this.createCell("th", "Customer Type"));
    headerRow1.appendChild(this.createCell("th", ""));

    let currentMonth = "";
    data.headers.forEach((header) => {
      const cell = this.createCell(
        "th",
        header.monthYear !== currentMonth ? header.monthYear : ""
      );
      headerRow1.appendChild(cell);
      currentMonth = header.monthYear;
    });

    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", ""));
    data.headers.forEach((header) => {
      headerRow2.appendChild(this.createCell("th", header.day));
    });

    thead.appendChild(headerRow1);
    thead.appendChild(headerRow2);
    table.appendChild(thead);

    // Create body
    const tbody = document.createElement("tbody");

    data.customerTypeOverview.forEach((customerType) => {
      // Delivered row
      const deliveredRow = document.createElement("tr");
      deliveredRow.appendChild(
        this.createCell(
          "td",
          customerType.customerType,
          "font-weight: bold; text-align: left"
        )
      );
      deliveredRow.appendChild(
        this.createCell("td", "Delivered", "text-align: left")
      );
      customerType.delivered.forEach((value) => {
        deliveredRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(deliveredRow);

      // Returned row
      const returnedRow = document.createElement("tr");
      returnedRow.appendChild(this.createCell("td", ""));
      returnedRow.appendChild(
        this.createCell("td", "Returned", "text-align: left")
      );
      customerType.returned.forEach((value) => {
        returnedRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(returnedRow);

      // Balance row
      const balanceRow = document.createElement("tr");
      balanceRow.appendChild(this.createCell("td", ""));
      balanceRow.appendChild(
        this.createCell("td", "Balance", "text-align: left; font-weight: bold")
      );
      customerType.balance.forEach((value) => {
        balanceRow.appendChild(
          this.createCell("td", value, "font-weight: bold")
        );
      });
      tbody.appendChild(balanceRow);
    });

    // Add totals section
    if (data.totals) {
      // Add separator row
      const separatorRow = document.createElement("tr");
      const separatorCell = this.createCell(
        "td",
        "",
        "border-top: 2px solid #333"
      );
      separatorCell.colSpan = 2 + data.headers.length;
      separatorRow.appendChild(separatorCell);
      tbody.appendChild(separatorRow);

      // Total rows
      const totalRows = [
        { label: "Total Delivered", data: data.totals.delivered },
        { label: "Total Returned", data: data.totals.returned },
        {
          label: "Balance in Circulation",
          data: data.totals.balanceInCirculation,
        },
        { label: "Balance at Factory", data: data.totals.balanceAtFactory },
        { label: "Total Cylinders", data: data.totals.totalCylinders },
      ];

      totalRows.forEach((row) => {
        const tr = document.createElement("tr");
        tr.appendChild(
          this.createCell(
            "td",
            row.label,
            "font-weight: bold; text-align: left; background: #f8f9fa"
          )
        );
        tr.appendChild(this.createCell("td", "", "background: #f8f9fa"));
        row.data.forEach((value) => {
          tr.appendChild(
            this.createCell(
              "td",
              value,
              "font-weight: bold; background: #f8f9fa"
            )
          );
        });
        tbody.appendChild(tr);
      });
    }

    table.appendChild(tbody);
    return table;
  }

  renderFillingReport(data) {
    const container = document.getElementById("fillingContent");

    if (!data || !data.headers || data.headers.length === 0) {
      container.innerHTML =
        '<p class="no-data">No filling data available for the selected period</p>';
      return;
    }

    const table = this.createFillingTable(data);
    container.innerHTML = "";
    container.appendChild(table);
  }

  createFillingTable(data) {
    const table = document.createElement("table");
    table.className = "report-table";

    // Create header
    const thead = document.createElement("thead");
    const headerRow1 = document.createElement("tr");
    const headerRow2 = document.createElement("tr");

    // Month headers
    headerRow1.appendChild(this.createCell("th", ""));
    let currentMonth = "";
    data.headers.forEach((header) => {
      const cell = this.createCell(
        "th",
        header.monthYear !== currentMonth ? header.monthYear : ""
      );
      headerRow1.appendChild(cell);
      currentMonth = header.monthYear;
    });

    // Day headers
    headerRow2.appendChild(this.createCell("th", ""));
    data.headers.forEach((header) => {
      headerRow2.appendChild(this.createCell("th", header.day));
    });

    thead.appendChild(headerRow1);
    thead.appendChild(headerRow2);
    table.appendChild(thead);

    // Create body
    const tbody = document.createElement("tbody");

    // Data rows
    const rows = [
      { label: "Total Processes", data: data.dailyOverall.totalProcesses },
      { label: "Total Cylinders", data: data.dailyOverall.totalCylinders },
      { label: "Filled Cylinders", data: data.dailyOverall.totalFilled },
      { label: "Rejected Cylinders", data: data.dailyOverall.totalRejected },
      {
        label: "Completed Processes",
        data: data.dailyOverall.completedProcesses,
      },
      { label: "Success Rate (%)", data: data.dailyOverall.successRate },
    ];

    rows.forEach((row) => {
      const tr = document.createElement("tr");
      tr.appendChild(this.createCell("td", row.label, "font-weight: bold"));
      row.data.forEach((value) => {
        tr.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(tr);
    });

    table.appendChild(tbody);
    return table;
  }

  renderFillingLineReport(data) {
    const container = document.getElementById("fillingLineContent");

    if (!data || !data.lineOverview || data.lineOverview.length === 0) {
      container.innerHTML =
        '<p class="no-data">No line data available for the selected period</p>';
      return;
    }

    const table = this.createFillingLineTable(data);
    container.innerHTML = "";
    container.appendChild(table);
  }

  createFillingLineTable(data) {
    const table = document.createElement("table");
    table.className = "report-table";

    // Create header
    const thead = document.createElement("thead");
    const headerRow1 = document.createElement("tr");
    const headerRow2 = document.createElement("tr");

    // First row headers
    headerRow1.appendChild(this.createCell("th", "Sr"));
    headerRow1.appendChild(this.createCell("th", "Line"));
    headerRow1.appendChild(this.createCell("th", "Type"));
    headerRow1.appendChild(this.createCell("th", ""));

    let currentMonth = "";
    data.headers.forEach((header) => {
      const cell = this.createCell(
        "th",
        header.monthYear !== currentMonth ? header.monthYear : ""
      );
      headerRow1.appendChild(cell);
      currentMonth = header.monthYear;
    });

    // Second row headers
    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", "Metric"));
    data.headers.forEach((header) => {
      headerRow2.appendChild(this.createCell("th", header.day));
    });

    thead.appendChild(headerRow1);
    thead.appendChild(headerRow2);
    table.appendChild(thead);

    // Create body
    const tbody = document.createElement("tbody");

    data.lineOverview.forEach((line) => {
      // Processes row
      const processRow = document.createElement("tr");
      processRow.appendChild(
        this.createCell("td", line.sr, "font-weight: bold")
      );
      processRow.appendChild(
        this.createCell(
          "td",
          line.lineName,
          "font-weight: bold; text-align: left"
        )
      );
      processRow.appendChild(
        this.createCell("td", line.lineType, "text-align: left")
      );
      processRow.appendChild(
        this.createCell("td", "Processes", "text-align: left")
      );
      line.processes.forEach((value) => {
        processRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(processRow);

      // Cylinders row
      const cylindersRow = document.createElement("tr");
      cylindersRow.appendChild(this.createCell("td", ""));
      cylindersRow.appendChild(this.createCell("td", ""));
      cylindersRow.appendChild(this.createCell("td", ""));
      cylindersRow.appendChild(
        this.createCell("td", "Cylinders", "text-align: left")
      );
      line.cylinders.forEach((value) => {
        cylindersRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(cylindersRow);

      // Success Rate row
      const successRow = document.createElement("tr");
      successRow.appendChild(this.createCell("td", ""));
      successRow.appendChild(this.createCell("td", ""));
      successRow.appendChild(this.createCell("td", ""));
      successRow.appendChild(
        this.createCell(
          "td",
          "Success %",
          "text-align: left; font-weight: bold"
        )
      );
      line.successRate.forEach((value) => {
        successRow.appendChild(
          this.createCell("td", value + "%", "font-weight: bold")
        );
      });
      tbody.appendChild(successRow);
    });

    table.appendChild(tbody);
    return table;
  }

  renderFillingStaffReport(data) {
    const container = document.getElementById("fillingStaffContent");

    if (!data || !data.staffOverview || data.staffOverview.length === 0) {
      container.innerHTML =
        '<p class="no-data">No staff data available for the selected period</p>';
      return;
    }

    const table = this.createFillingStaffTable(data);
    container.innerHTML = "";
    container.appendChild(table);
  }

  createFillingStaffTable(data) {
    const table = document.createElement("table");
    table.className = "report-table";

    // Create header (similar to line table)
    const thead = document.createElement("thead");
    const headerRow1 = document.createElement("tr");
    const headerRow2 = document.createElement("tr");

    headerRow1.appendChild(this.createCell("th", "Sr"));
    headerRow1.appendChild(this.createCell("th", "Staff"));
    headerRow1.appendChild(this.createCell("th", "Role"));
    headerRow1.appendChild(this.createCell("th", ""));

    let currentMonth = "";
    data.headers.forEach((header) => {
      const cell = this.createCell(
        "th",
        header.monthYear !== currentMonth ? header.monthYear : ""
      );
      headerRow1.appendChild(cell);
      currentMonth = header.monthYear;
    });

    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", ""));
    headerRow2.appendChild(this.createCell("th", "Metric"));
    data.headers.forEach((header) => {
      headerRow2.appendChild(this.createCell("th", header.day));
    });

    thead.appendChild(headerRow1);
    thead.appendChild(headerRow2);
    table.appendChild(thead);

    // Create body
    const tbody = document.createElement("tbody");

    data.staffOverview.forEach((staff) => {
      // Processes row
      const processRow = document.createElement("tr");
      processRow.appendChild(
        this.createCell("td", staff.sr, "font-weight: bold")
      );
      processRow.appendChild(
        this.createCell(
          "td",
          staff.staffName,
          "font-weight: bold; text-align: left"
        )
      );
      processRow.appendChild(
        this.createCell("td", staff.staffRole, "text-align: left")
      );
      processRow.appendChild(
        this.createCell("td", "Processes", "text-align: left")
      );
      staff.processes.forEach((value) => {
        processRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(processRow);

      // Cylinders row
      const cylindersRow = document.createElement("tr");
      cylindersRow.appendChild(this.createCell("td", ""));
      cylindersRow.appendChild(this.createCell("td", ""));
      cylindersRow.appendChild(this.createCell("td", ""));
      cylindersRow.appendChild(
        this.createCell("td", "Cylinders", "text-align: left")
      );
      staff.cylinders.forEach((value) => {
        cylindersRow.appendChild(this.createCell("td", value));
      });
      tbody.appendChild(cylindersRow);

      // Success Rate row
      const successRow = document.createElement("tr");
      successRow.appendChild(this.createCell("td", ""));
      successRow.appendChild(this.createCell("td", ""));
      successRow.appendChild(this.createCell("td", ""));
      successRow.appendChild(
        this.createCell(
          "td",
          "Success %",
          "text-align: left; font-weight: bold"
        )
      );
      staff.successRate.forEach((value) => {
        successRow.appendChild(
          this.createCell("td", value + "%", "font-weight: bold")
        );
      });
      tbody.appendChild(successRow);
    });

    table.appendChild(tbody);
    return table;
  }

  createCell(type, content, style = "") {
    const cell = document.createElement(type);
    cell.textContent = content;
    if (style) {
      cell.style.cssText = style;
    }
    return cell;
  }

  async exportReport(reportType) {
    if (!window.authManager.isAuthenticated()) {
      alert("Please login first");
      return;
    }

    const startDate = document.getElementById("startDate").value;
    const endDate = document.getElementById("endDate").value;

    if (!startDate || !endDate) {
      alert("Please select date range and load reports first");
      return;
    }

    const endpoints = {
      overview: "export/daily-overview",
      customer: "export/daily-by-customer",
      customerType: "export/daily-by-customer-type",
      filling: "export/filling-overview",
      fillingLine: "export/filling-overview",
      fillingStaff: "export/filling-overview",
    };

    const endpoint = endpoints[reportType];
    if (!endpoint) {
      alert("Invalid report type");
      return;
    }

    try {
      window.authManager.showLoading(true);

      const response = await fetch(
        `/public/${endpoint}?startDate=${startDate}&endDate=${endDate}`,
        {
          headers: window.authManager.getAuthHeader(),
        }
      );

      if (!response.ok) {
        throw new Error("Export failed");
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${reportType}_report_${startDate}_to_${endDate}.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export error:", error);
      alert("Export failed. Please try again.");
    } finally {
      window.authManager.showLoading(false);
    }
  }
}

// Initialize reports manager when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.reportsManager = new ReportsManager();
});
