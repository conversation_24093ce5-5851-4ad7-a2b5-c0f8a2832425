// Admin Management JavaScript
class AdminManager {
    constructor() {
        this.currentUser = null;
        this.users = [];
        this.stats = null;
        this.init();
    }

    init() {
        // Check authentication
        if (!window.authManager || !window.authManager.isAuthenticated()) {
            window.location.href = '/';
            return;
        }

        this.currentUser = window.authManager.getCurrentUser();
        
        // Check if user has admin privileges
        if (!['root', 'admin'].includes(this.currentUser.role)) {
            alert('Access denied. Admin privileges required.');
            window.location.href = '/';
            return;
        }

        this.setupEventListeners();
        this.updateUI();
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.admin-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Back to reports button
        document.getElementById('backToReports').addEventListener('click', () => {
            window.location.href = '/';
        });

        // Create user form
        document.getElementById('createUserForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createUser();
        });

        // Load users button
        document.getElementById('loadUsers').addEventListener('click', () => {
            this.loadUsers();
        });

        // Load stats button
        document.getElementById('loadStats').addEventListener('click', () => {
            this.loadStats();
        });

        // Filter changes
        document.getElementById('roleFilter').addEventListener('change', () => {
            this.filterUsers();
        });

        document.getElementById('statusFilter').addEventListener('change', () => {
            this.filterUsers();
        });
    }

    updateUI() {
        // Update user info
        const userInfo = document.getElementById('adminUserInfo');
        if (userInfo && this.currentUser) {
            userInfo.textContent = `${this.currentUser.name} (${this.currentUser.role})`;
        }

        // Show root option if user is root
        if (this.currentUser.role === 'root') {
            document.getElementById('rootOption').style.display = 'block';
        }
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.admin-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
        });
        document.getElementById(`${tabName}Tab`).classList.remove('hidden');
    }

    async createUser() {
        try {
            this.showLoading(true);
            this.clearAlert('createAlert');

            const formData = new FormData(document.getElementById('createUserForm'));
            const userData = Object.fromEntries(formData.entries());

            const response = await fetch('/api/admin/create-user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...window.authManager.getAuthHeader()
                },
                body: JSON.stringify(userData)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showAlert('createAlert', 'User created successfully!', 'success');
                document.getElementById('createUserForm').reset();
            } else {
                this.showAlert('createAlert', result.message || 'Failed to create user', 'error');
            }

        } catch (error) {
            console.error('Create user error:', error);
            this.showAlert('createAlert', 'Network error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadUsers() {
        try {
            this.showLoading(true);
            this.clearAlert('manageAlert');

            const response = await fetch('/api/admin/users', {
                headers: window.authManager.getAuthHeader()
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.users = result.users;
                this.renderUsersTable();
            } else {
                this.showAlert('manageAlert', result.message || 'Failed to load users', 'error');
            }

        } catch (error) {
            console.error('Load users error:', error);
            this.showAlert('manageAlert', 'Network error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    renderUsersTable() {
        const container = document.getElementById('usersTableContainer');
        
        if (!this.users || this.users.length === 0) {
            container.innerHTML = '<p>No users found</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'users-table';

        // Create header
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Status</th>
                <th>Created</th>
                <th>Actions</th>
            </tr>
        `;
        table.appendChild(thead);

        // Create body
        const tbody = document.createElement('tbody');
        this.users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.name}</td>
                <td>${user.email}</td>
                <td><span class="role-badge role-${user.role}">${user.role}</span></td>
                <td>
                    ${user.banned ? '<span class="status-badge status-banned">Banned</span>' : 
                      user.verified ? '<span class="status-badge status-verified">Verified</span>' : 
                      '<span class="status-badge status-unverified">Unverified</span>'}
                </td>
                <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                <td>
                    ${!user.verified ? `<button class="btn btn-success" onclick="adminManager.verifyUser('${user._id}')">Verify</button>` : ''}
                    ${!user.banned ? `<button class="btn btn-danger" onclick="adminManager.banUser('${user._id}', true)">Ban</button>` : 
                      `<button class="btn btn-success" onclick="adminManager.banUser('${user._id}', false)">Unban</button>`}
                    ${this.currentUser.role === 'root' ? `<button class="btn btn-danger" onclick="adminManager.deleteUser('${user._id}')">Delete</button>` : ''}
                </td>
            `;
            tbody.appendChild(row);
        });
        table.appendChild(tbody);

        container.innerHTML = '';
        container.appendChild(table);
    }

    filterUsers() {
        const roleFilter = document.getElementById('roleFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        let filteredUsers = [...this.users];

        if (roleFilter) {
            filteredUsers = filteredUsers.filter(user => user.role === roleFilter);
        }

        if (statusFilter) {
            if (statusFilter === 'verified') {
                filteredUsers = filteredUsers.filter(user => user.verified && !user.banned);
            } else if (statusFilter === 'unverified') {
                filteredUsers = filteredUsers.filter(user => !user.verified);
            } else if (statusFilter === 'banned') {
                filteredUsers = filteredUsers.filter(user => user.banned);
            }
        }

        // Temporarily store original users and render filtered
        const originalUsers = this.users;
        this.users = filteredUsers;
        this.renderUsersTable();
        this.users = originalUsers;
    }

    async verifyUser(userId) {
        try {
            this.showLoading(true);

            const response = await fetch(`/api/admin/users/${userId}/verify`, {
                method: 'PUT',
                headers: window.authManager.getAuthHeader()
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showAlert('manageAlert', 'User verified successfully!', 'success');
                this.loadUsers(); // Reload users
            } else {
                this.showAlert('manageAlert', result.message || 'Failed to verify user', 'error');
            }

        } catch (error) {
            console.error('Verify user error:', error);
            this.showAlert('manageAlert', 'Network error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async banUser(userId, banned) {
        try {
            if (!confirm(`Are you sure you want to ${banned ? 'ban' : 'unban'} this user?`)) {
                return;
            }

            this.showLoading(true);

            const response = await fetch(`/api/admin/users/${userId}/ban`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    ...window.authManager.getAuthHeader()
                },
                body: JSON.stringify({ banned })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showAlert('manageAlert', `User ${banned ? 'banned' : 'unbanned'} successfully!`, 'success');
                this.loadUsers(); // Reload users
            } else {
                this.showAlert('manageAlert', result.message || `Failed to ${banned ? 'ban' : 'unban'} user`, 'error');
            }

        } catch (error) {
            console.error('Ban user error:', error);
            this.showAlert('manageAlert', 'Network error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async deleteUser(userId) {
        try {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }

            this.showLoading(true);

            const response = await fetch(`/api/admin/users/${userId}`, {
                method: 'DELETE',
                headers: window.authManager.getAuthHeader()
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showAlert('manageAlert', 'User deleted successfully!', 'success');
                this.loadUsers(); // Reload users
            } else {
                this.showAlert('manageAlert', result.message || 'Failed to delete user', 'error');
            }

        } catch (error) {
            console.error('Delete user error:', error);
            this.showAlert('manageAlert', 'Network error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadStats() {
        try {
            this.showLoading(true);
            this.clearAlert('statsAlert');

            const response = await fetch('/api/admin/stats', {
                headers: window.authManager.getAuthHeader()
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.stats = result.stats;
                this.renderStats();
            } else {
                this.showAlert('statsAlert', result.message || 'Failed to load statistics', 'error');
            }

        } catch (error) {
            console.error('Load stats error:', error);
            this.showAlert('statsAlert', 'Network error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    renderStats() {
        const container = document.getElementById('statsContainer');
        
        if (!this.stats) {
            container.innerHTML = '<p>No statistics available</p>';
            return;
        }

        container.innerHTML = `
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${this.stats.total}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${this.stats.verified}</div>
                    <div class="stat-label">Verified Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${this.stats.banned}</div>
                    <div class="stat-label">Banned Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${this.stats.active}</div>
                    <div class="stat-label">Active Users</div>
                </div>
            </div>
            
            <h3>Role Distribution</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">${this.stats.roleBreakdown.root}</div>
                    <div class="stat-label">Root Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${this.stats.roleBreakdown.admin}</div>
                    <div class="stat-label">Admin Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${this.stats.roleBreakdown.manager}</div>
                    <div class="stat-label">Manager Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${this.stats.roleBreakdown.sale}</div>
                    <div class="stat-label">Sale Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${this.stats.roleBreakdown.fill}</div>
                    <div class="stat-label">Fill Users</div>
                </div>
            </div>
        `;
    }

    showAlert(containerId, message, type) {
        const container = document.getElementById(containerId);
        container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    }

    clearAlert(containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
    }

    showLoading(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = show ? 'flex' : 'none';
        }
    }
}

// Initialize admin manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminManager = new AdminManager();
});
