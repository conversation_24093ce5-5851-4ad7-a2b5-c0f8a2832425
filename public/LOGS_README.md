# System Logs Viewer - Quick Start Guide

## 🚀 Getting Started

### Access the Logs
1. **Login**: Go to `http://localhost:4000` and login with your credentials
2. **Check Permissions**: Logs button only appears for root, admin, manager, fill roles
3. **Navigate**: Click the purple "Logs" button in the header
4. **Load Data**: Click "Load Logs" to view system logs

### First Time Use
1. Click "Load Logs" to see recent system activity
2. Use filters to find specific types of logs
3. Click "View" on any log to see detailed information

## 🔧 Quick Actions

### View All Recent Logs
```
1. Click "Load Logs"
2. Browse through the table
3. Use pagination to see more logs
```

### Filter by Log Type
```
1. Select log type from dropdown (Error, Warning, Info, etc.)
2. Logs automatically filter
3. Statistics update to show filtered results
```

### Filter by Date Range
```
1. Set Start Date (defaults to 1 week ago)
2. Set End Date (defaults to today)
3. Click "Load Logs" to apply date filter
```

### Export Logs to CSV
```
1. Apply any desired filters
2. Click "Export" button
3. CSV file downloads automatically
4. Open in Excel or other spreadsheet application
```

### View Log Details
```
1. Find log in table
2. Click "View" button (eye icon)
3. Modal opens with complete information
4. Click X to close
```

## 📊 Understanding the Interface

### Statistics Dashboard
At the top of the page, you'll see:
- **Total Logs**: Total count in system
- **Today**: Logs created today
- **Errors**: Error-type logs count
- **Warnings**: Warning-type logs count
- **Active Users**: Unique users who created logs

### Log Type Badges
- 🔵 **Info**: General information
- 🟡 **Warning**: Attention needed
- 🔴 **Error**: Problems occurred
- 🟢 **Success**: Operations completed
- ⚫ **Debug**: Technical information
- 🔵 **System**: System events
- 🔷 **User**: User actions
- 🔴 **Security**: Security events
- 🟢 **Audit**: Audit trail

### Table Columns
- **Type**: Log category with color badge
- **Date & Time**: When the log was created
- **User**: Who created the log (with avatar)
- **Remarks**: Log message (click expand for long messages)
- **Actions**: View detailed information

## 🔍 Filter Options

### Available Filters
- **Log Type**: Info, Warning, Error, Success, Debug, System, User, Security, Audit
- **Date Range**: Start and end dates
- **Sort Order**: Newest first (default) or Oldest first
- **Results Limit**: 10, 25, 50, or 100 logs per page

### Filter Tips
- **Combine Filters**: Use multiple filters together for precise results
- **Date Ranges**: Use specific date ranges for better performance
- **Clear Filters**: Use "Clear Filters" button to reset everything
- **Export Filtered**: Export button exports only filtered results

## 🔐 Permissions

### Who Can Access Logs
- ✅ **Root**: Full access to all logs
- ✅ **Admin**: Full access to all logs
- ✅ **Manager**: Access to relevant logs
- ✅ **Fill**: Access to filling-related logs
- ❌ **Sale**: No access to logs

### What You Can Do
- **View Logs**: See all logs you have permission for
- **Filter Logs**: Use all filtering options
- **Export Logs**: Download filtered logs as CSV
- **View Details**: See complete log information

## 📱 Mobile Support

The logs viewer is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🛠️ Troubleshooting

### Common Issues

**"Access denied" Error**
- You don't have permission to view logs
- Contact your administrator for role upgrade

**No Logs Button Visible**
- Button only appears for authorized roles
- Contact admin if you should have access

**No Logs Displayed**
- Check if filters are too restrictive
- Try clearing filters and loading again
- Check date range settings

**Export Not Working**
- Check browser download settings
- Ensure popup blockers aren't interfering
- Try with different browser

**Slow Loading**
- Use date range filters to limit results
- Reduce the number of logs per page
- Check network connection

### Performance Tips
- Use specific date ranges for faster loading
- Filter by log type to reduce data
- Use appropriate result limits (25-50 for normal use)
- Clear browser cache if experiencing issues

## 📋 Log Types Explained

### When to Look for Each Type

**Error Logs**
- System problems
- Failed operations
- Critical issues needing attention

**Warning Logs**
- Potential problems
- Performance issues
- Configuration concerns

**Info Logs**
- Normal operations
- Status updates
- General information

**Success Logs**
- Completed operations
- Successful processes
- Confirmations

**User Logs**
- Login/logout events
- User actions
- Data changes

**System Logs**
- System startup/shutdown
- Configuration changes
- Maintenance activities

**Security Logs**
- Failed login attempts
- Permission changes
- Security events

**Audit Logs**
- Compliance tracking
- Data access logs
- Administrative actions

## 🎯 Best Practices

### Regular Monitoring
- Check logs daily for errors and warnings
- Use date range filters to review specific periods
- Export important logs for record keeping
- Monitor user activity through user logs

### Efficient Usage
- Start with recent logs (default view)
- Use filters to focus on specific issues
- Export filtered data for detailed analysis
- Clear filters between different searches

### Security Awareness
- Monitor security logs regularly
- Report suspicious activity to administrators
- Don't share exported log files inappropriately
- Log out when finished viewing logs

## 📞 Getting Help

If you need assistance:
1. **Check this guide** for common solutions
2. **Contact your system administrator**
3. **Report bugs** with specific error messages
4. **Include your role** when asking for help

## 🔄 Data Refresh

The logs system doesn't auto-refresh. To see the latest logs:
1. Click "Load Logs" button
2. Or refresh the browser page
3. Adjust filters as needed

---

**Need more detailed information?** See the complete documentation in `docs/LOGS_SYSTEM.md`

**System Version**: 1.0.0  
**Last Updated**: December 2024
