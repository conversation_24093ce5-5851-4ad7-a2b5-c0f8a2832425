# Price Management System - Quick Start Guide

## 🚀 Getting Started

### Access the Pricing System
1. **Login**: Go to `http://localhost:4000` and login with admin credentials
2. **Check Permissions**: Pricing button only appears for root, admin, manager roles
3. **Navigate**: Click the orange "Pricing" button in the header
4. **Start Managing**: Use the four main tabs to manage pricing

### First Time Use
1. Click "Refresh" in Price Lists tab to load existing price lists
2. Create your first price list if none exist
3. Add price entries for different cylinder specifications
4. Explore customer pricing and bulk operations

## 🏷️ Four Main Sections

### 1. Price Lists 📋
**Manage master pricing strategies**
```
- Create multiple price lists for different scenarios
- Add price entries with pressure, size, customer price, factory price
- Edit, copy, or delete price lists
- View detailed price information
```

### 2. Customer Pricing 👥
**Individual customer price overrides**
```
- View all customers with their pricing information
- Set custom prices for specific customers
- Override default price list prices
- Filter customers by type or search by name
```

### 3. Bulk Operations ⚙️
**Mass price updates with preview**
```
- Increase/decrease prices by percentage
- Set fixed prices across multiple lists
- Filter by cylinder size
- Preview changes before applying
```

### 4. Price Analysis 📊
**Comprehensive pricing analytics**
```
- Compare prices across different price lists
- Analyze profit margins
- Generate detailed reports
- Export analysis data to CSV
```

## 🔧 Quick Actions

### Create a New Price List
```
1. Go to "Price Lists" tab
2. Click "Add Price List"
3. Enter price list name
4. Click "Add Price Entry"
5. Fill in: Pressure (PSI), Size (L), Customer Price, Factory Price
6. Add more entries as needed
7. Click "Save Price List"
```

### Set Customer-Specific Pricing
```
1. Go to "Customer Pricing" tab
2. Click "Refresh" to load customers
3. Find customer and click "Edit"
4. Click "Add Custom Price"
5. Enter pressure, size, and custom price
6. Custom price will override default pricing
```

### Bulk Price Increase
```
1. Go to "Bulk Operations" tab
2. Select "Increase Prices"
3. Choose target (all lists or specific)
4. Enter percentage (e.g., 10 for 10% increase)
5. Click "Preview Changes"
6. Review the changes table
7. Click "Execute Operation" to apply
```

### Generate Margin Analysis
```
1. Go to "Price Analysis" tab
2. Select "Profit Margins" from dropdown
3. Click "Generate Analysis"
4. Review margin analysis table
5. Click "Export" to download CSV
```

## 📊 Understanding the Interface

### Statistics Dashboard
At the top of the page, you'll see:
- **Price Lists**: Total number of price lists
- **Price Entries**: Total individual price entries
- **Avg Customer Price**: Average price charged to customers
- **Avg Factory Price**: Average internal cost
- **Avg Margin**: Average profit margin percentage

### Price List Cards
Each price list is displayed as a card showing:
- **Price List Name**: Custom name for the pricing strategy
- **Price Entries**: All cylinder specifications with prices
- **Action Buttons**: View, Edit, Copy, Delete options
- **Price Details**: Customer price and factory price for each entry

### Customer Cards
Customer information displayed with:
- **Customer Name**: Business or individual name
- **Customer Type**: Hospital, Individual, Shop, Factory, Workshop
- **Default Price List**: Assigned pricing strategy
- **Custom Prices**: Number of customer-specific price overrides

## 🔍 Search & Filter Options

### Price Lists
- **Search**: Find price lists by name
- **Quick Actions**: Add, refresh, and manage price lists

### Customers
- **Search**: Find customers by name
- **Type Filter**: Filter by customer type
- **Quick View**: See pricing information at a glance

### Bulk Operations
- **Operation Type**: Increase, decrease, set, or copy
- **Target Selection**: All lists, specific lists, or customers
- **Size Filter**: Apply only to specific cylinder sizes
- **Value Input**: Percentage or fixed amount

### Analysis
- **Analysis Type**: Comparison, trends, margins, customer analysis
- **Export Options**: Download analysis as CSV
- **Visual Tables**: Sortable and filterable data tables

## 🔐 Permissions & Security

### Who Can Access
- ✅ **Root**: Full access to all pricing functions
- ✅ **Admin**: Full access to all pricing functions  
- ✅ **Manager**: Full access to all pricing functions
- ❌ **Sale**: No access to pricing management
- ❌ **Fill**: No access to pricing management

### What You Can Do
- **View Pricing**: See all price lists and customer pricing
- **Create/Edit**: Add and modify price lists and entries
- **Bulk Operations**: Mass price updates with preview
- **Analysis**: Generate reports and export data
- **Customer Pricing**: Set individual customer prices

### Special Permissions
- **Delete Price Lists**: Only root and admin can delete
- **Bulk Operations**: All admin roles can perform
- **Customer Pricing**: All admin roles can modify

## 💡 Pro Tips

### Efficient Price Management
- **Use Templates**: Copy existing price lists as starting points
- **Bulk Operations**: Use for efficiency when updating many prices
- **Preview First**: Always preview bulk changes before applying
- **Regular Reviews**: Check profit margins regularly

### Customer Pricing Strategy
- **Default Lists**: Assign appropriate default price lists to customers
- **Custom Overrides**: Use sparingly for special customers
- **Type-Based Pricing**: Consider different strategies for different customer types
- **Monitor Impact**: Use analysis to see effectiveness of pricing

### Analysis & Reporting
- **Regular Analysis**: Generate margin analysis monthly
- **Export Data**: Keep CSV exports for historical records
- **Compare Strategies**: Use price comparison to optimize pricing
- **Track Changes**: Monitor pricing changes over time

## 🛠️ Troubleshooting

### Common Issues

**"Access denied" Error**
- You don't have permission to manage pricing
- Contact administrator for role upgrade

**No Pricing Button Visible**
- Button only appears for admin roles
- Contact admin if you should have access

**Price Lists Not Loading**
- Click "Refresh" button to reload data
- Check network connection
- Contact admin if problem persists

**Bulk Operations Failing**
- Check all required fields are filled
- Ensure percentage values are reasonable
- Try smaller batches if dealing with large datasets

**Analysis Not Generating**
- Ensure price lists exist with price entries
- Check that you have data to analyze
- Try refreshing the page

### Performance Tips
- Use search and filters to work with smaller datasets
- Clear browser cache if experiencing slowdowns
- Use bulk operations instead of individual changes
- Export large datasets rather than viewing in browser

## 📋 Data Structure

### Price List Structure
```
Price List
├── Name (e.g., "Standard Pricing 2024")
├── Price Entries
│   ├── Pressure (PSI)
│   ├── Size (Liters)
│   ├── Customer Price ($)
│   └── Factory Price ($)
└── Owner (Factory/Organization)
```

### Customer Pricing Structure
```
Customer
├── Basic Info (Name, Type, Contact)
├── Default Price List
├── Custom Prices (Optional)
│   ├── Pressure (PSI)
│   ├── Size (Liters)
│   └── Custom Price ($)
└── Price History
```

## 🎯 Best Practices

### Price List Management
- Use descriptive names for price lists
- Keep factory prices updated for accurate margins
- Regular review and update of customer prices
- Archive old price lists rather than deleting

### Customer Pricing
- Use default price lists for most customers
- Custom prices only for special cases
- Document reasons for custom pricing
- Regular review of customer-specific prices

### Bulk Operations
- Always preview changes before applying
- Start with small test batches
- Document major price changes
- Coordinate with sales team before major updates

### Analysis & Reporting
- Generate regular margin reports
- Export data for external analysis
- Share insights with management team
- Use data to inform pricing strategy

## 📞 Getting Help

If you need assistance:
1. **Check this guide** for common solutions
2. **Contact your system administrator**
3. **Report bugs** with specific error messages
4. **Include your role** when asking for help
5. **Provide screenshots** of any issues

## 🔄 Data Backup

The pricing system automatically:
- Logs all pricing changes
- Tracks user who made changes
- Maintains audit trail
- Backs up pricing data regularly

**Manual Backups**: Use export functions to create your own backups

---

**Need more detailed information?** See the complete documentation in `docs/PRICING_SYSTEM.md`

**System Version**: 1.0.0  
**Last Updated**: December 2024

**Quick Access URLs**:
- Main System: `http://localhost:4000`
- Pricing Management: `http://localhost:4000/pricing.html`
- Reports Dashboard: `http://localhost:4000/index.html`
