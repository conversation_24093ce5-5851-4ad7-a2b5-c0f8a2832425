<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Logs - Cylinder System</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .logs-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .logs-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logs-controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .controls-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .logs-table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .logs-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .logs-table th,
        .logs-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .logs-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .logs-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .log-type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .log-type-info { background: #d1ecf1; color: #0c5460; }
        .log-type-warning { background: #fff3cd; color: #856404; }
        .log-type-error { background: #f8d7da; color: #721c24; }
        .log-type-success { background: #d4edda; color: #155724; }
        .log-type-debug { background: #e2e3e5; color: #383d41; }
        .log-type-system { background: #cce5ff; color: #004085; }
        .log-type-user { background: #e7f3ff; color: #0056b3; }
        .log-type-security { background: #f5c6cb; color: #721c24; }
        .log-type-audit { background: #d4edda; color: #155724; }
        .log-type-default { background: #e9ecef; color: #495057; }
        
        .staff-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .staff-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
        }
        
        .staff-details {
            display: flex;
            flex-direction: column;
        }
        
        .staff-name {
            font-weight: 500;
            color: #333;
        }
        
        .staff-role {
            font-size: 0.8rem;
            color: #666;
            text-transform: capitalize;
        }
        
        .log-date {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #666;
        }
        
        .log-remarks {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .log-remarks.expanded {
            white-space: normal;
            max-width: none;
        }
        
        .expand-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 0.8rem;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .expand-btn:hover {
            background: #f8f9fa;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: white;
            border-radius: 0 0 10px 10px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover:not(:disabled) {
            background: #f8f9fa;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .pagination .current-page {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            min-width: 120px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .hidden {
            display: none;
        }
        
        .log-details-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .detail-group {
            margin-bottom: 20px;
        }
        
        .detail-label {
            font-weight: 600;
            color: #555;
            margin-bottom: 8px;
            display: block;
        }
        
        .detail-value {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        
        @media (max-width: 768px) {
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                flex-direction: column;
                width: 100%;
            }
            
            .logs-table {
                font-size: 0.9rem;
            }
            
            .staff-info {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="logs-container">
        <div class="logs-header">
            <div>
                <h1><i class="fas fa-file-alt"></i> System Logs</h1>
                <p>View and monitor system activity logs</p>
            </div>
            <div>
                <span id="userInfo" class="user-info"></span>
                <button id="backToReports" class="btn btn-primary">
                    <i class="fas fa-chart-line"></i>
                    Back to Reports
                </button>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="stats-row" id="statsRow">
            <!-- Stats will be populated by JavaScript -->
        </div>

        <!-- Controls -->
        <div class="logs-controls">
            <div class="controls-row">
                <div class="filter-group">
                    <select id="logTypeFilter" hidden >
                        <option value="">All Log Types</option>
                        <option value="info">Info</option>
                        <option value="warning">Warning</option>
                        <option value="error">Error</option>
                        <option value="success">Success</option>
                        <option value="debug">Debug</option>
                        <option value="system">System</option>
                        <option value="user">User</option>
                        <option value="security">Security</option>
                        <option value="audit">Audit</option>
                    </select>
                    
                    <select id="sortOrder">
                        <option value="desc">Newest First</option>
                        <option value="asc">Oldest First</option>
                    </select>
                    
                    <select id="limitSelect">
                        <option value="10">10 logs</option>
                        <option value="25" selected>25 logs</option>
                        <option value="50">50 logs</option>
                        <option value="100">100 logs</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <input type="date" id="startDate" placeholder="Start Date">
                    <input type="date" id="endDate" placeholder="End Date">
                </div>
                
                <div class="filter-group">
                    <button id="loadLogs" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        Load Logs
                    </button>
                    
                    <button id="clearFilters" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Clear Filters
                    </button>
                    
                    <button id="exportLogs" class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Alert Container -->
        <div id="alertContainer"></div>

        <!-- Logs Table -->
        <div class="logs-table-container">
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Date & Time</th>
                        <th>User</th>
                        <th>Remarks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="logsTableBody">
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 40px;">
                            Click "Load Logs" to view system logs
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <div class="pagination" id="pagination">
                <button id="prevPage" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <span id="pageInfo">Page 1 of 1</span>
                <button id="nextPage" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Log Details Modal -->
    <div id="logDetailsModal" class="log-details-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-info-circle"></i> Log Details</h2>
                <span class="close">&times;</span>
            </div>
            <div id="logDetailsContent">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading logs...</p>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/logs.js"></script>
</body>
</html>
