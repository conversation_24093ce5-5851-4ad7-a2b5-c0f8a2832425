<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Management - Cylinder System</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .admin-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .admin-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
        }
        
        .admin-tab:hover {
            background: #f8f9fa;
            color: #333;
        }
        
        .admin-tab.active {
            background: #667eea;
            color: white;
        }
        
        .admin-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        .admin-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .users-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .users-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-verified {
            background: #d4edda;
            color: #155724;
        }
        
        .status-unverified {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-banned {
            background: #f5c6cb;
            color: #721c24;
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .role-root {
            background: #dc3545;
            color: white;
        }
        
        .role-admin {
            background: #667eea;
            color: white;
        }
        
        .role-manager {
            background: #28a745;
            color: white;
        }
        
        .role-sale {
            background: #ffc107;
            color: #212529;
        }
        
        .role-fill {
            background: #17a2b8;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .hidden {
            display: none;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div>
                <h1><i class="fas fa-users-cog"></i> Admin Management</h1>
                <p>Manage users and system administration</p>
            </div>
            <div>
                <span id="adminUserInfo" class="user-info"></span>
                <button id="backToReports" class="btn btn-primary">
                    <i class="fas fa-chart-line"></i>
                    Back to Reports
                </button>
            </div>
        </div>

        <div class="admin-tabs">
            <button class="admin-tab active" data-tab="create">
                <i class="fas fa-user-plus"></i>
                Create User
            </button>
            <button class="admin-tab" data-tab="manage">
                <i class="fas fa-users"></i>
                Manage Users
            </button>
            <button class="admin-tab" data-tab="stats">
                <i class="fas fa-chart-pie"></i>
                Statistics
            </button>
        </div>

        <div class="admin-content">
            <!-- Create User Tab -->
            <div id="createTab" class="tab-content">
                <h2><i class="fas fa-user-plus"></i> Create New User</h2>
                <div id="createAlert"></div>
                
                <form id="createUserForm" class="admin-form">
                    <div class="form-group">
                        <label for="createName">Full Name *</label>
                        <input type="text" id="createName" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="createEmail">Email Address *</label>
                        <input type="email" id="createEmail" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="createPassword">Password *</label>
                        <input type="password" id="createPassword" name="password" required minlength="6">
                    </div>
                    
                    <div class="form-group">
                        <label for="createRole">Role *</label>
                        <select id="createRole" name="role" required>
                            <option value="">Select Role</option>
                            <option value="admin">Admin</option>
                            <option value="manager">Manager</option>
                            <option value="sale">Sale</option>
                            <option value="fill">Fill</option>
                            <option value="root" id="rootOption" style="display: none;">Root</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="createPhone">Phone Number</label>
                        <input type="tel" id="createPhone" name="phoneNumber">
                    </div>
                    
                    <div class="form-group">
                        <label for="createAddress">Address</label>
                        <input type="text" id="createAddress" name="address">
                    </div>
                </form>
                
                <button type="submit" form="createUserForm" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    Create User
                </button>
            </div>

            <!-- Manage Users Tab -->
            <div id="manageTab" class="tab-content hidden">
                <h2><i class="fas fa-users"></i> Manage Users</h2>
                <div id="manageAlert"></div>
                
                <div style="margin-bottom: 20px;">
                    <button id="loadUsers" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        Load Users
                    </button>
                    
                    <select id="roleFilter" style="margin-left: 10px; padding: 8px;">
                        <option value="">All Roles</option>
                        <option value="root">Root</option>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="sale">Sale</option>
                        <option value="fill">Fill</option>
                    </select>
                    
                    <select id="statusFilter" style="margin-left: 10px; padding: 8px;">
                        <option value="">All Status</option>
                        <option value="verified">Verified</option>
                        <option value="unverified">Unverified</option>
                        <option value="banned">Banned</option>
                    </select>
                </div>
                
                <div id="usersTableContainer">
                    <p>Click "Load Users" to view users</p>
                </div>
            </div>

            <!-- Statistics Tab -->
            <div id="statsTab" class="tab-content hidden">
                <h2><i class="fas fa-chart-pie"></i> User Statistics</h2>
                <div id="statsAlert"></div>
                
                <button id="loadStats" class="btn btn-primary" style="margin-bottom: 20px;">
                    <i class="fas fa-sync-alt"></i>
                    Load Statistics
                </button>
                
                <div id="statsContainer">
                    <p>Click "Load Statistics" to view data</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
