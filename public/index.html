<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cylinder Management - Reports</title>
    <link rel="stylesheet" href="css/style.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container">
      <!-- Login Section -->
      <div id="loginSection" class="login-section">
        <div class="login-card">
          <div class="login-header">
            <i class="fas fa-gas-pump"></i>
            <h1>Cylinder Management System</h1>
            <p>Reports Dashboard</p>
          </div>

          <form id="loginForm" class="login-form">
            <div class="form-group">
              <label for="email">
                <i class="fas fa-envelope"></i>
                Email
              </label>
              <input type="email" id="email" name="email" required />
            </div>

            <div class="form-group">
              <label for="password">
                <i class="fas fa-lock"></i>
                Password
              </label>
              <input type="password" id="password" name="password" required />
            </div>

            <button type="submit" class="login-btn">
              <i class="fas fa-sign-in-alt"></i>
              Login
            </button>
          </form>

          <div
            id="loginError"
            class="error-message"
            style="display: none"
          ></div>
        </div>
      </div>

      <!-- Dashboard Section -->
      <div
        id="dashboardSection"
        class="dashboard-section"
        style="display: none"
      >
        <header class="dashboard-header">
          <div class="header-left">
            <h1><i class="fas fa-chart-line"></i> Reports Dashboard</h1>
          </div>
          <div class="header-right">
            <span id="userInfo" class="user-info"></span>
            <button id="logoutBtn" class="logout-btn">
              <i class="fas fa-sign-out-alt"></i>
              Logout
            </button>
          </div>
        </header>

        <main class="dashboard-main">
          <div class="report-controls">
            <div class="date-controls">
              <div class="form-group">
                <label for="startDate">Start Date:</label>
                <input type="date" id="startDate" name="startDate" />
              </div>
              <div class="form-group">
                <label for="endDate">End Date:</label>
                <input type="date" id="endDate" name="endDate" />
              </div>
              <button id="loadReportsBtn" class="load-btn">
                <i class="fas fa-sync-alt"></i>
                Load Reports
              </button>
            </div>
          </div>

          <div class="reports-container">
            <div class="report-tabs">
              <button class="tab-btn active" data-tab="overview">
                <i class="fas fa-chart-bar"></i>
                Daily Overview
              </button>
              <button class="tab-btn" data-tab="customer">
                <i class="fas fa-users"></i>
                By Customer
              </button>
              <button class="tab-btn" data-tab="customerType">
                <i class="fas fa-tags"></i>
                By Customer Type
              </button>
              <button class="tab-btn" data-tab="filling">
                <i class="fas fa-industry"></i>
                Filling Process
              </button>
              <button class="tab-btn" data-tab="fillingLine">
                <i class="fas fa-cogs"></i>
                By Line
              </button>
              <button class="tab-btn" data-tab="fillingStaff">
                <i class="fas fa-user-hard-hat"></i>
                By Staff
              </button>
            </div>

            <div class="tab-content">
              <div id="overviewTab" class="tab-pane active">
                <div class="report-header">
                  <h2>Daily Delivery Overview</h2>
                  <button id="exportOverview" class="export-btn">
                    <i class="fas fa-download"></i>
                    Export to Excel
                  </button>
                </div>
                <div id="overviewContent" class="report-content">
                  <p class="no-data">
                    Select date range and click "Load Reports" to view data
                  </p>
                </div>
              </div>

              <div id="customerTab" class="tab-pane">
                <div class="report-header">
                  <h2>Daily Report by Customer</h2>
                  <button id="exportCustomer" class="export-btn">
                    <i class="fas fa-download"></i>
                    Export to Excel
                  </button>
                </div>
                <div id="customerContent" class="report-content">
                  <p class="no-data">
                    Select date range and click "Load Reports" to view data
                  </p>
                </div>
              </div>

              <div id="customerTypeTab" class="tab-pane">
                <div class="report-header">
                  <h2>Daily Report by Customer Type</h2>
                  <button id="exportCustomerType" class="export-btn">
                    <i class="fas fa-download"></i>
                    Export to Excel
                  </button>
                </div>
                <div id="customerTypeContent" class="report-content">
                  <p class="no-data">
                    Select date range and click "Load Reports" to view data
                  </p>
                </div>
              </div>

              <div id="fillingTab" class="tab-pane">
                <div class="report-header">
                  <h2>Filling Process Overview</h2>
                  <button id="exportFilling" class="export-btn">
                    <i class="fas fa-download"></i>
                    Export to Excel
                  </button>
                </div>
                <div id="fillingContent" class="report-content">
                  <p class="no-data">
                    Select date range and click "Load Reports" to view data
                  </p>
                </div>
              </div>

              <div id="fillingLineTab" class="tab-pane">
                <div class="report-header">
                  <h2>Filling Process by Line</h2>
                  <button id="exportFillingLine" class="export-btn">
                    <i class="fas fa-download"></i>
                    Export to Excel
                  </button>
                </div>
                <div id="fillingLineContent" class="report-content">
                  <p class="no-data">
                    Select date range and click "Load Reports" to view data
                  </p>
                </div>
              </div>

              <div id="fillingStaffTab" class="tab-pane">
                <div class="report-header">
                  <h2>Filling Process by Staff</h2>
                  <button id="exportFillingStaff" class="export-btn">
                    <i class="fas fa-download"></i>
                    Export to Excel
                  </button>
                </div>
                <div id="fillingStaffContent" class="report-content">
                  <p class="no-data">
                    Select date range and click "Load Reports" to view data
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>

      <!-- Loading Overlay -->
      <div id="loadingOverlay" class="loading-overlay" style="display: none">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading...</p>
        </div>
      </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/reports.js"></script>
  </body>
</html>
