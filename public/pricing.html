<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Management - Cylinder System</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .pricing-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .pricing-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .pricing-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .pricing-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .pricing-tab:hover {
            background: #f8f9fa;
            color: #333;
        }
        
        .pricing-tab.active {
            background: #667eea;
            color: white;
        }
        
        .pricing-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        .price-list-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .price-list-card {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .price-list-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .price-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .price-list-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }
        
        .price-list-actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-view { background: #17a2b8; color: white; }
        .btn-edit { background: #28a745; color: white; }
        .btn-delete { background: #dc3545; color: white; }
        .btn-copy { background: #6f42c1; color: white; }
        
        .btn-small:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .price-entries {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .price-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .price-entry:last-child {
            border-bottom: none;
        }
        
        .price-specs {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .spec-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7rem;
            font-weight: 500;
        }
        
        .spec-pressure {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .spec-size {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .price-values {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 2px;
        }
        
        .customer-price {
            font-weight: 600;
            color: #2e7d32;
        }
        
        .factory-price {
            font-size: 0.8rem;
            color: #666;
        }
        
        .customer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .customer-card {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .customer-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .customer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .customer-name {
            font-weight: 600;
            color: #333;
        }
        
        .customer-type {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .type-hospital { background: #e8f5e8; color: #2e7d32; }
        .type-individual { background: #e3f2fd; color: #1976d2; }
        .type-shop { background: #fff3e0; color: #f57c00; }
        .type-factory { background: #fce4ec; color: #c2185b; }
        .type-workshop { background: #f3e5f5; color: #7b1fa2; }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 10px;
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .price-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .price-table th,
        .price-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .price-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .price-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .editable-price {
            background: none;
            border: 1px solid transparent;
            padding: 4px 8px;
            border-radius: 4px;
            width: 100px;
        }
        
        .editable-price:focus {
            border-color: #667eea;
            outline: none;
            background: white;
        }
        
        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            text-align: center;
            min-width: 120px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .hidden {
            display: none;
        }
        
        .bulk-actions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            min-width: 250px;
        }
        
        @media (max-width: 768px) {
            .pricing-tabs {
                flex-direction: column;
            }
            
            .price-list-grid,
            .customer-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .search-controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="pricing-container">
        <div class="pricing-header">
            <div>
                <h1><i class="fas fa-tags"></i> Price Management</h1>
                <p>Manage pricing lists, customer pricing, and bulk operations</p>
            </div>
            <div>
                <span id="userInfo" class="user-info"></span>
                <button id="backToReports" class="btn btn-primary">
                    <i class="fas fa-chart-line"></i>
                    Back to Reports
                </button>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="stats-row" id="statsRow">
            <!-- Stats will be populated by JavaScript -->
        </div>

        <!-- Pricing Tabs -->
        <div class="pricing-tabs">
            <button class="pricing-tab active" data-tab="price-lists">
                <i class="fas fa-list"></i>
                Price Lists
            </button>
            <button class="pricing-tab" data-tab="customer-pricing">
                <i class="fas fa-users"></i>
                Customer Pricing
            </button>
            <button class="pricing-tab" data-tab="bulk-operations">
                <i class="fas fa-cogs"></i>
                Bulk Operations
            </button>
            <button class="pricing-tab" data-tab="price-analysis">
                <i class="fas fa-chart-bar"></i>
                Price Analysis
            </button>
        </div>

        <div class="pricing-content">
            <!-- Price Lists Tab -->
            <div id="priceListsTab" class="tab-content">
                <div class="search-controls">
                    <input type="text" id="priceListSearch" class="search-input" placeholder="Search price lists...">
                    <button id="addPriceList" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Add Price List
                    </button>
                    <button id="loadPriceLists" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
                
                <div id="alertContainer"></div>
                
                <div id="priceListsContainer" class="price-list-grid">
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                        Click "Refresh" to load price lists
                    </div>
                </div>
            </div>

            <!-- Customer Pricing Tab -->
            <div id="customerPricingTab" class="tab-content hidden">
                <div class="search-controls">
                    <input type="text" id="customerSearch" class="search-input" placeholder="Search customers...">
                    <select id="customerTypeFilter">
                        <option value="">All Customer Types</option>
                        <option value="Hospital">Hospital</option>
                        <option value="Individual">Individual</option>
                        <option value="Shop">Shop</option>
                        <option value="Factory">Factory</option>
                        <option value="Workshop">Workshop</option>
                    </select>
                    <button id="loadCustomers" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
                
                <div id="customerAlertContainer"></div>
                
                <div id="customersContainer" class="customer-grid">
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px;">
                        Click "Refresh" to load customers
                    </div>
                </div>
            </div>

            <!-- Bulk Operations Tab -->
            <div id="bulkOperationsTab" class="tab-content hidden">
                <div class="bulk-actions">
                    <h3><i class="fas fa-cogs"></i> Bulk Price Operations</h3>
                </div>
                
                <div id="bulkAlertContainer"></div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label>Operation Type</label>
                        <select id="bulkOperationType">
                            <option value="">Select Operation</option>
                            <option value="increase">Increase Prices</option>
                            <option value="decrease">Decrease Prices</option>
                            <option value="set">Set Fixed Price</option>
                            <option value="copy">Copy Price List</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Target</label>
                        <select id="bulkTarget">
                            <option value="">Select Target</option>
                            <option value="all">All Price Lists</option>
                            <option value="specific">Specific Price List</option>
                            <option value="customers">Customer Prices</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Value/Percentage</label>
                        <input type="number" id="bulkValue" placeholder="Enter value or percentage">
                    </div>
                    
                    <div class="form-group">
                        <label>Filter by Size (Optional)</label>
                        <select id="bulkSizeFilter">
                            <option value="">All Sizes</option>
                            <option value="10">10L</option>
                            <option value="15">15L</option>
                            <option value="40">40L</option>
                            <option value="47">47L</option>
                        </select>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <button id="previewBulkOperation" class="btn btn-secondary">
                        <i class="fas fa-eye"></i>
                        Preview Changes
                    </button>
                    <button id="executeBulkOperation" class="btn btn-primary">
                        <i class="fas fa-play"></i>
                        Execute Operation
                    </button>
                </div>
                
                <div id="bulkPreviewContainer" class="hidden" style="margin-top: 20px;">
                    <!-- Preview results will be shown here -->
                </div>
            </div>

            <!-- Price Analysis Tab -->
            <div id="priceAnalysisTab" class="tab-content hidden">
                <div class="search-controls">
                    <select id="analysisType">
                        <option value="comparison">Price Comparison</option>
                        <option value="trends">Price Trends</option>
                        <option value="margins">Profit Margins</option>
                        <option value="customer-analysis">Customer Analysis</option>
                    </select>
                    <button id="generateAnalysis" class="btn btn-primary">
                        <i class="fas fa-chart-bar"></i>
                        Generate Analysis
                    </button>
                    <button id="exportAnalysis" class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
                
                <div id="analysisAlertContainer"></div>
                
                <div id="analysisContainer">
                    <div style="text-align: center; padding: 40px;">
                        Select analysis type and click "Generate Analysis"
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Price List Modal -->
    <div id="priceListModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="priceListModalTitle"><i class="fas fa-list"></i> Price List</h2>
                <span class="close" data-modal="priceListModal">&times;</span>
            </div>
            <div id="priceListAlert"></div>
            
            <form id="priceListForm">
                <div class="form-group">
                    <label for="priceListName">Price List Name *</label>
                    <input type="text" id="priceListName" name="priceName" required>
                </div>
            </form>
            
            <h3>Price Entries</h3>
            <div style="margin-bottom: 15px;">
                <button type="button" id="addPriceEntry" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    Add Price Entry
                </button>
            </div>
            
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Pressure (PSI)</th>
                        <th>Size (L)</th>
                        <th>Customer Price</th>
                        <th>Factory Price</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="priceEntriesTable">
                    <!-- Price entries will be populated here -->
                </tbody>
            </table>
            
            <div style="margin-top: 20px; text-align: right;">
                <button type="button" class="btn btn-secondary" data-modal="priceListModal">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button type="submit" form="priceListForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    Save Price List
                </button>
            </div>
        </div>
    </div>

    <!-- Customer Pricing Modal -->
    <div id="customerPricingModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="customerPricingModalTitle"><i class="fas fa-user"></i> Customer Pricing</h2>
                <span class="close" data-modal="customerPricingModal">&times;</span>
            </div>
            <div id="customerPricingAlert"></div>
            
            <div id="customerPricingContent">
                <!-- Customer pricing content will be populated here -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Processing...</p>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/pricing.js"></script>
</body>
</html>
