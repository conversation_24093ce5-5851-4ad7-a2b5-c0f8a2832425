#!/usr/bin/env node

/**
 * Create Admin User Script
 * 
 * This script creates admin users for the Cylinder Management System.
 * It can be run from command line with arguments or interactively.
 * 
 * Usage:
 * node scripts/createAdmin.js
 * node scripts/createAdmin.js --name "Admin Name" --email "<EMAIL>" --password "password123" --role "admin"
 */

const mongoose = require('mongoose');
const readline = require('readline');
require('dotenv').config();

// Import models
const StaffMaster = require('../models/StaffMaster');
const FactoryMaster = require('../models/FactoryMaster');

// Database connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/cylinder-management';
    await mongoose.connect(mongoURI);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
};

// Create readline interface for interactive input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Utility function to ask questions
const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
};

// Utility function to ask for password (hidden input)
const askPassword = (question) => {
  return new Promise((resolve) => {
    process.stdout.write(question);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // Backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
};

// Validate email format
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate role
const isValidRole = (role) => {
  const validRoles = ['root', 'admin', 'manager', 'sale', 'fill'];
  return validRoles.includes(role.toLowerCase());
};

// Get or create default factory
const getOrCreateDefaultFactory = async () => {
  try {
    let factory = await FactoryMaster.findOne();
    
    if (!factory) {
      console.log('📦 No factory found. Creating default factory...');
      factory = new FactoryMaster({
        factoryInCharge: "KitJima",
        address: "Default Address",
        phone: "************",
        email: "<EMAIL>",
        status: "Active",
      });
      await factory.save();
      console.log('✅ Default factory created');
    }
    
    return factory._id;
  } catch (error) {
    console.error('❌ Error creating default factory:', error.message);
    throw error;
  }
};

// Create admin user
const createAdminUser = async (userData) => {
  try {
    // Check if user already exists
    const existingUser = await StaffMaster.findOne({ email: userData.email });
    if (existingUser) {
      throw new Error(`User with email ${userData.email} already exists`);
    }

    // Get or create default factory
    const workingPlace = await getOrCreateDefaultFactory();

    // Create new admin user
    const adminUser = new StaffMaster({
      name: userData.name,
      email: userData.email,
      password: userData.password, // Note: In production, this should be hashed
      contact: userData.phoneNumber || '************',
      emergencyContact: '************',
      role: userData.role,
      workingPlace: workingPlace,
      verified: true, // Auto-verify admin users
      banned: false,
      phoneNumber: userData.phoneNumber || '************',
      address: userData.address || 'Default Address',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    await adminUser.save();
    
    console.log('\n✅ Admin user created successfully!');
    console.log('📋 User Details:');
    console.log(`   Name: ${adminUser.name}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   ID: ${adminUser._id}`);
    console.log(`   Verified: ${adminUser.verified}`);
    console.log(`   Created: ${adminUser.createdAt}`);
    
    return adminUser;
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    throw error;
  }
};

// Parse command line arguments
const parseArgs = () => {
  const args = process.argv.slice(2);
  const userData = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i];
    const value = args[i + 1];
    
    switch (key) {
      case '--name':
      case '-n':
        userData.name = value;
        break;
      case '--email':
      case '-e':
        userData.email = value;
        break;
      case '--password':
      case '-p':
        userData.password = value;
        break;
      case '--role':
      case '-r':
        userData.role = value;
        break;
      case '--phone':
        userData.phoneNumber = value;
        break;
      case '--address':
        userData.address = value;
        break;
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
    }
  }
  
  return userData;
};

// Show help information
const showHelp = () => {
  console.log(`
🔧 Create Admin User Script

Usage:
  node scripts/createAdmin.js [options]

Options:
  -n, --name <name>         Admin user's full name
  -e, --email <email>       Admin user's email address
  -p, --password <password> Admin user's password
  -r, --role <role>         Admin user's role (root, admin, manager, sale, fill)
  --phone <phone>           Admin user's phone number (optional)
  --address <address>       Admin user's address (optional)
  -h, --help                Show this help message

Examples:
  # Interactive mode
  node scripts/createAdmin.js

  # Command line mode
  node scripts/createAdmin.js --name "John Admin" --email "<EMAIL>" --password "secure123" --role "admin"

  # Create root user
  node scripts/createAdmin.js --name "Super Admin" --email "<EMAIL>" --password "rootpass123" --role "root"

Valid Roles:
  - root: Full system access
  - admin: Administrative access
  - manager: Management access
  - sale: Sales operations
  - fill: Filling operations
`);
};

// Interactive mode - collect user input
const collectUserData = async () => {
  console.log('\n🔧 Create Admin User - Interactive Mode');
  console.log('=====================================\n');
  
  const userData = {};
  
  // Get name
  while (!userData.name) {
    userData.name = await askQuestion('👤 Enter admin name: ');
    if (!userData.name) {
      console.log('❌ Name is required');
    }
  }
  
  // Get email
  while (!userData.email || !isValidEmail(userData.email)) {
    userData.email = await askQuestion('📧 Enter admin email: ');
    if (!userData.email) {
      console.log('❌ Email is required');
    } else if (!isValidEmail(userData.email)) {
      console.log('❌ Please enter a valid email address');
      userData.email = null;
    }
  }
  
  // Get password
  while (!userData.password || userData.password.length < 6) {
    userData.password = await askPassword('🔒 Enter admin password (min 6 chars): ');
    if (!userData.password) {
      console.log('❌ Password is required');
    } else if (userData.password.length < 6) {
      console.log('❌ Password must be at least 6 characters long');
      userData.password = null;
    }
  }
  
  // Get role
  console.log('\n📋 Available roles: root, admin, manager, sale, fill');
  while (!userData.role || !isValidRole(userData.role)) {
    userData.role = await askQuestion('👔 Enter admin role: ');
    if (!userData.role) {
      console.log('❌ Role is required');
    } else if (!isValidRole(userData.role)) {
      console.log('❌ Invalid role. Choose from: root, admin, manager, sale, fill');
      userData.role = null;
    }
  }
  
  // Optional fields
  userData.phoneNumber = await askQuestion('📱 Enter phone number (optional): ');
  userData.address = await askQuestion('🏠 Enter address (optional): ');
  
  return userData;
};

// Main function
const main = async () => {
  try {
    console.log('🚀 Starting Admin Creation Script...\n');
    
    // Connect to database
    await connectDB();
    
    // Parse command line arguments
    let userData = parseArgs();
    
    // If no arguments provided, use interactive mode
    if (!userData.name && !userData.email && !userData.password && !userData.role) {
      userData = await collectUserData();
    } else {
      // Validate required fields for command line mode
      if (!userData.name || !userData.email || !userData.password || !userData.role) {
        console.error('❌ Missing required arguments. Use --help for usage information.');
        process.exit(1);
      }
      
      if (!isValidEmail(userData.email)) {
        console.error('❌ Invalid email format');
        process.exit(1);
      }
      
      if (!isValidRole(userData.role)) {
        console.error('❌ Invalid role. Valid roles: root, admin, manager, sale, fill');
        process.exit(1);
      }
      
      if (userData.password.length < 6) {
        console.error('❌ Password must be at least 6 characters long');
        process.exit(1);
      }
    }
    
    // Create the admin user
    await createAdminUser(userData);
    
  } catch (error) {
    console.error('\n❌ Script failed:', error.message);
    process.exit(1);
  } finally {
    // Close database connection and readline interface
    await mongoose.connection.close();
    rl.close();
    console.log('\n👋 Script completed. Database connection closed.');
  }
};

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Script interrupted by user');
  await mongoose.connection.close();
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = { createAdminUser, connectDB };
