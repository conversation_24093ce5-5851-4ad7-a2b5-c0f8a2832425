#!/usr/bin/env node

/**
 * Test Serial Number System
 *
 * This script tests the new serial number generation system.
 * It creates test cylinders and verifies the serial number format.
 */

const mongoose = require("mongoose");
require("dotenv").config();

// Import models
const CylinderMaster = require("../models/CylinderMaster");
const FactoryMaster = require("../models/FactoryMaster");

// Database connection
const connectDB = async () => {
  try {
    const mongoURI =
      process.env.MONGO_URI || "mongodb://localhost:27017/cylinder-management";
    await mongoose.connect(mongoURI);
    console.log("✅ MongoDB connected successfully");
  } catch (error) {
    console.error("❌ MongoDB connection failed:", error.message);
    process.exit(1);
  }
};

// Get or create test factory
const getTestFactory = async () => {
  try {
    let factory = await FactoryMaster.findOne({
      factoryInCharge: "Test Manager",
    });

    if (!factory) {
      console.log("📦 Creating test factory...");
      factory = new FactoryMaster({
        address: "Test Address",
        phone: "************",
        factoryInCharge: "Test Manager",
      });
      await factory.save();
      console.log("✅ Test factory created");
    }

    return factory._id;
  } catch (error) {
    console.error("❌ Error creating test factory:", error.message);
    throw error;
  }
};

// Test serial number generation
const testSerialNumberGeneration = async () => {
  try {
    console.log("\n🧪 Testing Serial Number Generation");
    console.log("=====================================");

    const factory = await getTestFactory();
    const testCylinders = [];

    // Create 5 test cylinders
    for (let i = 1; i <= 5; i++) {
      console.log(`\n📝 Creating test cylinder ${i}...`);

      const cylinder = new CylinderMaster({
        cylinderSize: "47L",
        importDate: new Date(),
        valueType: "Japan",
        productionDate: new Date(),
        originalNumber: `TEST${i.toString().padStart(3, "0")}`,
        workingPressure: 1000,
        status: "Empty",
        owner: factory,
      });

      await cylinder.save();
      testCylinders.push(cylinder);

      console.log(
        `   ✅ Created cylinder with serial: ${cylinder.serialNumber}`
      );
      console.log(`   📋 Original number: ${cylinder.originalNumber}`);
      console.log(`   🏭 Factory: ${factory}`);
    }

    return testCylinders;
  } catch (error) {
    console.error("❌ Error testing serial number generation:", error.message);
    throw error;
  }
};

// Validate serial number format
const validateSerialNumbers = (cylinders) => {
  console.log("\n🔍 Validating Serial Number Format");
  console.log("===================================");

  const newFormatRegex = /^[A-Z]\d{4}$/;
  let validCount = 0;
  let invalidCount = 0;

  cylinders.forEach((cylinder, index) => {
    const isValid = newFormatRegex.test(cylinder.serialNumber);
    const status = isValid ? "✅" : "❌";

    console.log(
      `${status} Cylinder ${index + 1}: ${cylinder.serialNumber} (${
        isValid ? "VALID" : "INVALID"
      })`
    );

    if (isValid) {
      validCount++;
    } else {
      invalidCount++;
    }
  });

  console.log(`\n📊 Validation Results:`);
  console.log(`   ✅ Valid: ${validCount}`);
  console.log(`   ❌ Invalid: ${invalidCount}`);
  console.log(
    `   📈 Success Rate: ${((validCount / cylinders.length) * 100).toFixed(1)}%`
  );

  return validCount === cylinders.length;
};

// Test serial number sequence
const testSequence = (cylinders) => {
  console.log("\n🔢 Testing Serial Number Sequence");
  console.log("=================================");

  const serialNumbers = cylinders.map((c) => c.serialNumber).sort();
  let sequenceValid = true;

  for (let i = 1; i < serialNumbers.length; i++) {
    const current = serialNumbers[i];
    const previous = serialNumbers[i - 1];

    const currentLetter = current.charAt(0);
    const currentNumber = parseInt(current.substring(1));
    const previousLetter = previous.charAt(0);
    const previousNumber = parseInt(previous.substring(1));

    let expectedNext;
    if (previousNumber < 9999) {
      expectedNext = `${previousLetter}${(previousNumber + 1)
        .toString()
        .padStart(4, "0")}`;
    } else {
      const nextLetter = String.fromCharCode(previousLetter.charCodeAt(0) + 1);
      expectedNext = `${nextLetter}0001`;
    }

    const isCorrect = current === expectedNext;
    const status = isCorrect ? "✅" : "❌";

    console.log(
      `${status} ${previous} → ${current} (Expected: ${expectedNext})`
    );

    if (!isCorrect) {
      sequenceValid = false;
    }
  }

  console.log(`\n📊 Sequence Results:`);
  console.log(
    `   ${sequenceValid ? "✅" : "❌"} Sequence is ${
      sequenceValid ? "VALID" : "INVALID"
    }`
  );

  return sequenceValid;
};

// Get statistics
const getStatistics = async () => {
  try {
    console.log("\n📈 Getting Statistics");
    console.log("=====================");

    const [
      totalCylinders,
      newFormatCount,
      oldFormatCount,
      latestNewFormat,
      latestOldFormat,
    ] = await Promise.all([
      CylinderMaster.countDocuments(),
      CylinderMaster.countDocuments({
        serialNumber: { $regex: /^[A-Z]\d{4}$/ },
      }),
      CylinderMaster.countDocuments({ serialNumber: { $regex: /^CYD\d+$/ } }),
      CylinderMaster.findOne({ serialNumber: { $regex: /^[A-Z]\d{4}$/ } }).sort(
        { serialNumber: -1 }
      ),
      CylinderMaster.findOne({ serialNumber: { $regex: /^CYD\d+$/ } }).sort({
        createdAt: -1,
      }),
    ]);

    console.log(`📊 Total Cylinders: ${totalCylinders}`);
    console.log(
      `🆕 New Format: ${newFormatCount} (${(
        (newFormatCount / totalCylinders) *
        100
      ).toFixed(1)}%)`
    );
    console.log(
      `🗂️  Old Format: ${oldFormatCount} (${(
        (oldFormatCount / totalCylinders) *
        100
      ).toFixed(1)}%)`
    );
    console.log(
      `🔢 Latest New: ${
        latestNewFormat ? latestNewFormat.serialNumber : "None"
      }`
    );
    console.log(
      `📅 Latest Old: ${
        latestOldFormat ? latestOldFormat.serialNumber : "None"
      }`
    );

    return {
      total: totalCylinders,
      newFormat: newFormatCount,
      oldFormat: oldFormatCount,
      latestNew: latestNewFormat ? latestNewFormat.serialNumber : null,
      latestOld: latestOldFormat ? latestOldFormat.serialNumber : null,
    };
  } catch (error) {
    console.error("❌ Error getting statistics:", error.message);
    throw error;
  }
};

// Clean up test data
const cleanupTestData = async () => {
  try {
    console.log("\n🧹 Cleaning Up Test Data");
    console.log("========================");

    // Delete test cylinders
    const deletedCylinders = await CylinderMaster.deleteMany({
      originalNumber: { $regex: /^TEST\d{3}$/ },
    });

    // Delete test factory
    const deletedFactory = await FactoryMaster.deleteOne({
      factoryInCharge: "Test Manager",
    });

    console.log(`🗑️  Deleted ${deletedCylinders.deletedCount} test cylinders`);
    console.log(`🏭 Deleted ${deletedFactory.deletedCount} test factory`);

    return {
      cylinders: deletedCylinders.deletedCount,
      factories: deletedFactory.deletedCount,
    };
  } catch (error) {
    console.error("❌ Error cleaning up test data:", error.message);
    throw error;
  }
};

// Main test function
const runTests = async () => {
  try {
    console.log("🚀 Starting Serial Number System Tests");
    console.log("======================================\n");

    // Connect to database
    await connectDB();

    // Get initial statistics
    console.log("📊 Initial Statistics:");
    const initialStats = await getStatistics();

    // Test serial number generation
    const testCylinders = await testSerialNumberGeneration();

    // Validate format
    const formatValid = validateSerialNumbers(testCylinders);

    // Test sequence
    const sequenceValid = testSequence(testCylinders);

    // Get final statistics
    console.log("\n📊 Final Statistics:");
    const finalStats = await getStatistics();

    // Summary
    console.log("\n📋 Test Summary");
    console.log("===============");
    console.log(`✅ Cylinders Created: ${testCylinders.length}`);
    console.log(
      `${formatValid ? "✅" : "❌"} Format Validation: ${
        formatValid ? "PASSED" : "FAILED"
      }`
    );
    console.log(
      `${sequenceValid ? "✅" : "❌"} Sequence Validation: ${
        sequenceValid ? "PASSED" : "FAILED"
      }`
    );
    console.log(
      `📈 New Format Count: ${initialStats.newFormat} → ${
        finalStats.newFormat
      } (+${finalStats.newFormat - initialStats.newFormat})`
    );

    // Ask for cleanup
    const readline = require("readline");
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    const answer = await new Promise((resolve) => {
      rl.question("\n🧹 Clean up test data? (y/N): ", (answer) => {
        rl.close();
        resolve(answer.toLowerCase());
      });
    });

    if (answer === "y" || answer === "yes") {
      await cleanupTestData();
      console.log("✅ Test data cleaned up successfully");
    } else {
      console.log("ℹ️  Test data preserved");
    }

    const overallSuccess = formatValid && sequenceValid;
    console.log(
      `\n🎯 Overall Test Result: ${overallSuccess ? "✅ PASSED" : "❌ FAILED"}`
    );

    return overallSuccess;
  } catch (error) {
    console.error("\n❌ Test failed:", error.message);
    return false;
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("\n👋 Database connection closed");
  }
};

// Handle script termination
process.on("SIGINT", async () => {
  console.log("\n\n⚠️  Test interrupted by user");
  await mongoose.connection.close();
  process.exit(0);
});

// Run tests if script is executed directly
if (require.main === module) {
  runTests().then((success) => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { runTests };
