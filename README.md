# API Documentation

## Authentication

All protected endpoints require a JWT token in the Authorization header.

---

## Customer Endpoints

Base path: `/customers`

| Method | Endpoint | Auth Required | Description             |
| ------ | -------- | ------------- | ----------------------- |
| GET    | `/`      | No            | Get all customers       |
| GET    | `/:id`   | No            | Get a customer by ID    |
| POST   | `/`      | No            | Create a new customer   |
| PUT    | `/:id`   | No            | Update a customer by ID |
| DELETE | `/:id`   | No            | Delete a customer by ID |

### Customer Schema

```json
{
  "name": "String, required",
  "customerType": "String, enum: ['Hospital', 'Individual', 'Shop', 'Factory', 'Workshop'], required",
  "address": "String, required",
  "payment": "String, enum: ['Credit', 'Cash', 'DailyCash'], required",
  "purchasedHistory": "Array of ObjectId references to CylinderMaster",
  "returnedHistory": "Array of ObjectId references to CylinderMaster"
}
```

---

## Cylinder Endpoints

Base path: `/cylinders`

| Method | Endpoint                | Auth Required | Roles                 | Description                        |
| ------ | ----------------------- | ------------- | --------------------- | ---------------------------------- |
| POST   | `/`                     | Yes           | admin, manager        | Create a new cylinder              |
| GET    | `/`                     | Yes           | admin, manager, staff | Get all cylinders                  |
| GET    | `/:id`                  | Yes           | admin, manager, staff | Get a cylinder by ID               |
| PUT    | `/:id`                  | Yes           | admin, manager, staff | Update a cylinder by ID            |
| DELETE | `/:id`                  | Yes           | admin, manager        | Delete a cylinder by ID            |
| GET    | `/serial/:serialNumber` | Yes           | admin, manager, staff | Get a cylinder by serial number    |
| DELETE | `/serial/:serialNumber` | Yes           | admin, manager        | Delete a cylinder by serial number |

---

## Factory Endpoints

Base path: `/factories`

| Method | Endpoint | Auth Required | Description            |
| ------ | -------- | ------------- | ---------------------- |
| GET    | `/`      | No            | Get all factories      |
| GET    | `/:id`   | No            | Get a factory by ID    |
| POST   | `/`      | No            | Create a new factory   |
| PUT    | `/:id`   | No            | Update a factory by ID |
| DELETE | `/:id`   | No            | Delete a factory by ID |

---

## Line Number Endpoints

Base path: `/line-numbers`

| Method | Endpoint | Auth Required | Roles                 | Description                |
| ------ | -------- | ------------- | --------------------- | -------------------------- |
| POST   | `/`      | Yes           | admin, manager        | Create a new line number   |
| GET    | `/`      | Yes           | admin, manager, staff | Get all line numbers       |
| GET    | `/:id`   | Yes           | admin, manager, staff | Get a line number by ID    |
| PUT    | `/:id`   | Yes           | admin, manager        | Update a line number by ID |
| DELETE | `/:id`   | Yes           | admin, manager        | Delete a line number by ID |

---

## Price Endpoints

Base path: `/prices`

| Method | Endpoint | Auth Required | Description                 |
| ------ | -------- | ------------- | --------------------------- |
| POST   | `/`      | No            | Create a new price record   |
| PUT    | `/:id`   | No            | Update a price record by ID |
| GET    | `/`      | No            | Get all price records       |
| GET    | `/:id`   | No            | Get a price record by ID    |

---

## Report Endpoints

Base path: `/reports`

| Method | Endpoint                      | Auth Required | Roles          | Description                        |
| ------ | ----------------------------- | ------------- | -------------- | ---------------------------------- |
| GET    | `/:period/:startDate`         | Yes           | admin, manager | Get report for a specific period   |
| GET    | `/custom/:startDate/:endDate` | Yes           | admin, manager | Get report for a custom date range |

---

## Staff Endpoints

Base path: `/staff`

| Method | Endpoint         | Auth Required | Roles                 | Description                   |
| ------ | ---------------- | ------------- | --------------------- | ----------------------------- |
| GET    | `/`              | Yes           | admin, manager        | Get all staff members         |
| GET    | `/:id`           | Yes           | admin, manager        | Get a staff member by ID      |
| POST   | `/signup`        | No            | None                  | Staff signup                  |
| POST   | `/login`         | No            | None                  | Staff login                   |
| GET    | `/refresh/token` | Yes           | admin, manager, staff | Refresh token                 |
| PUT    | `/:id`           | Yes           | admin, manager        | Update a staff member         |
| PUT    | `/verify/:id`    | Yes           | admin                 | Verify new staff registration |
| DELETE | `/:id`           | Yes           | admin, manager        | Delete a staff member         |

---

## Truck Endpoints

Base path: `/trucks`

| Method | Endpoint     | Auth Required | Roles                         | Description              |
| ------ | ------------ | ------------- | ----------------------------- | ------------------------ |
| GET    | `/`          | Yes           | admin, manager, staff, driver | Get all trucks           |
| GET    | `/available` | Yes           | admin, manager, staff, driver | Get all available trucks |
| GET    | `/:id`       | Yes           | admin, manager, staff, driver | Get a truck by ID        |
| POST   | `/`          | Yes           | admin, manager                | Create a new truck       |
| PUT    | `/:id`       | Yes           | admin, manager                | Update a truck by ID     |
| DELETE | `/:id`       | Yes           | admin, manager                | Delete a truck by ID     |

---

## Analytics Endpoints

Base path: `/analytics`

| Method | Endpoint                                             | Auth Required | Roles                | Description                                                  |
| ------ | ---------------------------------------------------- | ------------- | -------------------- | ------------------------------------------------------------ |
| GET    | `/daily/:date`                                       | Yes           | root, admin, manager | Get daily sales analytics for a specific date (YYYY-MM-DD)   |
| GET    | `/monthly/:date`                                     | Yes           | root, admin, manager | Get monthly sales analytics for a specific month (YYYY-MM)   |
| GET    | `/yearly/:year`                                      | Yes           | root, admin, manager | Get yearly sales analytics for a specific year (YYYY)        |
| GET    | `/period?startDate=&endDate=`                        | Yes           | root, admin, manager | Get analytics for a custom date range                        |
| GET    | `/returns?period=&startDate=&endDate=`               | Yes           | root, admin, manager | Get cylinder return analytics                                |
| GET    | `/summary?period=&startDate=&endDate=`               | Yes           | root, admin, manager | Get combined sales and returns summary                       |
| GET    | `/overview/daily?startDate=&endDate=`                | Yes           | root, admin, manager | Get daily overview report (tabular format like your example) |
| GET    | `/overview/customer?startDate=&endDate=`             | Yes           | root, admin, manager | Get daily overview by customer                               |
| GET    | `/overview/customer-type?startDate=&endDate=`        | Yes           | root, admin, manager | Get daily overview by customer type                          |
| GET    | `/export/daily-overview?startDate=&endDate=`         | Yes           | root, admin, manager | Export daily overview to Excel file                          |
| GET    | `/export/customer-overview?startDate=&endDate=`      | Yes           | root, admin, manager | Export daily overview by customer to Excel file              |
| GET    | `/export/customer-type-overview?startDate=&endDate=` | Yes           | root, admin, manager | Export daily overview by customer type to Excel file         |
| GET    | `/export/summary?period=&startDate=&endDate=`        | Yes           | root, admin, manager | Export analytics summary to Excel file                       |

### Analytics Features

- **Sales Analysis**: Total sales, net sales, discounts, transaction counts
- **Payment Analysis**: Cash vs Credit breakdown with amounts and percentages
- **Cylinder Analysis**: Sales by cylinder size (47L, 40L, 15L, 10L)
- **Customer Analysis**: Sales by customer type, top customers
- **Return Analysis**: Cylinder returns by size, customer type, and time period
- **Time-based Analysis**: Daily, monthly, yearly breakdowns
- **Performance Metrics**: Average transaction value, return rates, payment distribution
- **Excel Export**: Professional Excel reports with styling, charts, and formatted tables

### Example Responses

#### Daily Analytics Response

```json
{
  "period": "daily",
  "date": "2024-01-15",
  "analytics": {
    "totalSales": 50000,
    "totalDiscount": 2000,
    "netSales": 48000,
    "totalQuantitySold": 25,
    "totalQuantityReturned": 3,
    "totalTransactions": 8,
    "cashSales": {
      "amount": 30000,
      "transactions": 5,
      "quantity": 15
    },
    "creditSales": {
      "amount": 20000,
      "transactions": 3,
      "quantity": 10
    },
    "cylinderSizes": {
      "47L": { "quantity": 15, "amount": 35000 },
      "40L": { "quantity": 8, "amount": 12000 },
      "15L": { "quantity": 2, "amount": 3000 }
    },
    "customerTypes": {
      "Hospital": { "transactions": 3, "amount": 25000, "quantity": 12 },
      "Individual": { "transactions": 5, "amount": 25000, "quantity": 13 }
    },
    "topCustomers": [
      {
        "name": "ABC Hospital",
        "transactions": 2,
        "amount": 15000,
        "quantity": 8
      }
    ]
  }
}
```

#### Summary Analytics Response

```json
{
  "period": "monthly",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "summary": {
    "totalSales": 500000,
    "netSales": 480000,
    "averageTransactionValue": 6250,
    "returnRate": 12.5,
    "cashPercentage": 60,
    "creditPercentage": 40,
    "totalQuantitySold": 250,
    "totalQuantityReturned": 31
  }
}
```

#### Daily Overview Response (Tabular Format)

```json
{
  "period": {
    "from": "26th May",
    "to": "8th June",
    "fromDate": "2024-05-26",
    "toDate": "2024-06-08"
  },
  "headers": [
    { "date": "2024-05-26", "day": "26", "monthYear": "May-24" },
    { "date": "2024-05-27", "day": "27", "monthYear": "May-24" },
    { "date": "2024-06-01", "day": "1", "monthYear": "Jun-24" }
  ],
  "dailyOverall": {
    "deliver": [
      350, 400, 250, 400, 250, 300, 300, 500, 400, 450, 500, 450, 400, 300
    ],
    "return": [
      300, 300, 300, 300, 500, 400, 400, 300, 300, 300, 350, 250, 250, 400
    ],
    "balanceInCirculation": [
      1000, 1100, 1050, 1150, 900, 800, 700, 900, 1000, 1150, 1300, 1500, 1650,
      1550
    ],
    "balanceAtFactory": [
      1200, 1100, 1150, 1050, 1300, 1400, 1500, 1300, 1200, 1050, 900, 700, 550,
      650
    ],
    "totalCylinders": [
      2200, 2200, 2200, 2200, 2200, 2200, 2200, 2200, 2200, 2200, 2200, 2200,
      2200, 2200
    ]
  },
  "summary": {
    "totalDelivered": 5250,
    "totalReturned": 4650,
    "netDelivered": 600,
    "totalCylinders": 2200
  }
}
```

#### Daily Overview by Customer Response (Your Requested Format)

```json
{
  "period": {
    "from": "26th May",
    "to": "8th June"
  },
  "headers": [
    { "date": "2024-05-26", "day": "26", "monthYear": "May-24" },
    { "date": "2024-05-27", "day": "27", "monthYear": "May-24" },
    { "date": "2024-05-28", "day": "28", "monthYear": "May-24" },
    { "date": "2024-05-29", "day": "29", "monthYear": "May-24" }
  ],
  "customerOverview": [
    {
      "sr": 1,
      "customerName": "Pan Hlaing",
      "customerType": "Hospital",
      "delivered": [70, 0, 0, 150],
      "returned": [50, 0, 0, 0],
      "balance": [100, 100, 100, 250],
      "totalDelivered": 220,
      "totalReturned": 50,
      "finalBalance": 250
    },
    {
      "sr": 2,
      "customerName": "ABC Factory",
      "customerType": "Factory",
      "delivered": [100, 120, 80, 110],
      "returned": [90, 100, 70, 95],
      "balance": [10, 30, 40, 55],
      "totalDelivered": 410,
      "totalReturned": 355,
      "finalBalance": 55
    }
  ]
}
```

#### Daily Overview by Customer Type Response (Your Requested Format)

```json
{
  "period": {
    "from": "26th May",
    "to": "8th June"
  },
  "headers": [
    { "date": "2024-05-26", "day": "26", "monthYear": "May-24" },
    { "date": "2024-05-27", "day": "27", "monthYear": "May-24" },
    { "date": "2024-05-28", "day": "28", "monthYear": "May-24" },
    { "date": "2024-05-29", "day": "29", "monthYear": "May-24" }
  ],
  "customerTypeOverview": [
    {
      "customerType": "Hospital",
      "delivered": [170, 150, 100, 300],
      "returned": [150, 100, 150, 100],
      "balance": [100, 150, 100, 300],
      "totalDelivered": 720,
      "totalReturned": 500,
      "finalBalance": 300
    },
    {
      "customerType": "Individual",
      "delivered": [0, 0, 0, 0],
      "returned": [0, 0, 0, 0],
      "balance": [0, 0, 0, 0],
      "totalDelivered": 0,
      "totalReturned": 0,
      "finalBalance": 0
    },
    {
      "customerType": "Shop",
      "delivered": [180, 250, 150, 100],
      "returned": [150, 200, 150, 200],
      "balance": [540, 590, 590, 490],
      "totalDelivered": 680,
      "totalReturned": 700,
      "finalBalance": 490
    }
  ],
  "totals": {
    "delivered": [350, 400, 250, 400],
    "returned": [300, 300, 300, 300],
    "balanceInCirculation": [640, 740, 690, 790],
    "balanceAtFactory": [1200, 1100, 1150, 1050],
    "totalCylinders": [2200, 2200, 2200, 2200]
  }
}
```

### Excel Export Features

The analytics system includes comprehensive Excel export functionality with professional styling:

#### Export Types Available

1. **Daily Overview Export**: Complete tabular format matching your example layout
2. **Customer Overview Export**: Detailed breakdown by individual customers
3. **Customer Type Overview Export**: Aggregated data by customer types
4. **Analytics Summary Export**: Comprehensive summary with key metrics

#### Excel Features

- **Professional Styling**: Blue headers, alternating row colors, borders
- **Proper Formatting**: Merged cells for titles, centered alignment
- **Period Information**: Clear date ranges and report metadata
- **Automatic Calculations**: Totals, percentages, and derived metrics
- **Multiple Worksheets**: Organized data across different sheets when applicable

#### Usage Examples

```bash
# Export daily overview (like your table format)
GET /api/analytics/export/daily-overview?startDate=2024-05-26&endDate=2024-06-08
# Downloads: Daily_Overview_2024-05-26_to_2024-06-08.xlsx

# Export customer breakdown
GET /api/analytics/export/customer-overview?startDate=2024-05-26&endDate=2024-06-08
# Downloads: Daily_Overview_by_Customer_2024-05-26_to_2024-06-08.xlsx

# Export customer type analysis
GET /api/analytics/export/customer-type-overview?startDate=2024-05-26&endDate=2024-06-08
# Downloads: Daily_Overview_by_Customer_Type_2024-05-26_to_2024-06-08.xlsx

# Export analytics summary
GET /api/analytics/export/summary?period=monthly&startDate=2024-05-01
# Downloads: Analytics_Summary_monthly_2024-05-01.xlsx
```

#### Excel File Structure

**Daily Overview Excel File:**

```
Daily Delivery Reports

Period
From: 26th May
To: 8th June

        May-24              Jun-24
        26  27  28  29  30  31  1   2   3   4   5   6   7   8
Deliver 350 400 250 400 250 300 300 500 400 450 500 450 400 300
Return  300 300 300 300 500 400 400 300 300 300 350 250 250 400
Balance in Circulation: 1000 1100 1050 1150 900 800 700 900 1000 1150 1300 1500 1650 1550
Balance at Factory: 1200 1100 1150 1050 1300 1400 1500 1300 1200 1050 900 700 550 650
Total Cylinders: 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200
```

**Daily By Customer Excel File:**

```
Daily By Customer

        May-24              Jun-24
Sr  Customer        26  27  28  29  30  31  1   2   3   4   5   6   7   8
1   Pan Hlaing  Delivered   70  0   0   150 0   100 0   200 0   0   300 0   200 0
                Returned    50  0   0   0   0   250 0   100 0   0   150 0   150 0
                Balance     100 100 100 250 250 100 100 200 200 200 350 350 400 400
2   ABC Factory Delivered   80  120 60  90  40  80  120 150 100 80  200 120 100 60
                Returned    70  100 50  80  30  70  100 130 90  70  180 100 90  50
                Balance     10  30  40  50  60  70  90  110 120 130 150 170 180 190
```

**Daily By Customer Type Excel File:**

```
Daily By Customer Type

        May-24              Jun-24
Customer Type       26  27  28  29  30  31  1   2   3   4   5   6   7   8
Hospital    Delivered   170 150 100 300 0   300 200 400 200 50  300 300 200 100
            Returned    150 100 150 100 300 250 120 300 100 0   150 200 150 200
            Balance     100 150 100 300 0   50  130 230 330 380 530 630 680 580
Individual  Delivered   0   0   0   0   0   0   0   0   0   0   0   0   0   0
            Returned    0   0   0   0   0   0   0   0   0   0   0   0   0   0
            Balance     0   0   0   0   0   0   0   0   0   0   0   0   0   0
Shop        Delivered   180 250 150 100 250 0   100 100 200 400 200 150 200 200
            Returned    150 200 150 200 200 150 280 0   200 300 200 50  100 200
            Balance     540 590 590 490 540 390 210 310 310 410 410 510 610 610
Workshop    Delivered   0   0   0   0   0   0   0   0   0   0   0   0   0   0
            Returned    0   0   0   0   0   0   0   0   0   0   0   0   0   0
            Balance     0   0   0   0   0   0   0   0   0   0   0   0   0   0
Total       Delivered   350 400 250 400 250 300 300 500 400 450 500 450 400 300
            Returned    300 300 300 300 500 400 400 300 300 300 350 250 250 400
            Balance in Circulation  640 740 690 790 540 440 340 540 640 790 940 1140 1290 1190
            Balance in Factory      1200 1100 1150 1050 1300 1400 1500 1300 1200 1050 900 700 550 650
            Total Cylinders         2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200 2200
```

---

## Notes

1. All protected endpoints require a valid JWT token in the Authorization header.
2. Ensure environment variables such as `MONGO_URI` and `JWT_SECRET` are properly configured.
3. Role hierarchy: admin > manager > staff > driver.
4. Dates in report endpoints should be provided in a valid date format.

For specific request/response formats for each endpoint, please consult the respective controllers:

- CustomerController
- CylinderMasterController
- FactoryController
- LineNumberMasterController
- PriceMasterController
- ReportController
- StaffController
- TruckController

This documentation provides a high-level overview of the API endpoints based on the route files shown. To make this documentation more complete, you would need to add:

1. Specific request body formats for POST/PUT endpoints.
2. Response formats for successful requests.
3. Additional error codes and messages.
4. Rate limiting information (if any).
5. Environment setup requirements.
6. Detailed examples of API calls.

Would you like more details on any specific aspect or functionality?
