# Cylinder Serial Number System

This document describes the new cylinder serial number format and how to use the admin system.

## New Serial Number Format

### Format: Letter + 4 Digits
- **Pattern**: `A0001`, `A0002`, `A0003`, ..., `A9999`, `B0001`, `B0002`, etc.
- **Range**: A0001 to Z9999 (259,974 total possible serial numbers)
- **Auto-increment**: Automatically generates the next available serial number

### Examples
```
A0001 → A0002 → A0003 → ... → A9999 → B0001 → B0002 → ... → Z9999
```

## How It Works

### Automatic Generation
When creating a new cylinder, the system automatically:
1. Finds the latest serial number in the new format
2. Increments the number (A0001 → A0002)
3. Rolls over to next letter when reaching 9999 (A9999 → B0001)

### Backward Compatibility
- **Old Format**: `CYD20251227...` (still supported for existing cylinders)
- **New Format**: `A0001`, `B0002`, etc. (for new cylinders)
- Both formats work in all API endpoints and searches

## API Endpoints

### Serial Number Management
```
GET /api/serial-numbers/stats          # Get statistics
GET /api/serial-numbers/next           # Preview next serial number
POST /api/serial-numbers/validate      # Validate serial number format
GET /api/serial-numbers/cylinders      # Get cylinders by format
GET /api/serial-numbers/search         # Search by pattern
POST /api/serial-numbers/migrate       # Migrate old to new format
```

### Example API Calls
```bash
# Get next serial number
curl -H "authorization: YOUR_TOKEN" http://localhost:4000/api/serial-numbers/next

# Get statistics
curl -H "authorization: YOUR_TOKEN" http://localhost:4000/api/serial-numbers/stats

# Search cylinders
curl -H "authorization: YOUR_TOKEN" "http://localhost:4000/api/serial-numbers/search?pattern=A00"
```

## Admin System Setup

### 1. Create Admin User
```bash
# Using the script
node scripts/createAdmin.js --name "Admin Name" --email "<EMAIL>" --password "password123" --role "admin"

# Interactive mode
node scripts/createAdmin.js
```

### 2. Login Process
1. Go to `http://localhost:4000`
2. Login with admin credentials
3. Click the green "Admin" button (visible only to admin/root users)

### 3. Fix Authentication Issues

If you get "Access denied. No token provided" error:

#### Check Token Format
The system expects the token in the `authorization` header (lowercase). Make sure your requests include:
```javascript
headers: {
    'authorization': 'your-jwt-token-here'
}
```

#### Verify Login
1. Make sure you're logged in to the main system first
2. The admin interface uses the same authentication as the main system
3. Admin button only appears for users with 'admin' or 'root' roles

## Migration from Old Format

### Check Current Status
```bash
curl -H "authorization: YOUR_TOKEN" http://localhost:4000/api/serial-numbers/stats
```

### Migrate Old Serial Numbers
```bash
# Dry run (preview what will be migrated)
curl -X POST -H "authorization: YOUR_TOKEN" -H "Content-Type: application/json" \
  -d '{"limit": 10, "dryRun": true}' \
  http://localhost:4000/api/serial-numbers/migrate

# Actual migration
curl -X POST -H "authorization: YOUR_TOKEN" -H "Content-Type: application/json" \
  -d '{"limit": 100}' \
  http://localhost:4000/api/serial-numbers/migrate
```

## Testing the New System

### 1. Create Test Cylinder
```bash
curl -X POST -H "authorization: YOUR_TOKEN" -H "Content-Type: application/json" \
  -d '{
    "cylinderSize": "47L",
    "importDate": "2025-01-01",
    "valueType": "Japan",
    "productionDate": "2025-01-01",
    "originalNumber": "TEST001",
    "workingPressure": 1000,
    "status": "Empty"
  }' \
  http://localhost:4000/api/cylinder-master
```

### 2. Verify Serial Number
The response should include a `serialNumber` field with the new format (e.g., "A0001").

### 3. Search by Serial Number
```bash
curl -H "authorization: YOUR_TOKEN" http://localhost:4000/api/cylinder-master/serial/A0001
```

## Admin Interface Features

### User Management
- **Create Users**: Add new staff members with different roles
- **Manage Users**: View, verify, ban/unban users
- **Role Management**: Assign roles (root, admin, manager, sale, fill)
- **Statistics**: View user statistics and role distribution

### Access Control
- **Root**: Full system access, can create other root users
- **Admin**: Can manage users, cannot create root users
- **Manager**: Limited administrative access
- **Sale/Fill**: Operational access only

## Troubleshooting

### Common Issues

1. **"Access denied. No token provided"**
   - Solution: Make sure you're logged in and using lowercase 'authorization' header

2. **"Admin button not visible"**
   - Solution: User must have 'admin' or 'root' role

3. **"Serial number limit exceeded"**
   - Solution: System supports up to Z9999 (259,974 cylinders)

4. **"User already exists"**
   - Solution: Use different email address or check existing users

### Getting Help

1. Check server logs for detailed error messages
2. Verify database connection
3. Ensure all required environment variables are set
4. Test API endpoints with curl or Postman

## Best Practices

### Serial Number Management
1. **Don't manually set serial numbers** - let the system auto-generate
2. **Use migration carefully** - test with small batches first
3. **Monitor capacity** - track usage approaching Z9999 limit

### Admin Operations
1. **Use root account sparingly** - create admin accounts for daily use
2. **Regular backups** - backup user data before major changes
3. **Audit trail** - monitor admin actions through logs

### Security
1. **Strong passwords** - enforce minimum 6 characters
2. **Regular verification** - verify new user accounts promptly
3. **Role principle** - assign minimum required permissions

## Future Enhancements

### Planned Features
1. **Bulk operations** - mass user management
2. **Advanced search** - complex serial number queries
3. **Export functionality** - user and cylinder data exports
4. **Audit logging** - detailed admin action tracking
5. **Email notifications** - automated user management emails

### Serial Number Extensions
1. **Custom prefixes** - factory-specific prefixes
2. **Batch numbering** - group cylinders by production batch
3. **QR code integration** - generate QR codes for serial numbers
4. **Barcode support** - barcode scanning for serial numbers
