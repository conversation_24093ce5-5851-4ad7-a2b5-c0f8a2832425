# System Logs Viewer

This document describes the comprehensive system logs viewing interface that allows authorized users to monitor and analyze system activity through a user-friendly web interface.

## Overview

The System Logs Viewer provides a complete interface for viewing and analyzing system logs with the following capabilities:
- **View All Logs**: Browse all system logs with pagination and filtering
- **Filter & Search**: Filter logs by type, date range, and user
- **Real-time Statistics**: Dashboard showing log statistics and trends
- **Export Functionality**: Export logs to CSV format
- **Detailed View**: View complete log details in modal windows
- **Role-based Access**: Secure access based on user permissions

## Access & Permissions

### Role-Based Access Control
- **View Access**: root, admin, manager, fill roles only
- **No Access**: sale role (restricted from viewing logs)

### Navigation
1. Login to the main system
2. Click the purple "Logs" button in the header (visible only to authorized users)
3. Access the logs viewing interface

## Features

### 1. Statistics Dashboard
Real-time statistics displayed at the top of the page:
- **Total Logs**: Total number of logs in the system
- **Today**: Number of logs created today
- **Errors**: Count of error-type logs
- **Warnings**: Count of warning-type logs
- **Active Users**: Number of unique users who have created logs

### 2. Filter & Sort System

#### Filter Options
- **Log Type Filter**: Filter by log type (Info, Warning, Error, Success, Debug, System, User, Security, Audit)
- **Date Range**: Filter logs by start and end date
- **Sort Order**: Newest first (default) or Oldest first
- **Limit**: Number of logs to display (10, 25, 50, 100)

#### Quick Actions
- **Load Logs**: Refresh and load logs with current filters
- **Clear Filters**: Reset all filters to default values
- **Export**: Download filtered logs as CSV file

### 3. Logs Table

#### Table Columns
- **Type**: Log type with color-coded badges
- **Date & Time**: When the log was created (formatted for readability)
- **User**: Staff member who created the log (with avatar and role)
- **Remarks**: Log message/description (expandable for long text)
- **Actions**: View detailed information button

#### Log Type Badges
- **Info**: Blue badge - informational messages
- **Warning**: Yellow badge - warning conditions
- **Error**: Red badge - error conditions
- **Success**: Green badge - successful operations
- **Debug**: Gray badge - debugging information
- **System**: Blue badge - system-level events
- **User**: Light blue badge - user actions
- **Security**: Red badge - security-related events
- **Audit**: Green badge - audit trail events

### 4. User Information Display

#### Staff Avatar System
- **Initials**: Shows first letters of user's name
- **Color Coding**: Consistent color scheme for user identification
- **Role Display**: Shows user's role below their name
- **Email**: Available in detailed view

### 5. Pagination System
- **Configurable Items**: 10, 25, 50, or 100 logs per page
- **Navigation**: Previous/Next buttons
- **Page Information**: Current page, total pages, and total count
- **Responsive**: Works on all screen sizes

### 6. Detailed Log View

#### Modal Features
- **Complete Information**: All log details in organized format
- **Log Type**: Visual badge with type information
- **Timestamp**: Precise date and time information
- **User Details**: Complete user information including email
- **Full Remarks**: Complete log message without truncation
- **Technical Details**: Log ID and creation/update timestamps

### 7. Export Functionality

#### CSV Export Features
- **Filtered Data**: Exports only currently filtered logs
- **Complete Information**: All log fields included
- **Proper Formatting**: CSV-compliant formatting with proper escaping
- **Automatic Download**: Browser automatically downloads the file
- **Timestamped Filename**: Files named with current date

#### Export Fields
- Log Type
- Date & Time
- User Name
- User Role
- Remarks
- Log ID

### 8. Expandable Remarks

#### Text Management
- **Truncation**: Long remarks are automatically truncated
- **Expand/Collapse**: Click button to show/hide full text
- **Visual Indicators**: Icons show expand/collapse state
- **Responsive**: Adapts to different screen sizes

## Technical Implementation

### Frontend Architecture
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern JavaScript**: ES6+ features with class-based architecture
- **Modal System**: Clean popup interfaces for detailed views
- **Real-time Filtering**: Client-side filtering for better performance
- **Error Handling**: Comprehensive error messages and feedback

### API Integration
- **Secure Endpoints**: JWT token-based authentication
- **Role Validation**: Server-side role checking
- **Efficient Queries**: Optimized database queries with pagination
- **Error Handling**: Proper HTTP status codes and error messages

### Security Features
- **Authentication Required**: All endpoints require valid JWT tokens
- **Role-based Access**: Server-side role validation
- **Data Isolation**: Users only see logs from their working place
- **Secure Export**: Token verification for all operations

## Usage Examples

### 1. Viewing Recent Logs
```
1. Click "Load Logs" button
2. Logs appear in table format
3. Use pagination to browse through logs
4. Click "View" to see detailed information
```

### 2. Filtering by Log Type
```
1. Select "Error" from Log Type Filter dropdown
2. Table automatically filters to show only error logs
3. Statistics update to reflect filtered data
4. Export button will export only error logs
```

### 3. Finding Logs from Specific Date
```
1. Set Start Date to desired date
2. Set End Date (optional, for date range)
3. Click "Load Logs" to apply date filter
4. Table shows only logs from specified period
```

### 4. Exporting Logs
```
1. Apply desired filters (type, date, etc.)
2. Click "Export" button
3. CSV file automatically downloads
4. File contains all filtered log data
```

### 5. Viewing Log Details
```
1. Find log in table
2. Click "View" button (eye icon)
3. Modal opens with complete log information
4. Click X or outside modal to close
```

## Log Types Explained

### System Log Types
- **Info**: General information about system operations
- **Warning**: Conditions that might need attention but aren't errors
- **Error**: Error conditions that need immediate attention
- **Success**: Successful completion of operations
- **Debug**: Technical debugging information for developers

### Business Log Types
- **System**: System-level events (startup, shutdown, configuration changes)
- **User**: User actions (login, logout, data changes)
- **Security**: Security-related events (failed logins, permission changes)
- **Audit**: Audit trail for compliance and tracking

## Best Practices

### Log Monitoring
1. **Regular Review**: Check logs regularly for errors and warnings
2. **Filter Usage**: Use filters to focus on specific types of events
3. **Export for Analysis**: Export logs for detailed analysis in spreadsheet applications
4. **Date Range Filtering**: Use date ranges to analyze specific time periods

### Performance Tips
1. **Use Filters**: Filter large datasets instead of loading all logs
2. **Limit Results**: Use appropriate result limits (25-50 for regular use)
3. **Date Ranges**: Use specific date ranges for better performance
4. **Regular Cleanup**: Archive old logs to maintain system performance

### Security Considerations
1. **Access Control**: Only authorized users can view logs
2. **Data Sensitivity**: Logs may contain sensitive information
3. **Export Security**: Exported files should be handled securely
4. **Regular Monitoring**: Monitor for security-related log entries

## Troubleshooting

### Common Issues

1. **"Access denied" Error**
   - **Cause**: Insufficient role permissions
   - **Solution**: Contact administrator for role upgrade

2. **No Logs Displayed**
   - **Cause**: Filters too restrictive or no logs in date range
   - **Solution**: Clear filters or adjust date range

3. **Export Not Working**
   - **Cause**: Browser blocking downloads or network issues
   - **Solution**: Check browser settings and network connection

4. **Slow Loading**
   - **Cause**: Large number of logs or network issues
   - **Solution**: Use filters to reduce dataset size

### Performance Issues

1. **Large Datasets**: Use date range filters to limit results
2. **Network Timeouts**: Reduce result limit or use more specific filters
3. **Browser Memory**: Clear browser cache if experiencing slowdowns
4. **Server Load**: Contact administrator if system is consistently slow

## Future Enhancements

### Planned Features
1. **Real-time Updates**: Live log streaming with WebSocket integration
2. **Advanced Search**: Full-text search across log messages
3. **Log Aggregation**: Summary views and trend analysis
4. **Alert System**: Automated alerts for critical log events
5. **Dashboard Integration**: Integration with main analytics dashboard

### Advanced Analytics
1. **Trend Analysis**: Graphical representation of log trends over time
2. **Error Patterns**: Automated detection of recurring error patterns
3. **User Activity**: Detailed user activity analysis
4. **Performance Metrics**: System performance insights from logs

### Integration Possibilities
1. **Email Notifications**: Automated email alerts for critical events
2. **Mobile App**: Dedicated mobile log viewing application
3. **External Systems**: Integration with external monitoring tools
4. **API Access**: RESTful API for external log analysis tools

## Support

### Getting Help
1. **User Manual**: Refer to this documentation
2. **System Administrator**: Contact your system admin for access issues
3. **Error Messages**: Read error messages carefully for guidance
4. **Browser Console**: Check browser console for technical errors

### Reporting Issues
When reporting issues, include:
1. **User Role**: Your role in the system
2. **Browser**: Browser type and version
3. **Steps**: Exact steps to reproduce the issue
4. **Error Messages**: Any error messages displayed
5. **Screenshots**: Visual evidence of the problem
6. **Log Filters**: What filters were applied when issue occurred

## Data Privacy & Compliance

### Data Handling
1. **Sensitive Information**: Logs may contain sensitive business data
2. **Access Logging**: All log access is tracked for audit purposes
3. **Data Retention**: Logs are retained according to company policy
4. **Export Tracking**: All log exports are logged for security

### Compliance Features
1. **Audit Trail**: Complete audit trail of who accessed what logs when
2. **Role-based Access**: Strict role-based access control
3. **Data Encryption**: All data transmitted securely
4. **Retention Policies**: Automated log retention and archival

The System Logs Viewer provides a comprehensive solution for monitoring system activity with an intuitive interface and robust security features. Regular use helps maintain system health and provides valuable insights into system operations and user activities.
