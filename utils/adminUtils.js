/**
 * Admin Utility Functions
 *
 * This module provides utility functions for creating and managing admin users.
 * Can be used within the application or imported by other modules.
 */

const StaffMaster = require("../models/StaffMaster");
const FactoryMaster = require("../models/FactoryMaster");

// Try to import bcrypt, but don't fail if it's not available
let bcrypt;
try {
  bcrypt = require("bcrypt");
} catch (error) {
  console.log(
    "Note: bcrypt not available, passwords will be stored as plain text"
  );
  bcrypt = null;
}

/**
 * Create an admin user
 * @param {Object} userData - User data object
 * @param {string} userData.name - Full name
 * @param {string} userData.email - Email address
 * @param {string} userData.password - Password
 * @param {string} userData.role - User role (root, admin, manager, sale, fill)
 * @param {string} [userData.phoneNumber] - Phone number (optional)
 * @param {string} [userData.address] - Address (optional)
 * @param {boolean} [userData.verified=true] - Verification status
 * @param {boolean} [userData.banned=false] - Ban status
 * @returns {Promise<Object>} Created user object
 */
const createAdminUser = async (userData) => {
  try {
    // Validate required fields
    const requiredFields = ["name", "email", "password", "role"];
    for (const field of requiredFields) {
      if (!userData[field]) {
        throw new Error(`${field} is required`);
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      throw new Error("Invalid email format");
    }

    // Validate role
    const validRoles = ["root", "admin", "manager", "sale", "fill"];
    if (!validRoles.includes(userData.role.toLowerCase())) {
      throw new Error(`Invalid role. Valid roles: ${validRoles.join(", ")}`);
    }

    // Validate password length
    if (userData.password.length < 6) {
      throw new Error("Password must be at least 6 characters long");
    }

    // Check if user already exists
    const existingUser = await StaffMaster.findOne({ email: userData.email });
    if (existingUser) {
      throw new Error(`User with email ${userData.email} already exists`);
    }

    // Get or create default factory
    const workingPlace = await getOrCreateDefaultFactory();

    // Hash password if bcrypt is available (optional)
    let hashedPassword = userData.password;
    try {
      if (bcrypt) {
        const saltRounds = 10;
        hashedPassword = await bcrypt.hash(userData.password, saltRounds);
      }
    } catch (error) {
      console.log("Note: bcrypt not available, storing plain text password");
    }

    // Create new admin user
    const adminUser = new StaffMaster({
      name: userData.name,
      email: userData.email.toLowerCase(),
      password: hashedPassword,
      role: userData.role.toLowerCase(),
      workingPlace: workingPlace,
      verified: userData.verified !== undefined ? userData.verified : true,
      banned: userData.banned !== undefined ? userData.banned : false,
      phoneNumber: userData.phoneNumber || "************",
      address: userData.address || "Default Address",
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    await adminUser.save();

    // Return user without password
    const userResponse = adminUser.toObject();
    delete userResponse.password;

    return userResponse;
  } catch (error) {
    throw new Error(`Failed to create admin user: ${error.message}`);
  }
};

/**
 * Get or create default factory
 * @returns {Promise<string>} Factory ID
 */
const getOrCreateDefaultFactory = async () => {
  try {
    let factory = await FactoryMaster.findOne();

    if (!factory) {
      factory = new FactoryMaster({
        factoryName: "Default Factory",
        address: "Default Address",
        phoneNumber: "************",
        email: "<EMAIL>",
        status: "Active",
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      await factory.save();
    }

    return factory._id;
  } catch (error) {
    throw new Error(`Failed to get or create factory: ${error.message}`);
  }
};

/**
 * Create root user (super admin)
 * @param {Object} userData - User data object
 * @returns {Promise<Object>} Created root user object
 */
const createRootUser = async (userData) => {
  const rootData = {
    ...userData,
    role: "root",
    verified: true,
    banned: false,
  };

  return await createAdminUser(rootData);
};

/**
 * Update user role
 * @param {string} userId - User ID
 * @param {string} newRole - New role
 * @returns {Promise<Object>} Updated user object
 */
const updateUserRole = async (userId, newRole) => {
  try {
    const validRoles = ["root", "admin", "manager", "sale", "fill"];
    if (!validRoles.includes(newRole.toLowerCase())) {
      throw new Error(`Invalid role. Valid roles: ${validRoles.join(", ")}`);
    }

    const user = await StaffMaster.findByIdAndUpdate(
      userId,
      {
        role: newRole.toLowerCase(),
        updatedAt: new Date(),
      },
      { new: true }
    ).select("-password");

    if (!user) {
      throw new Error("User not found");
    }

    return user;
  } catch (error) {
    throw new Error(`Failed to update user role: ${error.message}`);
  }
};

/**
 * Verify user account
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Updated user object
 */
const verifyUser = async (userId) => {
  try {
    const user = await StaffMaster.findByIdAndUpdate(
      userId,
      {
        verified: true,
        updatedAt: new Date(),
      },
      { new: true }
    ).select("-password");

    if (!user) {
      throw new Error("User not found");
    }

    return user;
  } catch (error) {
    throw new Error(`Failed to verify user: ${error.message}`);
  }
};

/**
 * Ban/unban user
 * @param {string} userId - User ID
 * @param {boolean} banned - Ban status
 * @returns {Promise<Object>} Updated user object
 */
const setBanStatus = async (userId, banned = true) => {
  try {
    const user = await StaffMaster.findByIdAndUpdate(
      userId,
      {
        banned: banned,
        updatedAt: new Date(),
      },
      { new: true }
    ).select("-password");

    if (!user) {
      throw new Error("User not found");
    }

    return user;
  } catch (error) {
    throw new Error(`Failed to update ban status: ${error.message}`);
  }
};

/**
 * Get all users with optional filtering
 * @param {Object} filters - Filter options
 * @param {string} [filters.role] - Filter by role
 * @param {boolean} [filters.verified] - Filter by verification status
 * @param {boolean} [filters.banned] - Filter by ban status
 * @returns {Promise<Array>} Array of users
 */
const getAllUsers = async (filters = {}) => {
  try {
    const query = {};

    if (filters.role) {
      query.role = filters.role.toLowerCase();
    }

    if (filters.verified !== undefined) {
      query.verified = filters.verified;
    }

    if (filters.banned !== undefined) {
      query.banned = filters.banned;
    }

    const users = await StaffMaster.find(query)
      .select("-password")
      .populate("workingPlace", "factoryName")
      .sort({ createdAt: -1 });

    return users;
  } catch (error) {
    throw new Error(`Failed to get users: ${error.message}`);
  }
};

/**
 * Delete user (soft delete by banning)
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Updated user object
 */
const deleteUser = async (userId) => {
  try {
    // Instead of hard delete, we ban the user
    return await setBanStatus(userId, true);
  } catch (error) {
    throw new Error(`Failed to delete user: ${error.message}`);
  }
};

/**
 * Reset user password
 * @param {string} userId - User ID
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Updated user object
 */
const resetUserPassword = async (userId, newPassword) => {
  try {
    if (newPassword.length < 6) {
      throw new Error("Password must be at least 6 characters long");
    }

    // Hash password if bcrypt is available
    let hashedPassword = newPassword;
    try {
      if (bcrypt) {
        const saltRounds = 10;
        hashedPassword = await bcrypt.hash(newPassword, saltRounds);
      }
    } catch (error) {
      console.log("Note: bcrypt not available, storing plain text password");
    }

    const user = await StaffMaster.findByIdAndUpdate(
      userId,
      {
        password: hashedPassword,
        updatedAt: new Date(),
      },
      { new: true }
    ).select("-password");

    if (!user) {
      throw new Error("User not found");
    }

    return user;
  } catch (error) {
    throw new Error(`Failed to reset password: ${error.message}`);
  }
};

/**
 * Check if user exists
 * @param {string} email - User email
 * @returns {Promise<boolean>} True if user exists
 */
const userExists = async (email) => {
  try {
    const user = await StaffMaster.findOne({ email: email.toLowerCase() });
    return !!user;
  } catch (error) {
    throw new Error(`Failed to check user existence: ${error.message}`);
  }
};

module.exports = {
  createAdminUser,
  createRootUser,
  updateUserRole,
  verifyUser,
  setBanStatus,
  getAllUsers,
  deleteUser,
  resetUserPassword,
  userExists,
  getOrCreateDefaultFactory,
};
