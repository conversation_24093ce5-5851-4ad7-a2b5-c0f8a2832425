/**
 * Serial Number Utility Functions
 * 
 * This module provides utility functions for managing cylinder serial numbers
 * in the new format: Letter + 4 digits (A0001, A0002, etc.)
 */

const CylinderMaster = require('../models/CylinderMaster');

/**
 * Generate the next serial number in sequence
 * Format: A0001, A0002, ..., A9999, B0001, B0002, etc.
 * @returns {Promise<string>} Next serial number
 */
const generateNextSerialNumber = async () => {
    try {
        // Find the latest cylinder with the new format (starts with a letter)
        const latestCylinder = await CylinderMaster
            .findOne({ 
                serialNumber: { $regex: /^[A-Z]\d{4}$/ } 
            })
            .sort({ serialNumber: -1 })
            .exec();

        if (!latestCylinder) {
            // No cylinders with new format exist, start with A0001
            return 'A0001';
        }

        const currentSerial = latestCylinder.serialNumber;
        const currentLetter = currentSerial.charAt(0);
        const currentNumber = parseInt(currentSerial.substring(1));

        // Calculate next serial number
        if (currentNumber < 9999) {
            // Increment number within same letter
            const nextNumber = (currentNumber + 1).toString().padStart(4, '0');
            return `${currentLetter}${nextNumber}`;
        } else {
            // Move to next letter, reset to 0001
            const nextLetterCode = currentLetter.charCodeAt(0) + 1;
            if (nextLetterCode > 90) { // Beyond 'Z'
                throw new Error('Serial number limit exceeded (Z9999)');
            }
            const nextLetter = String.fromCharCode(nextLetterCode);
            return `${nextLetter}0001`;
        }
    } catch (error) {
        console.error('Error generating serial number:', error);
        throw error;
    }
};

/**
 * Validate serial number format
 * @param {string} serialNumber - Serial number to validate
 * @returns {object} Validation result with format type
 */
const validateSerialNumber = (serialNumber) => {
    if (!serialNumber || typeof serialNumber !== 'string') {
        return { valid: false, format: 'invalid', message: 'Serial number is required and must be a string' };
    }

    // New format: Letter + 4 digits (A0001, B0002, etc.)
    if (/^[A-Z]\d{4}$/.test(serialNumber)) {
        return { valid: true, format: 'new', message: 'Valid new format (Letter + 4 digits)' };
    }

    // Old format: CYD + date + number
    if (/^CYD\d+$/.test(serialNumber)) {
        return { valid: true, format: 'old', message: 'Valid old format (CYD + date + number)' };
    }

    return { valid: false, format: 'unknown', message: 'Invalid serial number format' };
};

/**
 * Check if serial number already exists
 * @param {string} serialNumber - Serial number to check
 * @returns {Promise<boolean>} True if exists, false otherwise
 */
const serialNumberExists = async (serialNumber) => {
    try {
        const cylinder = await CylinderMaster.findOne({ serialNumber });
        return !!cylinder;
    } catch (error) {
        console.error('Error checking serial number existence:', error);
        throw error;
    }
};

/**
 * Get serial number statistics
 * @returns {Promise<object>} Statistics about serial numbers
 */
const getSerialNumberStats = async () => {
    try {
        const [
            totalCylinders,
            newFormatCount,
            oldFormatCount,
            latestNewFormat,
            latestOldFormat
        ] = await Promise.all([
            CylinderMaster.countDocuments(),
            CylinderMaster.countDocuments({ serialNumber: { $regex: /^[A-Z]\d{4}$/ } }),
            CylinderMaster.countDocuments({ serialNumber: { $regex: /^CYD\d+$/ } }),
            CylinderMaster.findOne({ serialNumber: { $regex: /^[A-Z]\d{4}$/ } }).sort({ serialNumber: -1 }),
            CylinderMaster.findOne({ serialNumber: { $regex: /^CYD\d+$/ } }).sort({ createdAt: -1 })
        ]);

        return {
            total: totalCylinders,
            newFormat: {
                count: newFormatCount,
                latest: latestNewFormat ? latestNewFormat.serialNumber : null,
                nextAvailable: await generateNextSerialNumber()
            },
            oldFormat: {
                count: oldFormatCount,
                latest: latestOldFormat ? latestOldFormat.serialNumber : null
            },
            percentage: {
                newFormat: totalCylinders > 0 ? (newFormatCount / totalCylinders * 100).toFixed(2) : 0,
                oldFormat: totalCylinders > 0 ? (oldFormatCount / totalCylinders * 100).toFixed(2) : 0
            }
        };
    } catch (error) {
        console.error('Error getting serial number statistics:', error);
        throw error;
    }
};

/**
 * Convert old format serial numbers to new format (for migration)
 * @param {number} limit - Maximum number of cylinders to convert per batch
 * @returns {Promise<object>} Migration result
 */
const migrateOldSerialNumbers = async (limit = 100) => {
    try {
        // Find cylinders with old format serial numbers
        const oldFormatCylinders = await CylinderMaster
            .find({ serialNumber: { $regex: /^CYD\d+$/ } })
            .limit(limit)
            .sort({ createdAt: 1 }); // Oldest first

        if (oldFormatCylinders.length === 0) {
            return { success: true, converted: 0, message: 'No old format serial numbers found' };
        }

        const converted = [];
        const errors = [];

        for (const cylinder of oldFormatCylinders) {
            try {
                const oldSerial = cylinder.serialNumber;
                const newSerial = await generateNextSerialNumber();
                
                // Update the cylinder with new serial number
                cylinder.serialNumber = newSerial;
                await cylinder.save();
                
                converted.push({ oldSerial, newSerial, id: cylinder._id });
            } catch (error) {
                errors.push({ 
                    cylinderId: cylinder._id, 
                    oldSerial: cylinder.serialNumber, 
                    error: error.message 
                });
            }
        }

        return {
            success: true,
            converted: converted.length,
            errors: errors.length,
            details: { converted, errors },
            message: `Successfully converted ${converted.length} serial numbers${errors.length > 0 ? `, ${errors.length} errors` : ''}`
        };
    } catch (error) {
        console.error('Error migrating serial numbers:', error);
        return {
            success: false,
            converted: 0,
            errors: 1,
            message: `Migration failed: ${error.message}`
        };
    }
};

/**
 * Generate multiple serial numbers in sequence
 * @param {number} count - Number of serial numbers to generate
 * @returns {Promise<string[]>} Array of serial numbers
 */
const generateMultipleSerialNumbers = async (count) => {
    try {
        const serialNumbers = [];
        
        for (let i = 0; i < count; i++) {
            const nextSerial = await generateNextSerialNumber();
            serialNumbers.push(nextSerial);
            
            // Create a temporary cylinder to reserve this serial number
            // This prevents duplicate serial numbers when generating multiple at once
            const tempCylinder = new CylinderMaster({
                cylinderSize: '47L', // Temporary values
                importDate: new Date(),
                valueType: 'Japan',
                productionDate: new Date(),
                originalNumber: `TEMP_${Date.now()}_${i}`,
                workingPressure: 1000,
                status: 'Empty',
                serialNumber: nextSerial,
                owner: null // Will be set when actually creating the cylinder
            });
            
            // Save temporarily to reserve the serial number
            await tempCylinder.save();
        }
        
        return serialNumbers;
    } catch (error) {
        console.error('Error generating multiple serial numbers:', error);
        throw error;
    }
};

/**
 * Get available serial number ranges
 * @returns {Promise<object>} Available ranges information
 */
const getAvailableRanges = async () => {
    try {
        const stats = await getSerialNumberStats();
        const currentLetter = stats.newFormat.latest ? stats.newFormat.latest.charAt(0) : 'A';
        const currentNumber = stats.newFormat.latest ? parseInt(stats.newFormat.latest.substring(1)) : 0;
        
        const remainingInCurrentLetter = 9999 - currentNumber;
        const remainingLetters = 90 - currentLetter.charCodeAt(0); // 90 is 'Z'
        const totalRemaining = remainingInCurrentLetter + (remainingLetters * 9999);
        
        return {
            currentLetter,
            currentNumber,
            remainingInCurrentLetter,
            remainingLetters,
            totalRemaining,
            capacity: {
                perLetter: 9999,
                totalLetters: 26,
                totalCapacity: 26 * 9999 // 259,974 total possible serial numbers
            }
        };
    } catch (error) {
        console.error('Error getting available ranges:', error);
        throw error;
    }
};

module.exports = {
    generateNextSerialNumber,
    validateSerialNumber,
    serialNumberExists,
    getSerialNumberStats,
    migrateOldSerialNumbers,
    generateMultipleSerialNumbers,
    getAvailableRanges
};
