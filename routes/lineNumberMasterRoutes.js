const express = require('express');
const router = express.Router();
const {
  createLineNumber,
  getAllLineNumbers,
  getLineNumberById,
  updateLineNumber,
  deleteLineNumber
} = require('../controllers/lineNumberMasterController');
const authMiddleware = require('../middleware/authMiddleware');

router.post('/',authMiddleware(['admin','manager']), createLineNumber);
router.get('/',authMiddleware(['admin','manager','fill']),  getAllLineNumbers);
router.get('/:id',authMiddleware(['admin','manager','fill']),  getLineNumberById);
router.put('/:id',authMiddleware(['admin','manager']),  updateLineNumber);
router.delete('/:id',authMiddleware(['admin','manager']),  deleteLineNumber);

module.exports = router;
