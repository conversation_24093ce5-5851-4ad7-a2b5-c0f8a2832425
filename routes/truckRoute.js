const express = require('express');
const router = express.Router();
const truckController = require('../controllers/truckController');
const authMiddleware = require('../middleware/authMiddleware');

// Get all trucks
router.get('/',authMiddleware(['admin','manager','staff','driver']), truckController.getAllTrucks);
router.get('/available',authMiddleware(['admin','manager','staff','driver']), truckController.getAvailableTrucks);

// Get a single truck by ID
router.get('/:id',authMiddleware(['admin','manager','staff','driver']), truckController.getTruckById);

// Create a new truck
router.post('/',authMiddleware(['admin','manager']), truckController.createTruck);

// Update a truck by ID
router.put('/:id',authMiddleware(['admin','manager']), truckController.updateTruck);

// Delete a truck by ID
router.delete('/:id',authMiddleware(['admin','manager']), truckController.deleteTruck);

module.exports = router;
