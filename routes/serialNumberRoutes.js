/**
 * Serial Number Routes
 * 
 * Routes for managing cylinder serial numbers, statistics, and migration.
 */

const express = require('express');
const router = express.Router();
const SerialNumberController = require('../controllers/SerialNumberController');
const authMiddleware = require('../middleware/authMiddleware');

// ==================== SERIAL NUMBER MANAGEMENT ROUTES ====================

/**
 * Get serial number statistics
 * GET /api/serial-numbers/stats
 * Access: admin, manager, root
 */
router.get(
    '/stats',
    authMiddleware(['root', 'admin', 'manager']),
    SerialNumberController.getStats
);

/**
 * Get next available serial number (preview)
 * GET /api/serial-numbers/next
 * Access: admin, manager, root
 */
router.get(
    '/next',
    authMiddleware(['root', 'admin', 'manager']),
    SerialNumberController.getNextSerialNumber
);

/**
 * Validate serial number format
 * POST /api/serial-numbers/validate
 * Body: { serialNumber: string }
 * Access: admin, manager, root, fill, sale
 */
router.post(
    '/validate',
    authMiddleware(['root', 'admin', 'manager', 'fill', 'sale']),
    SerialNumberController.validateSerialNumber
);

/**
 * Get cylinders by serial number format
 * GET /api/serial-numbers/cylinders?format=new|old&page=1&limit=50
 * Access: admin, manager, root, fill, sale
 */
router.get(
    '/cylinders',
    authMiddleware(['root', 'admin', 'manager', 'fill', 'sale']),
    SerialNumberController.getCylindersByFormat
);

/**
 * Search cylinders by serial number pattern
 * GET /api/serial-numbers/search?pattern=A00&limit=20
 * Access: admin, manager, root, fill, sale
 */
router.get(
    '/search',
    authMiddleware(['root', 'admin', 'manager', 'fill', 'sale']),
    SerialNumberController.searchByPattern
);

/**
 * Get serial number format information
 * GET /api/serial-numbers/format-info
 * Access: admin, manager, root, fill, sale
 */
router.get(
    '/format-info',
    authMiddleware(['root', 'admin', 'manager', 'fill', 'sale']),
    SerialNumberController.getFormatInfo
);

// ==================== MIGRATION ROUTES ====================

/**
 * Migrate old format serial numbers to new format
 * POST /api/serial-numbers/migrate
 * Body: { limit?: number, dryRun?: boolean }
 * Access: root, admin only
 */
router.post(
    '/migrate',
    authMiddleware(['root', 'admin']),
    SerialNumberController.migrateSerialNumbers
);

/**
 * Generate bulk serial numbers for preview
 * POST /api/serial-numbers/generate-bulk
 * Body: { count: number }
 * Access: root, admin only
 */
router.post(
    '/generate-bulk',
    authMiddleware(['root', 'admin']),
    SerialNumberController.generateBulkSerialNumbers
);

// ==================== UTILITY ROUTES ====================

/**
 * Health check for serial number routes
 * GET /api/serial-numbers/health
 * Access: public
 */
router.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Serial number routes are healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

module.exports = router;
