const express = require("express");
const router = express.Router();
const PublicController = require("../controllers/PublicController");

// Public login endpoint
router.post("/login", PublicController.login);

// Public report endpoints (require authentication)
router.get("/daily-overview", PublicController.getDailyOverview);
router.get("/daily-by-customer", PublicController.getDailyByCustomer);
router.get("/daily-by-customer-type", PublicController.getDailyByCustomerType);

// Filling process reports
router.get("/filling-overview", PublicController.getFillingOverview);
router.get("/filling-by-line", PublicController.getFillingByLine);
router.get("/filling-by-staff", PublicController.getFillingByStaff);

// Export endpoints
router.get("/export/daily-overview", PublicController.exportDailyOverview);
router.get("/export/daily-by-customer", PublicController.exportDailyByCustomer);
router.get(
  "/export/daily-by-customer-type",
  PublicController.exportDailyByCustomerType
);
router.get("/export/filling-overview", PublicController.exportFillingOverview);

// Logs endpoint
router.get("/logs", PublicController.getLogs);

// Verify token endpoint
router.get("/verify-token", PublicController.verifyToken);

module.exports = router;
