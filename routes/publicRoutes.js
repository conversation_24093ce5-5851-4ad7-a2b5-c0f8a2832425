const express = require("express");
const router = express.Router();
const PublicController = require("../controllers/PublicController");

// Public login endpoint
router.post("/login", PublicController.login);

// Public report endpoints (require authentication)
router.get("/daily-overview", PublicController.getDailyOverview);
router.get("/daily-by-customer", PublicController.getDailyByCustomer);
router.get("/daily-by-customer-type", PublicController.getDailyByCustomerType);

// Export endpoints
router.get("/export/daily-overview", PublicController.exportDailyOverview);
router.get("/export/daily-by-customer", PublicController.exportDailyByCustomer);
router.get("/export/daily-by-customer-type", PublicController.exportDailyByCustomerType);

// Verify token endpoint
router.get("/verify-token", PublicController.verifyToken);

module.exports = router;
