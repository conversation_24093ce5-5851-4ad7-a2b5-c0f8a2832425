const express = require('express');
const router = express.Router();
const { getDailySalesReport,getDailySalesReportByProduct,getDailySalesReportBySalesperson } = require('../controllers/report');
const {getSellPreport} = require('../controllers/sellReport');
const {getFillReport} = require('../controllers/fillReport');
const {getCylidnerReport} = require('../controllers/cylinderReport');

const authMiddleware = require('../middleware/authMiddleware');

router.get('/sales-report/:date', getDailySalesReport);
router.get('/sales-report-by-product/:date', getDailySalesReportByProduct);
router.get('/sales-report-by-salesperson/:date', getDailySalesReportBySalesperson);
router.get('/sales/all',authMiddleware(['admin','manager',]), getSellPreport);
router.get('/fill/all',authMiddleware(['admin','manager',]), getFillReport);
router.get('/cylinder/all',authMiddleware(['admin','manager',]), getCylidnerReport);
module.exports = router;