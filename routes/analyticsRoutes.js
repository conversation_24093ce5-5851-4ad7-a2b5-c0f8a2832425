const express = require("express");
const router = express.Router();
const AnalyticsController = require("../controllers/AnalyticsController");
const authMiddleware = require("../middleware/authMiddleware");

// Daily Analytics
router.get(
  "/daily/:date",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getDailyAnalytics
);

// Monthly Analytics
router.get(
  "/monthly/:date",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getMonthlyAnalytics
);

// Yearly Analytics
router.get(
  "/yearly/:year",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getYearlyAnalytics
);

// Period Analytics (Custom Date Range)
router.get(
  "/period",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getPeriodAnalytics
);

// Cylinder Return Analytics
router.get(
  "/returns",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getCylinderReturnAnalytics
);

// Combined Sales and Returns Summary
router.get(
  "/summary",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getCombinedSummary
);

// Daily Overview Reports (Tabular format)
router.get(
  "/overview/daily",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getDailyOverview
);

// Daily Overview by Customer
router.get(
  "/overview/customer",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getDailyOverviewByCustomer
);

// Daily Overview by Customer Type
router.get(
  "/overview/customer-type",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.getDailyOverviewByCustomerType
);

// Excel Export Routes
// Export Daily Overview to Excel
router.get(
  "/export/daily-overview",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.exportDailyOverviewToExcel
);

// Export Daily Overview by Customer to Excel
router.get(
  "/export/customer-overview",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.exportDailyOverviewByCustomerToExcel
);

// Export Daily Overview by Customer Type to Excel
router.get(
  "/export/customer-type-overview",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.exportDailyOverviewByCustomerTypeToExcel
);

// Export Analytics Summary to Excel
router.get(
  "/export/summary",
  authMiddleware(["root", "admin", "manager"]),
  AnalyticsController.exportAnalyticsSummaryToExcel
);

module.exports = router;
