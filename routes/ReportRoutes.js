const express = require('express');
const router = express.Router();
const ReportController = require('../controllers/ReportController');
const authMiddleware = require('../middleware/authMiddleware');

// Get report for a specific period with start date
router.get('/:period/:startDate', authMiddleware(["admin", "manager"]), ReportController.getReport);

// Get report for a custom date range
router.get('/custom/:startDate/:endDate', authMiddleware(["admin", "manager"]), ReportController.getCustomReport);

module.exports = router;
