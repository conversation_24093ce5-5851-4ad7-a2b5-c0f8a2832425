const express = require('express');
const router = express.Router();
const { getAllMaintenances, } = require('../controllers/maintainController');
const authMiddleware = require('../middleware/authMiddleware');
const { get } = require('mongoose');

router.get('/', authMiddleware(['admin', 'manager', ]), getAllMaintenances);
// router.get('/:id', authMiddleware(['admin', 'manager',]), getMaintainById);
// router.delete('/:id', authMiddleware(['admin', 'manager']), deleteMaintain);
module.exports = router;