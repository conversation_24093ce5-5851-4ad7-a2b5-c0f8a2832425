const express = require('express');
const router = express.Router();
const {
    createFillingProcess,
    getAllFillingProcesses,
    getFillingProcessById,
    updateFillingProcess,
    deleteFillingProcess,
    getFillingProcessesByLineNumberAndDate,
    getFillingProcessesByDate,
    startProcess,
    addCylinder,
    updateCylinderStatus,
    endProcess,
    getFillingProcessReport,
    removeCylinder,
    getFillingProcessReportByDate,
    getFillingProcessesByLineNumberToday,
    getFillingReport
} = require('../controllers/FillingProcessController');
const authMiddleware = require('../middleware/authMiddleware');

// Create a new filling process
router.post('/', authMiddleware(["admin", "manager", "fill"]), createFillingProcess);

// Get all filling processes
router.get('/', authMiddleware(["admin", "manager", "fill"]), getAllFillingProcesses);

// Get a single filling process by ID
router.get('/:id', authMiddleware(["admin", "manager", "fill"]), getFillingProcessById);

// Update a filling process by ID
router.put('/:id', authMiddleware(["admin", "manager", "fill"]), updateFillingProcess);

// Delete a filling process by ID
router.delete('/:id', authMiddleware(["admin", "manager"]), deleteFillingProcess);

// Get filling processes by line number and date
router.get('/date/:date/lineNumber/:lineNumber', authMiddleware(['admin', 'manager', 'fill']), getFillingProcessesByLineNumberAndDate);

// Get filling processes by date
router.get('/date/:date', authMiddleware(['admin', 'manager', 'fill']), getFillingProcessesByDate);

// Get filling processes by line number for today
router.get('/today/:date/lineNumber/:lineNumber', authMiddleware(['admin', 'manager', 'fill']), getFillingProcessesByLineNumberToday);

// Start a filling process
router.post('/start', authMiddleware(["admin", "manager", "fill"]), startProcess);

// Add a cylinder to the filling process
router.post('/:processId/add-cylinder', authMiddleware(["admin", "manager", "fill"]), addCylinder);

// Update the status of a cylinder in the filling process
router.post('/:processId/update-cylinder-status', authMiddleware(["admin", "manager", "fill"]), updateCylinderStatus);

// End the filling process
router.post('/:processId/end', authMiddleware(["admin", "manager", "fill"]), endProcess);

// Get filling process report
router.get('/report/:period', authMiddleware(["admin", "manager", "fill"]), getFillingProcessReport);

// Get filling process report by date
router.get('/report/date/:date', authMiddleware(["admin", "manager", "fill"]), getFillingProcessReportByDate);

// Remove a cylinder from the filling process
router.post('/:processId/remove-cylinder', authMiddleware(["admin", "manager", "fill"]), removeCylinder);

router.get('/summary/:date', authMiddleware(['admin', 'manager' ]), getFillingReport);

module.exports = router;
