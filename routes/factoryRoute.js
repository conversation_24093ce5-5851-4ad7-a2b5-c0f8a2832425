const express = require('express');
const router = express.Router();
const factoryController = require('../controllers/factoryController');

router.get('/', factoryController.getAllFactories);
router.get('/:id', factoryController.getFactoryById);
router.post('/', factoryController.createFactory);
router.put('/:id', factoryController.updateFactory);
router.delete('/:id', factoryController.deleteFactory);

module.exports = router;
