const express = require('express');
const mongoose = require('mongoose');

// Import your models
const CylinderMaster = require('../models/CylinderMaster');
const TruckMaster = require('../models/TruckMaster');
const FillingProcess = require('../models/FillingProcessModel');
const RoundMaster = require('../models/RoundMaster');
const CustomerMaster = require('../models/CustomerMaster');
const SellMaster = require(
  '../models/SellMaster'  
)
const moment = require('moment-timezone');
const router = express.Router();
const authMiddleware = require('../middleware/authMiddleware'); // Import your auth middleware

router.get('/truck-round-report', authMiddleware(['admin', 'manager', 'staff']), async (req, res) => {
    try {
      const { startDate, endDate } = req.query; // Expecting startDate and endDate as query parameters (YYYY-MM-DD)
  
      // Validate date formats
      if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
      }
  
      const start = new Date(startDate);
      const end = new Date(endDate);
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({ message: 'Invalid date values' });
      }
      if (start > end) {
        return res.status(400).json({ message: 'Start date must be before end date' });
      }
  
      // Set time boundaries
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
  
      // Fetch rounds within the period for the user's working place (factory)
      const rounds = await RoundMaster.find({
        createdAt: { $gte: start, $lte: end },
        owner: req.user.workingPlace
      })
        .populate('truck', 'truckType truckNumber status')
        .populate('sell', 'total discount quantity payment returnedCylinders')
        .populate('deliveringCylinders', 'cylinderSize status')
        .populate('returnedCylinders', 'cylinderSize status');
  
      // Fetch all trucks for reference
      const trucks = await TruckMaster.find({ owner: req.user.workingPlace });
  
      // Aggregate rounds by truck
      const truckRoundReport = trucks.map(truck => {
        const truckRounds = rounds.filter(round => round.truck._id.toString() === truck._id.toString());
  
        // Calculate metrics for this truck
        const totalRounds = truckRounds.length;
        const totalSales = truckRounds.reduce((sum, round) => {
          return sum + (round.sell.reduce((s, sale) => s + (sale.total || 0), 0));
        }, 0);
        const totalDiscount = truckRounds.reduce((sum, round) => {
          return sum + (round.sell.reduce((s, sale) => s + (sale.discount || 0), 0));
        }, 0);
        const netTotalSales = totalSales - totalDiscount;
        const totalDeliveries = truckRounds.reduce((sum, round) => sum + round.deliveringCylinders.length, 0);
        const totalReturns = truckRounds.reduce((sum, round) => sum + round.returnedCylinders.length, 0);
        const totalCashSales = truckRounds.reduce((sum, round) => {
          return sum + (round.sell.reduce((s, sale) => (sale.payment === 'Cash' ? s + sale.total : s), 0));
        }, 0);
        const totalCreditSales = truckRounds.reduce((sum, round) => {
          return sum + (round.sell.reduce((s, sale) => (sale.payment === 'Credit' ? s + sale.total : s), 0));
        }, 0);
  
        return {
          truck: {
            id: truck._id,
            truckType: truck.truckType,
            truckNumber: truck.truckNumber,
            status: truck.status
          },
          totalRounds,
          totalSales,
          totalDiscount,
          netTotalSales,
          totalDeliveries,
          totalReturns,
          totalCashSales,
          totalCreditSales,
          rounds: truckRounds.map(round => ({
            serialNumber: round.serialNumber,
            date: round.date,
            status: round.status,
            salesCount: round.sell.length,
            deliveries: round.deliveringCylinders.length,
            returns: round.returnedCylinders.length
          }))
        };
      }).filter(report => report.totalRounds > 0); // Only include trucks with rounds
  
      // Overall totals
      const overallTotals = {
        totalRounds: rounds.length,
        totalSales: rounds.reduce((sum, round) => sum + (round.sell.reduce((s, sale) => s + (sale.total || 0), 0)), 0),
        totalDiscount: rounds.reduce((sum, round) => sum + (round.sell.reduce((s, sale) => s + (sale.discount || 0), 0)), 0),
        totalDeliveries: rounds.reduce((sum, round) => sum + round.deliveringCylinders.length, 0),
        totalReturns: rounds.reduce((sum, round) => sum + round.returnedCylinders.length, 0),
        totalCashSales: rounds.reduce((sum, round) => {
          return sum + (round.sell.reduce((s, sale) => (sale.payment === 'Cash' ? s + sale.total : s), 0));
        }, 0),
        totalCreditSales: rounds.reduce((sum, round) => {
          return sum + (round.sell.reduce((s, sale) => (sale.payment === 'Credit' ? s + sale.total : s), 0));
        }, 0)
      };
      overallTotals.netTotalSales = overallTotals.totalSales - overallTotals.totalDiscount;
  
      // Final response
      const report = {
        period: {
          startDate: moment(start).format('YYYY-MM-DD'),
          endDate: moment(end).format('YYYY-MM-DD')
        },
        overallTotals,
        truckRoundReport
      };
  
      res.status(200).json(report);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  })
  router.get('/today-truck-round-report', authMiddleware(['admin', 'manager', 'staff']), async (req, res) => {
    try {
      // Set today's date boundaries
      const today = new Date();
      const start = new Date(today.setHours(0, 0, 0, 0)); // Start of today
      const end = new Date(today.setHours(23, 59, 59, 999)); // End of today
  
      // Fetch rounds for today for the user's working place (factory)
      const rounds = await RoundMaster.find({
        createdAt: { $gte: start, $lte: end },
        owner: req.user.workingPlace
      })
        .populate('truck', 'truckType truckNumber status')
        .populate('deliveringCylinders', 'cylinderSize status')
        .populate('returnedCylinders', 'cylinderSize status');
  
      // Fetch sales directly from SellMaster for today
      const sales = await SellMaster.find({
        owner: req.user.workingPlace,
        date: { $gte: start, $lte: end }
      })
        .populate('cylinders.cylinder', 'cylinderSize')
        .populate('customer', 'name')
        .populate('staff', 'name')
        .populate('returnedCylinders', 'cylinderSize');
  
      // Fetch all trucks for reference
      const trucks = await TruckMaster.find({ owner: req.user.workingPlace })
        .populate('lastUsed', 'name');
  
      // Calculate truck status counts
      const availableTrucks = trucks.filter(truck => truck.status === 'Available').length;
      const unavailableTrucks = trucks.filter(truck => truck.status === 'NotAvailable').length;
  
      // Aggregate rounds and sales by truck
      const truckRoundReport = trucks.map(truck => {
        const truckRounds = rounds.filter(round => round.truck._id.toString() === truck._id.toString());
        const truckSales = sales.filter(sale => truckRounds.some(round => sale.round?.toString() === round._id.toString()));
  
        // Calculate metrics for this truck
        const totalRounds = truckRounds.length;
        const totalSales = truckSales.reduce((sum, sale) => sum + sale.total, 0);
        const totalDiscount = truckSales.reduce((sum, sale) => sum + (sale.discount || 0), 0);
        const netTotalSales = totalSales - totalDiscount;
        const totalDeliveries = truckRounds.reduce((sum, round) => sum + round.deliveringCylinders.length, 0);
        const totalReturns = truckSales.reduce((sum, sale) => sum + sale.returnedCylinders.length, 0);
        const totalCashSales = truckSales.reduce((sum, sale) => sale.payment === 'Cash' ? sum + sale.total : sum, 0);
        const totalCreditSales = truckSales.reduce((sum, sale) => sale.payment === 'Credit' ? sum + sale.total : sum, 0);
        const totalCylindersSold = truckSales.reduce((sum, sale) => sum + sale.cylinders.length, 0);
  
        return {
          truck: {
            id: truck._id,
            truckType: truck.truckType,
            truckNumber: truck.truckNumber,
            status: truck.status
          },
          totalRounds,
          totalSales,
          totalDiscount,
          netTotalSales,
          totalDeliveries,
          totalReturns,
          totalCashSales,
          totalCreditSales,
          totalCylindersSold, // New metric from SellMaster cylinders array
          rounds: truckRounds.map(round => {
            const roundSales = truckSales.filter(sale => sale.round?.toString() === round._id.toString());
            return {
              serialNumber: round.serialNumber,
              date: round.date,
              status: round.status,
              salesCount: roundSales.length,
              deliveries: round.deliveringCylinders.length,
              returns: roundSales.reduce((sum, sale) => sum + sale.returnedCylinders.length, 0),
              totalSales: roundSales.reduce((sum, sale) => sum + sale.total, 0)
            };
          })
        };
      }).filter(report => report.totalRounds > 0 || report.totalSales > 0); // Include trucks with sales or rounds
  
      // Overall totals for today
      const overallTotals = {
        totalRounds: rounds.length,
        totalSales: sales.reduce((sum, sale) => sum + sale.total, 0),
        totalDiscount: sales.reduce((sum, sale) => sum + (sale.discount || 0), 0),
        totalDeliveries: rounds.reduce((sum, round) => sum + round.deliveringCylinders.length, 0),
        totalReturns: sales.reduce((sum, sale) => sum + sale.returnedCylinders.length, 0),
        totalCashSales: sales.reduce((sum, sale) => sale.payment === 'Cash' ? sum + sale.total : sum, 0),
        totalCreditSales: sales.reduce((sum, sale) => sale.payment === 'Credit' ? sum + sale.total : sum, 0),
        totalCylindersSold: sales.reduce((sum, sale) => sum + sale.cylinders.length, 0),
        totalTrucks: trucks.length,
        availableTrucks,
        unavailableTrucks
      };
      overallTotals.netTotalSales = overallTotals.totalSales - overallTotals.totalDiscount;
  
      // Truck list (all trucks, active or not)
      const truckList = trucks.map(truck => ({
        id: truck._id,
        truckType: truck.truckType,
        truckNumber: truck.truckNumber,
        status: truck.status,
        lastUsed: truck.lastUsed ? { id: truck.lastUsed._id, name: truck.lastUsed.name } : null
      }));
  
      // Final response
      const report = {
        date: moment(start).format('YYYY-MM-DD'),
        overallTotals,
        truckRoundReport,
        truckList
      };
  
      res.status(200).json(report);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
/**
 * Cylinders Monitoring
 */


router.get('/cylinders/status', authMiddleware(['admin', 'manager']), async (req, res) => {
    try {
        const owner = new mongoose.Types.ObjectId(req.user.workingPlace);
        console.log(`req owner (after conversion):`, owner);

        // Aggregate cylinder statuses
        const statuses = await CylinderMaster.aggregate([
            { $match: { owner } }, 
            { $group: { _id: '$status', count: { $sum: 1 } } }
        ]);

        // Aggregate unique customer IDs with counts
        const customerAggregation = await CylinderMaster.aggregate([
            { $match: { owner } },
            { $group: { _id: '$takenCustomer', count: { $sum: 1 } } },
            { $match: { _id: { $ne: null } } } // Exclude null customer IDs if applicable
        ]);

        // Extract customer IDs from aggregation result
        const customerIds = customerAggregation.map(item => item._id);

        // Fetch customer details using the IDs
        const customers = await CustomerMaster.find(
            { _id: { $in: customerIds } },
            'name customerType' // Select only name and customerType
        ).lean();

        // Combine counts with customer details
        const customersWithCounts = customers.map(customer => {
            const aggData = customerAggregation.find(item => item._id.equals(customer._id));
            return {
                ...customer,
                count: aggData ? aggData.count : 0
            };
        });

        console.log("Aggregation result:", statuses);

        // Calculate total cylinder count where owned is true
        const totalCylinders = await CylinderMaster.countDocuments({ owner, owned: true });

        res.json({ statuses, totalCylinders, customers: customersWithCounts });

    } catch (error) {
        console.error("Error fetching cylinder statuses:", error);
        res.status(500).json({ message: 'Error fetching cylinder statuses', error: error.message });
    }
});
  

// Get detailed information about all cylinders
router.get('/cylinders/details',authMiddleware(['admin','manager']), async (req, res) => {
    try {
        const owner = req.user.workingPlace; // Extract workingPlace from authenticated user

        const cylinders = await CylinderMaster.find({ owner })
            .populate('owner', 'factoryInCharge') // Populate factory owner details
            .populate('takenCustomer', 'name customerType') // Populate customer details
            .populate('takenTruck', 'truckNumber') // Populate truck details
            .populate('latestDelivery', 'date roundTime') // Populate latest delivery info
            .populate('latestFilling', 'fillingStartedAt fillingEndedAt'); // Populate latest filling info

        res.json(cylinders);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching cylinder details', error });
    }
});

/**
 * Trucks Monitoring
 */

// Get all trucks and their availability
router.get('/trucks/status',authMiddleware(['admin','manager']), async (req, res) => {
    try {
        const owner = req.user.workingPlace; // Extract workingPlace from authenticated user

        const trucks = await TruckMaster.find({owner})
            .populate('owner', 'factoryInCharge'); // Populate factory owner details

        res.json(trucks);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching truck statuses', error });
    }
});

// Get trucks currently in use with their assigned rounds
router.get('/trucks/in-use',authMiddleware(['admin','manager']), async (req, res) => {
    try {
        const owner = req.user.workingPlace; // Extract workingPlace from authenticated user

        const inUseTrucks = await RoundMaster.find({ status: { $ne: 'Delivery Completed' }, owner  })
            .populate('truck', 'truckNumber status')
            .populate('deliveringCylinders', 'serialNumber status')
            .populate('customers', 'name customerType');

        res.json(inUseTrucks);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching trucks in use', error });
    }
});

/**
 * Filling Process Monitoring
 */

// Get all filling processes with metrics
router.get('/filling-processes/details',authMiddleware(['admin','manager']), async (req, res) => {
    try {
        const owner = req.user.workingPlace; // Extract workingPlace from authenticated user

        const fillingProcesses = await FillingProcess.find({owner})
            .populate('lineNumber', 'lineNumber type') // Populate line number details
            .populate('startedBy', 'name') // Populate staff who started the process
            .populate('endedBy', 'name') // Populate staff who ended the process
            .populate('cylinders', 'serialNumber status'); // Populate cylinder details

        res.json(fillingProcesses);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching filling processes', error });
    }
});

/**
 * Delivery Rounds Monitoring
 */

// Get all delivery rounds with detailed information
router.get('/delivery-rounds/details',authMiddleware(['admin','manager']), async (req, res) => {
    try {
        const deliveryRounds = await RoundMaster.find({owner})
            .populate('truck', 'truckNumber status') // Populate truck details
            .populate('deliveringCylinders', 'serialNumber status') // Populate delivering cylinder details
            .populate('returnedCylinders', 'serialNumber status') // Populate returned cylinder details
            .populate('customers', 'name address contact'); // Populate customer details

        res.json(deliveryRounds);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching delivery rounds', error });
    }
});

module.exports = router;
