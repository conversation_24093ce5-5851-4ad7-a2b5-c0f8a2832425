const express = require('express');
const router = express.Router();
const {
  createCylinder,
  updateCylinder,
  getAllCylinders,
  getCylinderById,
  deleteCylinder,
  deleteCylinderBySerialNumber,
  getCylinderBySerialNumber,
  createCylinderWithoutData,
  maintainCylinder
} = require('../controllers/cylinderMasterController');
const authmiddleware = require('../middleware/authMiddleware');

router.post('/',authmiddleware(["admin","manager"]), createCylinder);
router.post('/maintain',authmiddleware(["admin","manager"]), maintainCylinder);
router.post('/create-without-data',authmiddleware(["admin","manager"]), createCylinderWithoutData);
router.get('/',authmiddleware(["admin","manager","fill",'sale']), getAllCylinders);
router.get('/:id',authmiddleware(["admin","manager","fill",'sale']), getCylinderById);
router.put('/:id',authmiddleware(["admin","manager","fill",'sale']), updateCylinder);
router.delete('/:id',authmiddleware(["admin","manager"]), deleteCylinder);

router.get('/serial/:serialNumber', authmiddleware(['admin', 'manager', 'fill','sale']), getCylinderBySerialNumber);

// Delete a cylinder by serial number
router.delete('/serial/:serialNumber', authmiddleware(['admin', 'manager']), deleteCylinderBySerialNumber);

module.exports = router;