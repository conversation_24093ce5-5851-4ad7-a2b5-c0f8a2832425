const express = require("express");
const router = express.Router();
const StaffController = require("../controllers/StaffController");
const authMiddleware = require("../middleware/authMiddleware");

// Get all staff members (only accessible by admin and manager)
router.get(
  "/",
  authMiddleware(["admin", "manager"]),
  StaffController.getAllStaff
);

// Get a single staff member by ID
router.get(
  "/:id",
  authMiddleware(["admin", "manager"]),
  StaffController.getStaffById
);

// Staff signup
router.post("/signup", StaffController.signupStaff);

// Staff login
router.post("/login", StaffController.loginStaff);

// Refresh token
router.get(
  "/refresh/token",
  authMiddleware(["admin", "manager", "staff"]),
  StaffController.refreshToken
);

// Update a staff member
router.put(
  "/:id",
  authMiddleware(["admin", "manager"]),
  StaffController.updateStaff
);

// Verify new staff registration (only accessible by admin)
router.put(
  "/verify/:id",
  authMiddleware(["admin"]),
  StaffController.verifyStaff
);

router.get(
  "/check",
  authMiddleware(["admin", "manager", "staff"]),
  StaffController.checkStaff
);

// Delete a staff member
router.delete(
  "/:id",
  authMiddleware(["admin", "manager"]),
  StaffController.deleteStaff
);

// Password change (for authenticated users)
router.post(
  "/change-password/:id",
  authMiddleware(["root", "admin", "manager", "sale", "fill"]),
  StaffController.changePassword
);

// Password reset request (for users who forgot their password)
router.post("/request-reset", StaffController.requestPasswordReset);

// Password reset with token
router.post("/reset-password", StaffController.resetPassword);

// Create a root user (only accessible by admin or root)
router.post(
  "/create-root",
  authMiddleware(["admin", "root"]),
  StaffController.createRootUser
);

// Ban a staff member (only accessible by admin, root, or manager)
router.put(
  "/ban/:id",
  authMiddleware(["admin", "root",]),
  StaffController.banStaff
);

// Unban a staff member (only accessible by admin, root, or manager)
router.put(
  "/unban/:id",
  authMiddleware(["admin", "root"]),
  StaffController.unbanStaff
);

// Get all banned staff members (only accessible by admin, root, or manager)
router.get(
  "/banned",
  authMiddleware(["admin", "root"]),
  StaffController.getBannedStaff
);

module.exports = router;
