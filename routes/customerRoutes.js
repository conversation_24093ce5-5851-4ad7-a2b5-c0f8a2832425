const express = require('express');
const router = express.Router();
const CustomerController = require('../controllers/CustomerController');
const authmiddleware = require('../middleware/authMiddleware');

// Basic CRUD routes
router.get('/',authmiddleware(['admin','manager','sale']), CustomerController.getAllCustomers);
router.get('/:id',authmiddleware(['admin','manager','sale']), CustomerController.getCustomerById);
router.post('/',authmiddleware(['admin','manager','sale']), CustomerController.createCustomer);
router.put('/:id',authmiddleware(['admin','manager','sale']), CustomerController.updateCustomer);
router.delete('/:id',authmiddleware(['admin','manager','sale']), CustomerController.deleteCustomer);

// Price management routes
router.put('/:id/prices', CustomerController.handleCustomerPrice);
router.delete('/:id/prices/:priceId', CustomerController.removeCustomerPrice);

module.exports = router;