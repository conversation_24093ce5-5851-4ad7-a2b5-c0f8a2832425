/**
 * Admin Routes
 * 
 * Routes for admin user management operations.
 * All routes require authentication and appropriate role permissions.
 */

const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/AdminController');
const authMiddleware = require('../middleware/authMiddleware');

// ==================== USER MANAGEMENT ROUTES ====================

/**
 * Create a new user
 * POST /api/admin/create-user
 * Access: root, admin
 */
router.post(
  '/create-user',
  authMiddleware(['root', 'admin']),
  AdminController.createUser
);

/**
 * Get all users with optional filtering
 * GET /api/admin/users
 * Query params: role, verified, banned, page, limit
 * Access: root, admin
 */
router.get(
  '/users',
  authMiddleware(['root', 'admin']),
  AdminController.getAllUsers
);

/**
 * Update user role
 * PUT /api/admin/users/:userId/role
 * Body: { role: string }
 * Access: root, admin
 */
router.put(
  '/users/:userId/role',
  authMiddleware(['root', 'admin']),
  AdminController.updateUserRole
);

/**
 * Verify user account
 * PUT /api/admin/users/:userId/verify
 * Access: root, admin
 */
router.put(
  '/users/:userId/verify',
  authMiddleware(['root', 'admin']),
  AdminController.verifyUser
);

/**
 * Ban/unban user
 * PUT /api/admin/users/:userId/ban
 * Body: { banned: boolean }
 * Access: root, admin
 */
router.put(
  '/users/:userId/ban',
  authMiddleware(['root', 'admin']),
  AdminController.setBanStatus
);

/**
 * Reset user password
 * PUT /api/admin/users/:userId/reset-password
 * Body: { newPassword: string }
 * Access: root, admin
 */
router.put(
  '/users/:userId/reset-password',
  authMiddleware(['root', 'admin']),
  AdminController.resetUserPassword
);

/**
 * Delete user (soft delete by banning)
 * DELETE /api/admin/users/:userId
 * Access: root only
 */
router.delete(
  '/users/:userId',
  authMiddleware(['root']),
  AdminController.deleteUser
);

/**
 * Check if user exists by email
 * GET /api/admin/check-user/:email
 * Access: root, admin
 */
router.get(
  '/check-user/:email',
  authMiddleware(['root', 'admin']),
  AdminController.checkUserExists
);

/**
 * Get user statistics
 * GET /api/admin/stats
 * Access: root, admin
 */
router.get(
  '/stats',
  authMiddleware(['root', 'admin']),
  AdminController.getUserStats
);

// ==================== QUICK ACTION ROUTES ====================

/**
 * Create root user (super admin)
 * POST /api/admin/create-root
 * Body: { name, email, password, phoneNumber?, address? }
 * Access: root only
 */
router.post(
  '/create-root',
  authMiddleware(['root']),
  async (req, res) => {
    try {
      const { name, email, password, phoneNumber, address } = req.body;

      if (!name || !email || !password) {
        return res.status(400).json({ 
          message: 'Name, email, and password are required' 
        });
      }

      const adminUtils = require('../utils/adminUtils');
      const rootUser = await adminUtils.createRootUser({
        name,
        email,
        password,
        phoneNumber,
        address
      });

      res.status(201).json({
        success: true,
        message: 'Root user created successfully',
        user: rootUser
      });

    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * Bulk verify users
 * PUT /api/admin/bulk-verify
 * Body: { userIds: string[] }
 * Access: root, admin
 */
router.put(
  '/bulk-verify',
  authMiddleware(['root', 'admin']),
  async (req, res) => {
    try {
      const { userIds } = req.body;

      if (!userIds || !Array.isArray(userIds)) {
        return res.status(400).json({ 
          message: 'userIds array is required' 
        });
      }

      const adminUtils = require('../utils/adminUtils');
      const results = [];

      for (const userId of userIds) {
        try {
          const user = await adminUtils.verifyUser(userId);
          results.push({ userId, success: true, user });
        } catch (error) {
          results.push({ userId, success: false, error: error.message });
        }
      }

      res.status(200).json({
        success: true,
        message: 'Bulk verification completed',
        results
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * Bulk ban users
 * PUT /api/admin/bulk-ban
 * Body: { userIds: string[], banned: boolean }
 * Access: root, admin
 */
router.put(
  '/bulk-ban',
  authMiddleware(['root', 'admin']),
  async (req, res) => {
    try {
      const { userIds, banned } = req.body;

      if (!userIds || !Array.isArray(userIds) || banned === undefined) {
        return res.status(400).json({ 
          message: 'userIds array and banned status are required' 
        });
      }

      const adminUtils = require('../utils/adminUtils');
      const results = [];

      for (const userId of userIds) {
        try {
          // Prevent banning self
          if (userId === req.user._id.toString()) {
            results.push({ userId, success: false, error: 'Cannot ban your own account' });
            continue;
          }

          const user = await adminUtils.setBanStatus(userId, banned);
          results.push({ userId, success: true, user });
        } catch (error) {
          results.push({ userId, success: false, error: error.message });
        }
      }

      res.status(200).json({
        success: true,
        message: `Bulk ${banned ? 'ban' : 'unban'} completed`,
        results
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

// ==================== UTILITY ROUTES ====================

/**
 * Get current user info
 * GET /api/admin/me
 * Access: authenticated users
 */
router.get(
  '/me',
  authMiddleware(['root', 'admin', 'manager', 'sale', 'fill']),
  async (req, res) => {
    try {
      const StaffMaster = require('../models/StaffMaster');
      const user = await StaffMaster.findById(req.user._id)
        .select('-password')
        .populate('workingPlace', 'factoryName');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.status(200).json({
        success: true,
        user
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

/**
 * Health check for admin routes
 * GET /api/admin/health
 * Access: public
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Admin routes are healthy',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
