const express = require('express');
const router = express.Router();
const RoundMasterController = require('../controllers/RoundMasterController');
const authMiddleware = require('../middleware/authMiddleware');

// Create a new round
router.post('/', authMiddleware(['manager', 'admin']), RoundMasterController.createRound);

// Get all rounds
router.get('/', authMiddleware(['manager', 'admin','sale']), RoundMasterController.getAllRounds);

// Get a single round by ID
router.get('/:id', authMiddleware(['manager', 'admin','sale']), RoundMasterController.getRoundById);

// Update a round by ID
router.put('/:id', authMiddleware(['manager', 'admin']),  RoundMasterController.updateRoundById);

// Delete a round by ID
router.delete('/:id', authMiddleware(['manager', 'admin',]), RoundMasterController.deleteRoundById);


router.post('/add-cylinder/:roundId', authMiddleware(['manager', 'admin','sale']), RoundMasterController.addCylinderToRound);

// Get rounds by date
router.get('/date/:date', authMiddleware(['manager', 'admin','sale','driver']), RoundMasterController.getRoundsByDate);

// Get rounds by truck
router.get('/truck/:truckId', authMiddleware(['manager', 'admin','sale','driver']), RoundMasterController.getRoundsByTruck);

//  daily report
router.get('/report/daily/:date', authMiddleware(['manager', 'admin', 'sale', 'driver']), RoundMasterController.getDailyReport);

//  weekly report
router.get('/report/weekly/:startDate/:endDate', authMiddleware(['manager', 'admin', 'sale', 'driver']), RoundMasterController.getWeeklyReport);

//  monthly report
router.get('/report/monthly/:year/:month', authMiddleware(['manager', 'admin', 'sale', 'driver']), RoundMasterController.getMonthlyReport);

//  yearly report
router.get('/report/yearly/:year', authMiddleware(['manager', 'admin', 'sale', 'driver']), RoundMasterController.getYearlyReport);

//  all-time report
router.get('/report/all-time', authMiddleware(['manager', 'admin', 'sale', 'driver']), RoundMasterController.getAllTimeReport);

router.post('/set-status/:id',authMiddleware(['manager','admin']),RoundMasterController.setRoundStatus);

router.post('/complete/:roundId',authMiddleware(['manager','admin']),RoundMasterController.completeRound);

module.exports = router;
