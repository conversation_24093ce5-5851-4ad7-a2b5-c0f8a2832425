const express = require('express');
const router = express.Router();
const SellController = require('../controllers/SellController');
const authMiddleware = require('../middleware/authMiddleware');
const { createOtherFilling } = require('../controllers/OtherFillingController');

router.get('/round/:roundId',authMiddleware(['admin','manager','sale']),SellController.getCurrentSells);

router.get('/report/:date',authMiddleware(['admin','manager','sale']),SellController.getDailyReport);
router.get('/period-report', authMiddleware(['admin', 'manager', 'sale']), (req, res, next) => {
    console.log('Reached /period-report route');
    SellController.getPeriodReport(req, res);
    // res.status(200).json({ message: 'Period report route' });
  });
router.get('/',authMiddleware(['admin','manager','sale']),SellController.getAllSells);
router.get('/:id',authMiddleware(['admin','manager','sale']),SellController.getSellById);

router.get('/today',authMiddleware(['admin','manager','sale']),SellController.getTodaySells);


router.post('/',authMiddleware(['admin','manager','sale']),SellController.createSell)
router.post('/other',authMiddleware(['admin','manager','sale']),createOtherFilling)
router.post('/return' ,authMiddleware(['admin','manager','sale']),SellController.cydReturn)
module.exports = router;
