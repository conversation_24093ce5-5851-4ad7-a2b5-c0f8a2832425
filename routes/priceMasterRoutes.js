const express = require('express');
const router = express.Router();
const { createPrice, updatePrice, getAllPrices, getPriceById, addPrice, removePrice } = require('../controllers/PriceController');
const authMiddleware = require('../middleware/authMiddleware');
// POST route to create a new price record
router.post('/',authMiddleware(['admin','manager']), createPrice);
router.post('/add/:id',authMiddleware(['admin','manager']), addPrice);
router.post('/remove/:id/:priceId',authMiddleware(['admin','manager']), removePrice);
router.post('/update/:id/:priceId',authMiddleware(['admin','manager']), updatePrice);

// PUT route to update an existing price record by ID
router.put('/:id', updatePrice);

// GET route to fetch all price records
router.get('/',authMiddleware(['admin','manager','sale','fill']), getAllPrices);

// GET route to fetch a price record by ID
router.get('/:id',authMiddleware(['admin','manager','sale','fill']), getPriceById);

module.exports = router;
