const express = require("express");
const connectDB = require("./config/db");
const cors = require("cors");
const moment = require("moment-timezone");

const path = require("path");

// Import routes
const customerRoutes = require("./routes/customerRoutes");
const cylinderMasterRoutes = require("./routes/cylinderMasterRoutes");
const staffRoutes = require("./routes/StaffRoutes");
const factoryMasterRoutes = require("./routes/factoryRoute");
const truckRoutes = require("./routes/truckRoute");
const priceRoutes = require("./routes/priceMasterRoutes");
const roundMasterRoutes = require("./routes/RoundMasterRoutes");
const fillingProcessRoutes = require("./routes/FillingProcessRoutes");
const reportRoutes = require("./routes/ReportRoutes");
const lineNumberMasterRoutes = require("./routes/lineNumberMasterRoutes");
const inspectionRoutes = require("./routes/inspectionRoute");
const sellRoutes = require("./routes/SellRoutes");
const monitoringRoutes = require("./routes/monitoringRoutes");
const report = require("./routes/report");
const logRoutes = require("./routes/logRoutes");
const maintainRoutes = require("./routes/maintainRoute");
const analyticsRoutes = require("./routes/analyticsRoutes");
const publicRoutes = require("./routes/publicRoutes");
const adminRoutes = require("./routes/adminRoutes");
const serialNumberRoutes = require("./routes/serialNumberRoutes");

// Connect to MongoDB
connectDB();
const app = express();
app.use(express.json());
app.use(cors());

// Serve static files from public directory
app.use(express.static(path.join(__dirname, "public")));

// Middleware
app.use("/api/customers", customerRoutes);
app.use("/api/cylinders", cylinderMasterRoutes);
app.use("/api/staff", staffRoutes);
app.use("/api/factories", factoryMasterRoutes);
app.use("/api/trucks", truckRoutes);
app.use("/api/prices", priceRoutes);
app.use("/api/rounds", roundMasterRoutes);
app.use("/api/filling-process", fillingProcessRoutes);
app.use("/api/reports", reportRoutes);
app.use("/api/line-numbers", lineNumberMasterRoutes);
app.use("/api/inspection", inspectionRoutes);
app.use("/api/sell", sellRoutes);
app.use("/api/monitoring", monitoringRoutes);
app.use("/api/logs", logRoutes);
app.use("/api", report);
app.use("/api/maintain", maintainRoutes);
app.use("/api/analytics", analyticsRoutes);
app.use("/public", publicRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/serial-numbers", serialNumberRoutes);

// const staffcontroller = require('./controllers/StaffController');
// staffcontroller.createAdmin();

const t = moment().tz("Asia/Yangon").format();
console.log(t);
// Start server
const CylinderMaster = require("./models/CylinderMaster");

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on  ${PORT}`);
});
