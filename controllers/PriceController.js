const PriceMaster = require('../models/PriceMaster');
const LogModel = require('../models/LogModel');


exports.getAllPrices = async (req, res) => {
    try {
        const prices = await PriceMaster.find();
        res.status(200).json(prices);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
}

exports.getPriceById = async (req, res) => {
    try {
        const price = await PriceMaster.findById(req.params.id);
        if (!price) return res.status(404).json({ message: 'Price not found' });
        res.status(200).json(price);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
}   

exports.createPrice = async (req, res) => {
    try {
        const newPrice = new PriceMaster(req.body);
        const owner = req.user.workingPlace;
        newPrice.owner = owner;
        console.log(newPrice);
        await newPrice.save();
        const log = new LogModel({
            logType: 'Price Create',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Price created with ID ${newPrice._id}`,
        })
        await log.save();
            
        res.status(201).json(newPrice);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
}

exports.updatePrice = async (req, res) => {
    try {
        const updatedPrice = await PriceMaster.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedPrice) return res.status(404).json({ message: 'Price not found' });

        const log = new LogModel({
            logType: 'Price Update',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Price updated with ID ${updatedPrice._id}`,
        })
        await log.save();
        res.status(200).json(updatedPrice);

           } catch (err) { 
        res.status(400).json({ message: err.message });
        }          }

exports.addPrice = async (req, res) => {
    try {
        const price = await PriceMaster.findById(req.params.id);
        if (!price) return res.status(404).json({ message: 'Price not found' });
        price.prices.push(req.body);
        await price.save();
        const log = new LogModel({
            logType: 'Price Add',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Price added to ID ${price._id}`,
        })
        await log.save();
        res.status(200).json(price);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
}
exports.removePrice = async (req, res) => {
    try {
        const price = await PriceMaster.findById(req.params.id);
        if (!price) return res.status(404).json({ message: 'Price not found' });
        price.prices = price.prices.filter(p => p._id != req.params.priceId);
        await price.save();
        const log = new LogModel({
            logType: 'Price Remove',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Price removed from ID ${price._id}`,
        })
        await log.save();
        res.status(200).json(price);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
}
exports.editPrice = async (req, res) => {
    try { 
        const price = await PriceMaster.findById(req.params.id);
        if (!price) return res.status(404).json({ message: 'Price not found' });
        const priceIndex = price.prices.findIndex(p => p._id == req.params.priceId);
        if (priceIndex === -1) return res.status(404).json({ message: 'Price not found' });
        price.prices[priceIndex] = req.body;
        await price.save();
        const log = new LogModel({
            logType: 'Price Edit',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Price edited with ID ${price._id}`,
        })
        await log.save();
        res.status(200).json(price);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }   }
exports.deletePrice = async (req, res) => {
    try {
        const deletedPrice = await PriceMaster.findByIdAndDelete(req.params.id);
        if (!deletedPrice) return res.status(404).json({ message: 'Price not found' });
        const log = new LogModel({
            logType: 'Price Delete',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Price deleted with ID ${deletedPrice._id}`,
        })
        await log.save();
        res.status(201).json({ message: 'Price deleted' });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
}        