const FillingProcess = require('../models/FillingProcessModel');

const getDateRange = (period, startDate) => {
    const now = new Date();
    let start, end;

    switch (period) {
        case 'daily':
            start = new Date(startDate);
            end = new Date(start);
            end.setHours(23, 59, 59, 999);
            break;
        case 'weekly':
            start = new Date(startDate);
            end = new Date(start);
            end.setDate(start.getDate() + 6);
            end.setHours(23, 59, 59, 999);
            break;
        case 'monthly':
            start = new Date(startDate);
            end = new Date(start.getFullYear(), start.getMonth() + 1, 0);
            end.setHours(23, 59, 59, 999);
            break;
        case 'yearly':
            start = new Date(startDate);
            end = new Date(start.getFullYear(), 11, 31);
            end.setHours(23, 59, 59, 999);
            break;
        case 'alltime':
        default:
            start = new Date(0);
            end = new Date();
            break;
    }

    return { startDate: start, endDate: end };
};

exports.getReport = async (req, res) => {
    const { period, startDate } = req.params;
    const { startDate: start, endDate } = getDateRange(period, startDate);

    try {
        const report = await FillingProcess.aggregate([
            {
                $match: {
                    fillingTime: { $gte: start, $lte: endDate }
                }
            },
            {
                $group: {
                    _id: "$date",
                    fillingCount: { $sum: 1 },
                    fillingProcess: { $push: "$$ROOT" }
                }
            },
            {
                $sort: { _id: 1 }
            }
        ]);

        res.status(200).json(report);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getCustomReport = async (req, res) => {
    const { startDate, endDate } = req.params;

    try {
        const report = await FillingProcess.aggregate([
            {
                $match: {
                    fillingTime: { $gte: new Date(startDate), $lte: new Date(endDate) }
                }
            },
            {
                $group: {
                    _id: "$date",
                    fillingCount: { $sum: 1 },
                    fillingProcess: { $push: "$$ROOT" }
                }
            },
            {
                $sort: { _id: 1 }
            }
        ]);

        res.status(200).json(report);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};


exports.periodReport =async(req,res)=>{
    /*
       filling
          : totalFillingProcess
          : totalTotalFilledCylinder
          : totalRejectedCylinder
          : lineReport
                :[lineNumber:[
                    : totalFillingProcess
                    : totalTotalFilledCylinder
                    : totalRejectedCylinder
                ]]

        sale
          : totalDeliveryRound
          : totalDeliveredCylinder
          : totalReturnedCylinder
          : totalSales
          : totalDirectSales
          :   

    */
}