const mongoose = require('mongoose');
const moment = require('moment-timezone');
const SellMaster = require('../models/SellMaster');
const CustomerMaster = require('../models/CustomerMaster');
const CylinderMaster = require('../models/CylinderMaster');
const StaffMaster = require('../models/StaffMaster');
const RoundMaster = require('../models/RoundMaster');
const TruckMaster = require('../models/TruckMaster');
const ExcelJS = require('exceljs');


const getAllSell = async (req, res) => {
  try {
    const sells = await SellMaster.find().populate('customer owner ').populate('cylinders.cylinder cylinders.cylinder.usedCustomerList').populate('staff').populate('round');
    res.status(200).json(sells);
  } catch (error) {     
    res.status(404).json({ message: error.message });
  }}


const getDailySalesReport = async (req, res) => {
  try {
    const { date } = req.params; // Expect date in YYYY-MM-DD format (e.g., 2025-03-08)
    const exportToExcel = req.query.export === 'true'; // Check if export is requested (e.g., ?export=true)

    // Validate date format
    if (!moment(date, 'YYYY-MM-DD', true).isValid()) {
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD.' });
    }

    // Define start and end of the day in Asia/Yangon timezone
    const startOfDay = moment.tz(date, 'Asia/Yangon').startOf('day').toDate();
    const endOfDay = moment.tz(date, 'Asia/Yangon').endOf('day').toDate();

    console.log('Start of day:', startOfDay);
    console.log('End of day:', endOfDay);

    // Test query to debug data
    const testReport = await SellMaster.find({
      date: { $gte: startOfDay, $lte: endOfDay },
      round: null, // Filter for factory sales (round is null)
    });
    console.log('Test report for Factory Sales:', testReport);

    // Aggregation pipeline to generate the report
    const report = await SellMaster.aggregate([
      {
        $match: {
          date: { $gte: startOfDay, $lte: endOfDay },
          round: null, // Filter for factory sales (round is null)
        },
      },
      // Join with CustomerMaster
      {
        $lookup: {
          from: 'customermasters',
          localField: 'customer',
          foreignField: '_id',
          as: 'customerData',
        },
      },
      {
        $lookup: {
          from: 'cylindermasters',
          localField: 'cylinders.cylinder',
          foreignField: '_id',
          as: 'cylinderData',
        },
      },
      {
        $lookup: {
          from: 'staffmasters',
          localField: 'staff',
          foreignField: '_id',
          as: 'staffData',
        },
      },
      {
        $unwind: { path: '$customerData', preserveNullAndEmptyArrays: true },
      },
      {
        $unwind: { path: '$staffData', preserveNullAndEmptyArrays: true },
      },
      {
        $project: {
          transactionNumber: '$_id',
          salesperson: { $ifNull: ['$staffData.name', 'Unknown'] },
          time: { $dateToString: { format: '%H:%M', date: '$date' } },
          customerName: { $ifNull: ['$customerData.name', 'Unknown'] },
          cylinderData: 1,
          totalSalesAmount: {
            cash: { $cond: [{ $eq: ['$payment', 'Cash'] }, '$total', 0] },
            credit: { $cond: [{ $eq: ['$payment', 'Credit'] }, '$total', 0] },
          },
          total: { $ifNull: ['$total', 0] },
        },
      },
      {
        $unwind: '$cylinderData', // Unwind cylinderData to group by size
      },
      {
        $group: {
          _id: {
            transactionNumber: '$transactionNumber',
            salesperson: '$salesperson',
            time: '$time',
            customerName: '$customerName',
            totalSalesAmount: '$totalSalesAmount',
            total: '$total',
          },
          cylinderSizes: {
            $push: '$cylinderData.cylinderSize',
          },
        },
      },
      {
        $project: {
          transactionNumber: '$_id.transactionNumber',
          salesperson: '$_id.salesperson',
          time: '$_id.time',
          customerName: '$_id.customerName',
          totalSalesAmount: '$_id.totalSalesAmount',
          total: '$_id.total',
          '47L': {
            $size: {
              $filter: {
                input: '$cylinderSizes',
                as: 'size',
                cond: { $eq: ['$$size', '47L'] },
              },
            },
          },
          '40L': {
            $size: {
              $filter: {
                input: '$cylinderSizes',
                as: 'size',
                cond: { $eq: ['$$size', '40L'] },
              },
            },
          },
          '15L': {
            $size: {
              $filter: {
                input: '$cylinderSizes',
                as: 'size',
                cond: { $eq: ['$$size', '15L'] },
              },
            },
          },
          '10L': {
            $size: {
              $filter: {
                input: '$cylinderSizes',
                as: 'size',
                cond: { $eq: ['$$size', '10L'] },
              },
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          transactions: {
            $push: {
              transactionNumber: '$transactionNumber',
              salesperson: '$salesperson',
              time: '$time',
              customerName: '$customerName',
              '47L': '$47L',
              '40L': '$40L',
              '15L': '$15L',
              '10L': '$10L',
              totalSalesAmount: '$totalSalesAmount',
              total: '$total',
            },
          },
          totalTransactions: { $sum: 1 },
          totalCustomers: { $addToSet: '$customerName' },
          total47L: { $sum: '$47L' },
          total40L: { $sum: '$40L' },
          total15L: { $sum: '$15L' },
          total10L: { $sum: '$10L' },
          totalCash: { $sum: '$totalSalesAmount.cash' },
          totalCredit: { $sum: '$totalSalesAmount.credit' },
        },
      },
      {
        $project: {
          _id: 0,
          date: date,
          transactions: 1,
          totalNumberOfTransactions: '$totalTransactions',
          totalNumberOfCustomers: { $size: '$totalCustomers' },
          total47L: '$total47L',
          total40L: '$total40L',
          total15L: '$total15L',
          total10L: '$total10L',
          totalSalesAmount: {
            cash: '$totalCash',
            credit: '$totalCredit',
          },
        },
      },
    ]);

    const reportData = report.length > 0 ? report[0] : {
      date,
      transactions: [],
      totalNumberOfTransactions: 0,
      totalNumberOfCustomers: 0,
      total47L: 0,
      total40L: 0,
      total15L: 0,
      total10L: 0,
      totalSalesAmount: { cash: 0, credit: 0 },
    };

    // If export is requested, generate and send Excel file
    if (exportToExcel) {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Daily Sales Report (Factory Sales)');

      // Title and Date
      worksheet.mergeCells('A1:D1');
      worksheet.getCell('A1').value = 'Daily Sales Report (Factory Sales)';
      worksheet.getCell('A1').font = { bold: true, size: 14 };
      worksheet.getCell('A1').alignment = { horizontal: 'left' };

      worksheet.mergeCells('I1:J1');
      worksheet.getCell('I1').value = `Date: ${moment(date).format('D/MMMM/YYYY')}`;
      worksheet.getCell('I1').font = { bold: true, size: 12 };
      worksheet.getCell('I1').alignment = { horizontal: 'right' };

      // Define columns
      worksheet.columns = [
        { key: 'sr', width: 5 },
        { key: 'transactionNumber', width: 20 },
        { key: 'salesperson', width: 20 },
        { key: 'time', width: 15 },
        { key: 'customerName', width: 20 },
        { key: '47L', width: 10 },
        { key: '40L', width: 10 },
        { key: '15L', width: 10 },
        { key: '10L', width: 10 },
        { key: 'cash', width: 15 },
        { key: 'credit', width: 15 },
      ];

      // Add header row
      const headerRow = worksheet.addRow([
        'Sr',
        'Transaction Number #',
        'Salesperson',
        'Time',
        'Customer Name',
        '47L',
        '40L',
        '15L',
        '10L',
        'Total Sales Amount', '', // Will span 2 columns
      ]);

      const subHeaderRow = worksheet.addRow([
        '', // Sr
        '', // Transaction Number
        '', // Salesperson
        '', // Time
        '', // Customer Name
        '', // 47L
        '', // 40L
        '', // 15L
        '', // 10L
        'Cash', 'Credit', // Total Sales Amount
      ]);

      // Merge cells for headers
      worksheet.mergeCells('J3:K3');

      // Style header rows
      [headerRow, subHeaderRow].forEach((row) => {
        row.eachCell((cell) => {
          cell.font = { bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'D3D3D3' }, // Light gray background
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });

      // Add transaction data
      reportData.transactions.forEach((transaction, index) => {
        const row = worksheet.addRow({
          sr: index + 1,
          transactionNumber: transaction.transactionNumber,
          salesperson: transaction.salesperson,
          time: transaction.time,
          customerName: transaction.customerName,
          '47L': transaction['47L'],
          '40L': transaction['40L'],
          '15L': transaction['15L'],
          '10L': transaction['10L'],
          cash: transaction.totalSalesAmount.cash,
          credit: transaction.totalSalesAmount.credit,
        });

        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });

      // Add totals row
      const totalsRow = worksheet.addRow([
        '',
        '',
        '',
        '',
        '',
        reportData.total47L,
        reportData.total40L,
        reportData.total15L,
        reportData.total10L,
        reportData.totalSalesAmount.cash,
        reportData.totalSalesAmount.credit,
      ]);

      totalsRow.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
      });

      // Add summary section
      worksheet.addRow([]); // Empty row for spacing
      worksheet.addRow(['TOTAL NUMBER OF TRANSACTIONS:', reportData.totalNumberOfTransactions]);
      worksheet.addRow(['TOTAL NUMBER OF CUSTOMERS:', reportData.totalNumberOfCustomers]);

      // Set response headers for file download
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=Daily_Sales_Report_Factory_Sales_${date}.xlsx`
      );

      // Write workbook to response
      await workbook.xlsx.write(res);
      res.end();
    } else {
      // Return JSON response if export is not requested
      res.status(200).json(reportData);
    }
  } catch (error) {
    console.error('Error generating factory sales report:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};


const getDailySalesReportByProduct = async (req, res) => {
  try {
    const { date } = req.params; // Expect date in YYYY-MM-DD format (e.g., 2025-03-08)
    const exportToExcel = req.query.export === 'true'; // Check if export is requested (e.g., ?export=true)

    // Validate date format
    if (!moment(date, 'YYYY-MM-DD', true).isValid()) {
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD.' });
    }

    // Define start and end of the day in Asia/Yangon timezone
    const startOfDay = moment.tz(date, 'Asia/Yangon').startOf('day').toDate();
    const endOfDay = moment.tz(date, 'Asia/Yangon').endOf('day').toDate();

    console.log('Start of day:', startOfDay);
    console.log('End of day:', endOfDay);

    // Test query to debug data
    const testReport = await SellMaster.find({
      date: { $gte: startOfDay, $lte: endOfDay },
    });
    console.log('Test report:', testReport);

    // Aggregation pipeline to generate the report
    const report = await SellMaster.aggregate([
      {
        $match: {
          date: { $gte: startOfDay, $lte: endOfDay },
        },
      },
      // Join with RoundMaster to get truck info
      {
        $lookup: {
          from: 'roundmasters',
          localField: 'round',
          foreignField: '_id',
          as: 'roundData',
        },
      },
      {
        $unwind: { path: '$roundData', preserveNullAndEmptyArrays: true },
      },
      // Join with TruckMaster to get truckNumber (for JSON output)
      {
        $lookup: {
          from: 'truckmasters',
          localField: 'roundData.truck',
          foreignField: '_id',
          as: 'truckData',
        },
      },
      {
        $unwind: { path: '$truckData', preserveNullAndEmptyArrays: true },
      },
      // Join with CustomerMaster
      {
        $lookup: {
          from: 'customermasters',
          localField: 'customer',
          foreignField: '_id',
          as: 'customerData',
        },
      },
      {
        $lookup: {
          from: 'cylindermasters',
          localField: 'cylinders.cylinder',
          foreignField: '_id',
          as: 'cylinderData',
        },
      },
      {
        $lookup: {
          from: 'staffmasters',
          localField: 'staff',
          foreignField: '_id',
          as: 'staffData',
        },
      },
      {
        $unwind: { path: '$customerData', preserveNullAndEmptyArrays: true },
      },
      {
        $unwind: { path: '$staffData', preserveNullAndEmptyArrays: true },
      },
      {
        $unwind: '$cylinderData', // Unwind cylinderData to group by size
      },
      {
        $group: {
          _id: {
            cylinderSize: '$cylinderData.cylinderSize',
            customerId: '$customerData._id',
            customerType: '$customerData.customerType',
            roundId: '$roundData._id',
          },
          totalCash: {
            $sum: { $cond: [{ $eq: ['$payment', 'Cash'] }, '$total', 0] },
          },
          totalCredit: {
            $sum: { $cond: [{ $eq: ['$payment', 'Credit'] }, '$total', 0] },
          },
          qty: { $sum: 1 }, // Count each cylinder
        },
      },
      {
        $group: {
          _id: '$_id.cylinderSize',
          factorySales: {
            $push: {
              $cond: [
                { $eq: ['$_id.customerType', 'Factory'] },
                {
                  customerId: '$_id.customerId',
                  qty: '$qty',
                  cash: '$totalCash',
                  credit: '$totalCredit',
                },
                null,
              ],
            },
          },
          deliverySales: {
            $push: {
              $cond: [
                { $ne: ['$_id.roundId', null] },
                {
                  roundId: '$_id.roundId',
                  customerId: '$_id.customerId',
                  qty: '$qty',
                  cash: '$totalCash',
                  credit: '$totalCredit',
                },
                null,
              ],
            },
          },
        },
      },
      {
        $project: {
          cylinderSize: '$_id',
          factorySales: {
            $filter: {
              input: '$factorySales',
              as: 'sale',
              cond: { $ne: ['$$sale', null] },
            },
          },
          deliverySales: {
            $filter: {
              input: '$deliverySales',
              as: 'sale',
              cond: { $ne: ['$$sale', null] },
            },
          },
        },
      },
      {
        $project: {
          cylinderSize: 1,
          factorySales: {
            noOfCustomers: { $size: { $setUnion: '$factorySales.customerId' } },
            qty: { $sum: '$factorySales.qty' },
            cash: { $sum: '$factorySales.cash' },
            credit: { $sum: '$factorySales.credit' },
          },
          deliverySales: {
            noOfRounds: { $size: { $setUnion: '$deliverySales.roundId' } },
            noOfCustomers: { $size: { $setUnion: '$deliverySales.customerId' } },
            qty: { $sum: '$deliverySales.qty' },
            cash: { $sum: '$deliverySales.cash' },
            credit: { $sum: '$deliverySales.credit' },
          },
          totalSales: {
            noOfCustomers: {
              $add: [
                { $size: { $setUnion: '$factorySales.customerId' } },
                { $size: { $setUnion: '$deliverySales.customerId' } },
              ],
            },
            qty: {
              $add: [{ $sum: '$factorySales.qty' }, { $sum: '$deliverySales.qty' }],
            },
            cash: {
              $add: [{ $sum: '$factorySales.cash' }, { $sum: '$deliverySales.cash' }],
            },
            credit: {
              $add: [
                { $sum: '$factorySales.credit' },
                { $sum: '$deliverySales.credit' },
              ],
            },
            total: {
              $add: [
                { $add: [{ $sum: '$factorySales.cash' }, { $sum: '$deliverySales.cash' }] },
                { $add: [{ $sum: '$factorySales.credit' }, { $sum: '$deliverySales.credit' }] },
              ],
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          products: {
            $push: '$$ROOT',
          },
          grandTotal: {
            $sum: {
              $add: [
                { $sum: '$factorySales.cash' },
                { $sum: '$factorySales.credit' },
                { $sum: '$deliverySales.cash' },
                { $sum: '$deliverySales.credit' },
              ],
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          date: date,
          products: 1,
          grandTotal: 1,
        },
      },
    ]);

    const reportData = report.length > 0 ? report[0] : {
      date,
      products: [
        { cylinderSize: '47L', factorySales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, deliverySales: { noOfRounds: 0, noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, totalSales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0, total: 0 } },
        { cylinderSize: '40L', factorySales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, deliverySales: { noOfRounds: 0, noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, totalSales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0, total: 0 } },
        { cylinderSize: '15L', factorySales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, deliverySales: { noOfRounds: 0, noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, totalSales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0, total: 0 } },
        { cylinderSize: '10L', factorySales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, deliverySales: { noOfRounds: 0, noOfCustomers: 0, qty: 0, cash: 0, credit: 0 }, totalSales: { noOfCustomers: 0, qty: 0, cash: 0, credit: 0, total: 0 } },
      ],
      grandTotal: 0,
    };

    // If export is requested, generate and send Excel file
    if (exportToExcel) {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Daily Sales Report by Product');

      // Title and Date
      worksheet.mergeCells('A1:D1');
      worksheet.getCell('A1').value = 'Daily Sales Report by Product';
      worksheet.getCell('A1').font = { bold: true, size: 14 };
      worksheet.getCell('A1').alignment = { horizontal: 'left' };

      worksheet.mergeCells('K1:M1');
      worksheet.getCell('K1').value = `Date: ${moment(date).format('D/MMMM/YYYY')}`;
      worksheet.getCell('K1').font = { bold: true, size: 12 };
      worksheet.getCell('K1').alignment = { horizontal: 'right' };

      // Define columns (for internal mapping, not directly used for headers)
      worksheet.columns = [
        { key: 'sr', width: 5 },
        { key: 'product', width: 10 },
        { key: 'factoryNoOfCustomers', width: 15 },
        { key: 'factoryQty', width: 10 },
        { key: 'factoryCash', width: 15 },
        { key: 'factoryCredit', width: 15 },
        { key: 'deliveryNoOfRounds', width: 15 },
        { key: 'deliveryNoOfCustomers', width: 15 },
        { key: 'deliveryQty', width: 10 },
        { key: 'deliveryCash', width: 15 },
        { key: 'deliveryCredit', width: 15 },
        { key: 'totalNoOfCustomers', width: 15 },
        { key: 'totalQty', width: 10 },
        { key: 'totalCash', width: 15 },
        { key: 'totalCredit', width: 15 },
        { key: 'total', width: 15 },
      ];

      // Add header rows
      const headerRow1 = worksheet.addRow([
        'Sr',
        'Product',
        'Factory Sales', '', '', '', // Will span 4 columns
        'Delivery Sales', '', '', '', '', // Will span 5 columns
        'Total Sales', '', '', '', // Will span 4 columns
      ]);

      const headerRow2 = worksheet.addRow([
        '', // Sr
        '', // Product
        'No. of Customer Served', 'Qty', 'Cash', 'Credit', // Factory Sales
        'No. of Round', 'No. of Customer Served', 'Qty', 'Cash', 'Credit', // Delivery Sales
        'No. of Customer Served', 'Qty', 'Cash', 'Credit', 'Total', // Total Sales
      ]);

      // Merge cells for headers
      worksheet.mergeCells('C3:F3');
      worksheet.mergeCells('G3:K3');
      worksheet.mergeCells('L3:O3');

      // Style header rows
      [headerRow1, headerRow2].forEach((row) => {
        row.eachCell((cell) => {
          cell.font = { bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'D3D3D3' }, // Light gray background
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });

      // Add product data
      const cylinderSizes = ['47L', '40L', '15L', '10L'];
      reportData.products
        .sort((a, b) => cylinderSizes.indexOf(a.cylinderSize) - cylinderSizes.indexOf(b.cylinderSize))
        .forEach((product, index) => {
          const row = worksheet.addRow({
            sr: index + 1,
            product: product.cylinderSize,
            factoryNoOfCustomers: product.factorySales.noOfCustomers,
            factoryQty: product.factorySales.qty,
            factoryCash: product.factorySales.cash,
            factoryCredit: product.factorySales.credit,
            deliveryNoOfRounds: product.deliverySales.noOfRounds,
            deliveryNoOfCustomers: product.deliverySales.noOfCustomers,
            deliveryQty: product.deliverySales.qty,
            deliveryCash: product.deliverySales.cash,
            deliveryCredit: product.deliverySales.credit,
            totalNoOfCustomers: product.totalSales.noOfCustomers,
            totalQty: product.totalSales.qty,
            totalCash: product.totalSales.cash,
            totalCredit: product.totalSales.credit,
            total: product.totalSales.total,
          });

          row.eachCell((cell) => {
            cell.border = {
              top: { style: 'thin' },
              left: { style: 'thin' },
              bottom: { style: 'thin' },
              right: { style: 'thin' },
            };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          });
        });

      // Add Grand Total row
      const grandTotalRow = worksheet.addRow([
        'Grand Total',
        '',
        reportData.products.reduce((sum, p) => sum + p.factorySales.noOfCustomers, 0),
        reportData.products.reduce((sum, p) => sum + p.factorySales.qty, 0),
        reportData.products.reduce((sum, p) => sum + p.factorySales.cash, 0),
        reportData.products.reduce((sum, p) => sum + p.factorySales.credit, 0),
        reportData.products.reduce((sum, p) => sum + p.deliverySales.noOfRounds, 0),
        reportData.products.reduce((sum, p) => sum + p.deliverySales.noOfCustomers, 0),
        reportData.products.reduce((sum, p) => sum + p.deliverySales.qty, 0),
        reportData.products.reduce((sum, p) => sum + p.deliverySales.cash, 0),
        reportData.products.reduce((sum, p) => sum + p.deliverySales.credit, 0),
        reportData.products.reduce((sum, p) => sum + p.totalSales.noOfCustomers, 0),
        reportData.products.reduce((sum, p) => sum + p.totalSales.qty, 0),
        reportData.products.reduce((sum, p) => sum + p.totalSales.cash, 0),
        reportData.products.reduce((sum, p) => sum + p.totalSales.credit, 0),
        reportData.grandTotal,
      ]);

      // Style Grand Total row
      grandTotalRow.eachCell((cell) => {
        cell.font = { bold: true };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'D3D3D3' }, // Light gray background
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
      });

      // Set response headers for file download
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=Daily_Sales_Report_by_Product_${date}.xlsx`
      );

      // Write workbook to response
      await workbook.xlsx.write(res);
      res.end();
    } else {
      // Return JSON response if export is not requested
      res.status(200).json(reportData);
    }
  } catch (error) {
    console.error('Error generating sales report by product:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

const getDailySalesReportBySalesperson = async (req, res) => {
  try {
    const { date } = req.params; // Expect date in YYYY-MM-DD format (e.g., 2025-03-08)
    const exportToExcel = req.query.export === 'true'; // Check if export is requested (e.g., ?export=true)

    // Validate date format
    if (!moment(date, 'YYYY-MM-DD', true).isValid()) {
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD.' });
    }

    // Define start and end of the day in Asia/Yangon timezone
    const startOfDay = moment.tz(date, 'Asia/Yangon').startOf('day').toDate();
    const endOfDay = moment.tz(date, 'Asia/Yangon').endOf('day').toDate();

    console.log('Start of day:', startOfDay);
    console.log('End of day:', endOfDay);

    // Test query to debug data
    const testReport = await SellMaster.find({
      date: { $gte: startOfDay, $lte: endOfDay },
    });
    console.log('Test report:', testReport);

    // Aggregation pipeline to generate the report
    const report = await SellMaster.aggregate([
      {
        $match: {
          date: { $gte: startOfDay, $lte: endOfDay },
        },
      },
      // Join with RoundMaster to get truck info
      {
        $lookup: {
          from: 'roundmasters',
          localField: 'round',
          foreignField: '_id',
          as: 'roundData',
        },
      },
      {
        $unwind: { path: '$roundData', preserveNullAndEmptyArrays: true },
      },
      // Join with TruckMaster to get truckNumber
      {
        $lookup: {
          from: 'truckmasters',
          localField: 'roundData.truck',
          foreignField: '_id',
          as: 'truckData',
        },
      },
      {
        $unwind: { path: '$truckData', preserveNullAndEmptyArrays: true },
      },
      // Join with CustomerMaster
      {
        $lookup: {
          from: 'customermasters',
          localField: 'customer',
          foreignField: '_id',
          as: 'customerData',
        },
      },
      {
        $lookup: {
          from: 'cylindermasters',
          localField: 'cylinders.cylinder',
          foreignField: '_id',
          as: 'cylinderData',
        },
      },
      {
        $lookup: {
          from: 'staffmasters',
          localField: 'staff',
          foreignField: '_id',
          as: 'staffData',
        },
      },
      {
        $unwind: { path: '$customerData', preserveNullAndEmptyArrays: true },
      },
      {
        $unwind: { path: '$staffData', preserveNullAndEmptyArrays: true },
      },
      {
        $project: {
          salesperson: { $ifNull: ['$staffData.name', 'Unknown'] },
          truckNumber: { $ifNull: ['$truckData.truckNumber', 'N/A'] },
          roundId: '$roundData._id',
          customerId: '$customerData._id',
          cylinderData: 1,
          totalSalesAmount: {
            cash: { $cond: [{ $eq: ['$payment', 'Cash'] }, '$total', 0] },
            credit: { $cond: [{ $eq: ['$payment', 'Credit'] }, '$total', 0] },
          },
          total: { $ifNull: ['$total', 0] },
        },
      },
      {
        $unwind: '$cylinderData', // Unwind cylinderData to group by size
      },
      {
        $group: {
          _id: {
            salesperson: '$salesperson',
            truckNumber: '$truckNumber',
          },
          roundIds: { $addToSet: '$roundId' },
          customerIds: { $addToSet: '$customerId' },
          cylinderSizes: {
            $push: '$cylinderData.cylinderSize',
          },
          totalCash: { $sum: '$totalSalesAmount.cash' },
          totalCredit: { $sum: '$totalSalesAmount.credit' },
        },
      },
      {
        $project: {
          salesperson: '$_id.salesperson',
          truckNumber: '$_id.truckNumber',
          numberOfRounds: { $size: { $setUnion: ['$roundIds', []] } }, // Exclude null values
          numberOfCustomers: { $size: '$customerIds' },
          totalCitySales: {
            '47L': {
              $size: {
                $filter: {
                  input: '$cylinderSizes',
                  as: 'size',
                  cond: { $eq: ['$$size', '47L'] },
                },
              },
            },
            '40L': {
              $size: {
                $filter: {
                  input: '$cylinderSizes',
                  as: 'size',
                  cond: { $eq: ['$$size', '40L'] },
                },
              },
            },
            '15L': {
              $size: {
                $filter: {
                  input: '$cylinderSizes',
                  as: 'size',
                  cond: { $eq: ['$$size', '15L'] },
                },
              },
            },
            '10L': {
              $size: {
                $filter: {
                  input: '$cylinderSizes',
                  as: 'size',
                  cond: { $eq: ['$$size', '10L'] },
                },
              },
            },
          },
          totalSalesAmount: {
            cash: '$totalCash',
            credit: '$totalCredit',
          },
        },
      },
      {
        $group: {
          _id: null,
          salespersons: {
            $push: {
              salesperson: '$salesperson',
              truckNumber: '$truckNumber',
              numberOfRounds: '$numberOfRounds',
              numberOfCustomers: '$numberOfCustomers',
              totalCitySales: '$totalCitySales',
              totalSalesAmount: '$totalSalesAmount',
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          date: date,
          salespersons: 1,
        },
      },
    ]);

    const reportData = report.length > 0 ? report[0] : {
      date,
      salespersons: [],
    };

    // If export is requested, generate and send Excel file
    if (exportToExcel) {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Daily Sales Report by Salesperson');

      // Title and Date
      worksheet.mergeCells('A1:D1');
      worksheet.getCell('A1').value = 'Daily Sales Report (by Salesperson)';
      worksheet.getCell('A1').font = { bold: true, size: 14 };
      worksheet.getCell('A1').alignment = { horizontal: 'left' };

      worksheet.mergeCells('I1:J1');
      worksheet.getCell('I1').value = `Date: ${moment(date).format('D/MMMM/YYYY')}`;
      worksheet.getCell('I1').font = { bold: true, size: 12 };
      worksheet.getCell('I1').alignment = { horizontal: 'right' };

      // Define columns
      worksheet.columns = [
        { key: 'sr', width: 5 },
        { key: 'truckNumber', width: 15 },
        { key: 'salesperson', width: 20 },
        { key: 'numberOfRounds', width: 15 },
        { key: 'numberOfCustomers', width: 20 },
        { key: '47L', width: 10 },
        { key: '40L', width: 10 },
        { key: '15L', width: 10 },
        { key: '10L', width: 10 },
        { key: 'cash', width: 15 },
        { key: 'credit', width: 15 },
      ];

      // Add header row
      const headerRow = worksheet.addRow([
        'Sr',
        'Truck Number',
        'Salesperson',
        'Number of Rounds',
        'Number of Customer Served',
        'Total City Sales', '', '', '', // Will span 4 columns
        'Total Sales Amount', '', // Will span 2 columns
      ]);

      const subHeaderRow = worksheet.addRow([
        '', // Sr
        '', // Truck Number
        '', // Salesperson
        '', // Number of Rounds
        '', // Number of Customer Served
        '47L', '40L', '15L', '10L', // Total City Sales
        'Cash', 'Credit', // Total Sales Amount
      ]);

      // Merge cells for headers
      worksheet.mergeCells('F3:I3');
      worksheet.mergeCells('J3:K3');

      // Style header rows
      [headerRow, subHeaderRow].forEach((row) => {
        row.eachCell((cell) => {
          cell.font = { bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'D3D3D3' }, // Light gray background
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });

      // Add salesperson data
      reportData.salespersons.forEach((sp, index) => {
        const row = worksheet.addRow({
          sr: index + 1,
          truckNumber: sp.truckNumber,
          salesperson: sp.salesperson,
          numberOfRounds: sp.numberOfRounds,
          numberOfCustomers: sp.numberOfCustomers,
          '47L': sp.totalCitySales['47L'],
          '40L': sp.totalCitySales['40L'],
          '15L': sp.totalCitySales['15L'],
          '10L': sp.totalCitySales['10L'],
          cash: sp.totalSalesAmount.cash,
          credit: sp.totalSalesAmount.credit,
        });

        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
      });

      // Set response headers for file download
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename=Daily_Sales_Report_by_Salesperson_${date}.xlsx`
      );

      // Write workbook to response
      await workbook.xlsx.write(res);
      res.end();
    } else {
      // Return JSON response if export is not requested
      res.status(200).json(reportData);
    }
  } catch (error) {
    console.error('Error generating sales report by salesperson:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};




module.exports = { getDailySalesReport ,getDailySalesReportByProduct,getDailySalesReportBySalesperson,getAllSell};

