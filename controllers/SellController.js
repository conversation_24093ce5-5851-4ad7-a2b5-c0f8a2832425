const SellMaster = require('../models/SellMaster');
const RoundMaster = require('../models/RoundMaster');
const CylinderMaster = require('../models/CylinderMaster');
const CustomerMaster = require('../models/CustomerMaster');
const moment = require('moment-timezone');
const LogModel = require('../models/LogModel');
// Create a new sale
exports.createSell = async (req, res) => {
  try {
    // Merge incoming request body with default fields
    // (Note: discount and payment can be passed in req.body if needed; otherwise, discount defaults to 0)
    const sellBody = { ...req.body, staff: req.user._id, total: 0, quantity: 0 , discount: req.body.discount || 0, payment: req.body.payment,sellId: req.body.sellId || '' };
    const owner = req.user.workingPlace;
        sellBody.owner = owner;

    // Validate input
    if (!sellBody.customer) throw new Error('Customer is required');
    // if (!sellBody.cylinders || !sellBody.cylinders.length) throw new Error('At least one cylinder is required');
    const customer = await CustomerMaster.findById(sellBody.customer);
    if (!customer) throw new Error('Customer not found');
    if(!sellBody.payment){
      sellBody.payment=customer.payment;
    }

    

    // Check for duplicate cylinders across sold and returned arrays
    const allCylinders = [
      ...sellBody.cylinders.map(c => c.cylinder.toString()),
      ...(sellBody.returnedCylinders || []).map(c => c.toString())
    ];
    if (new Set(allCylinders).size !== allCylinders.length) {
      throw new Error('Duplicate cylinders in sale');
    }

    // Process sold cylinders
    for (const cylinder of sellBody.cylinders) {
      if (!cylinder.price || !cylinder.cylinder) throw new Error('Invalid cylinder data');
      
      const cylinderMaster = await CylinderMaster.findById(cylinder.cylinder);
      if (!cylinderMaster) throw new Error(`Cylinder ${cylinder.cylinder} not found`);
      if (!['Delivered', 'Available'].includes(cylinderMaster.status)) throw new Error('Cylinder not available');
      
      sellBody.total += cylinder.price;
      sellBody.quantity += 1;
      
      // Update the cylinder status and customer details
      cylinderMaster.status = 'Taken';
      cylinderMaster.takenCustomer = sellBody.customer;
      cylinderMaster.usedCustomerList.push(sellBody.customer);
      await cylinderMaster.save();
    }
    sellBody.total = sellBody.total - sellBody.discount;
    

    // Process returned cylinders
    if (sellBody.returnedCylinders && sellBody.returnedCylinders.length) {
      for (const cylinderId of sellBody.returnedCylinders) {
        const cylinderMaster = await CylinderMaster.findById(cylinderId);
        if (!cylinderMaster) throw new Error(`Cylinder ${cylinderId} not found`);
        if (cylinderMaster.status !== 'Taken') throw new Error('Cylinder not sold');
        if (cylinderMaster.takenCustomer.toString() !== sellBody.customer) throw new Error('Cylinder customer mismatch');

        // We rely on the length of the returnedCylinders array in the schema,
        // so we do not need a separate "returnQty" field.
        cylinderMaster.status = 'Empty';
        cylinderMaster.takenCustomer = null;
        await cylinderMaster.save();
      }
    }

    // Handle round association if provided
    if (sellBody.round) {
      let round = sellBody.round.startsWith('R') 
        ? await RoundMaster.findOne({ serialNumber: sellBody.round })
        : await RoundMaster.findById(sellBody.round);
      
      if (!round) throw new Error('Round not found');
      sellBody.round = round._id;
    }

    // Create and save the sell record
    const sell = new SellMaster(sellBody);
    await sell.save();

    // Update round (if associated) by pushing the sell ID
    if (sellBody.round) {
      await RoundMaster.findByIdAndUpdate(sellBody.round, { $push: { sell: sell._id } });
    }

    // Update customer purchased history
    await CustomerMaster.findByIdAndUpdate(sellBody.customer, { $push: { purchasedHistory: sell._id } });


    const log = new LogModel({
      logType: 'Sell Create',
      logDate: new Date(),owner:req.user.workingPlace,
      logBy: req.user._id,
      remarks: `Sell created with ID ${sell._id}`,
    })
    await log.save();
    res.status(201).json(sell);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};


// Get daily report
exports.getDailyReport = async (req, res) => {
  try {
    const { date } = req.params; // Expecting date in URL parameters (YYYY-MM-DD)

    // Validate date format (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // Create start and end dates for the given day
    const startDate = new Date(date);
    if (isNaN(startDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date' });
    }
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(startDate);
    endDate.setHours(23, 59, 59, 999);

    // Fetch sales within the day
    const sales = await SellMaster.find({
      createdAt: { $gte: startDate, $lte: endDate },
      owner: req.user.workingPlace
    }).populate('customer staff cylinders.cylinder');

    // Fetch rounds within the day
    const rounds = await RoundMaster.find({
      createdAt: { $gte: startDate, $lte: endDate },
      owner: req.user.workingPlace
    }).populate('sell');

    // Group sales by customer for the customer sale report
    const customerSales = sales.reduce((acc, sale) => {
      if (!sale.customer || !sale.customer._id) return acc;

      const customerId = sale.customer._id.toString();
      if (!acc[customerId]) {
        acc[customerId] = {
          customer: sale.customer,
          totalSales: 0,
          totalDiscount: 0,
          netSales: 0,
          totalQuantity: 0,
          totalTransactions: 0,
          totalReturns: 0,
          sales: [],
          cashSales: 0,  // New: Total cash sales for the customer
          creditSales: 0 // New: Total credit sales for the customer
        };
      }
      acc[customerId].totalSales += sale.total;
      acc[customerId].totalDiscount += sale.discount || 0;
      acc[customerId].netSales += (sale.total - (sale.discount || 0));
      acc[customerId].totalQuantity += sale.quantity;
      acc[customerId].totalTransactions += 1;
      acc[customerId].totalReturns += sale.returnedCylinders ? sale.returnedCylinders.length : 0;
      acc[customerId].sales.push(sale);

      // Accumulate cash and credit sales
      if (sale.payment === 'Cash') {
        acc[customerId].cashSales += sale.total;
      } else if (sale.payment === 'Credit') {
        acc[customerId].creditSales += sale.total;
      }

      return acc;
    }, {});

    const customerSaleReport = Object.values(customerSales);

    // Calculate overall cash and credit sales
    const totalCashSales = sales.reduce((sum, sale) => (sale.payment === 'Cash' ? sum + sale.total : sum), 0);
    const totalCreditSales = sales.reduce((sum, sale) => (sale.payment === 'Credit' ? sum + sale.total : sum), 0);

    // Overall totals and net sales calculation
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalDiscount = sales.reduce((sum, sale) => sum + (sale.discount || 0), 0);
    const netTotalSales = totalSales - totalDiscount;

    // Build overall report
    const report = {
      date,
      totalSales,
      totalDiscount,
      netTotalSales,
      totalQuantity: sales.reduce((sum, sale) => sum + sale.quantity, 0),
      totalReturns: sales.reduce((sum, sale) => sum + (sale.returnedCylinders ? sale.returnedCylinders.length : 0), 0),
      totalTransactions: sales.length,
      totalRounds: rounds.length,
      factorySales: sales.filter(sale => sale.customer?.name === 'Other').length,
      customerSaleReport,
      transactions: sales,
      totalCashSales,  // New: Total cash sales for the day
      totalCreditSales // New: Total credit sales for the day
    };

    res.status(200).json(report);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


// Get current sells for a given round
exports.getCurrentSells = async (req, res) => {
  try {
    const round = await RoundMaster.findOne({ serialNumber: req.params.roundId });
    if (!round) throw new Error('Round not found');

    const sell = await SellMaster.find({ round: round._id })
      .populate({
        path: 'cylinders.cylinder',
        select: 'cylinderSize status serialNumber'
      })
      .populate('customer', 'name contact')
      .populate('returnedCylinders', 'status cylinderSize serialNumber')
      .populate('round', 'serialNumber date')
      .populate('staff', 'name role')
      .sort({ createdAt: -1 });

    res.status(200).json(sell);
  } catch (error) {
    res.status(404).json({ message: error.message });
  }
};
exports.getTodaySells =async(req,res)=>{
  try{

    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set the time to the start of the day
    const sells = await SellMaster.find({ createdAt: { $gte: today } })
      .populate('cylinders.cylinder customer round staff');
      res.status(200).json(sells);

  }catch(e){
    res.status(404).json({ message: error.message });
  }
}
exports.cydReturn = async (req, res) => {
  try {
   const {customerId, cylinders} = req.body;
    const customer = await CustomerMaster.findById(customerId);
    if (!customer) throw new Error('Customer not found');
    for (const cylinderId of cylinders) {
      const cylinderMaster = await CylinderMaster.findById(cylinderId);
      cylinderMaster.status = 'Empty';
      cylinderMaster.takenCustomer = null;
      await cylinderMaster.save();
    }
    res.status(200).json({ message: 'Cylinder returned successfully' });

  }
  catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Get all sells
exports.getAllSells = async (req, res) => {
  try {
    const sells = await SellMaster.find()
      .populate('cylinders.cylinder customer round staff');
    res.status(200).json(sells);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get a sell by its ID
exports.getSellById = async (req, res) => {
  try {
    const sell = await SellMaster.findById(req.params.id)
      .populate('cylinders.cylinder customer round staff');
    
    if (!sell) {
      return res.status(404).json({ message: 'Sell record not found' });
    }
    res.status(200).json(sell);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Update a sell record
exports.updateSell = async (req, res) => {
  try {
    const sell = await SellMaster.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!sell) {
      return res.status(404).json({ message: 'Sell record not found' });
    }
    res.status(200).json(sell);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete a sell record
exports.deleteSell = async (req, res) => {
  try {
    const sell = await SellMaster.findByIdAndDelete(req.params.id);
    
    if (!sell) {
      return res.status(404).json({ message: 'Sell record not found' });
    }
    res.status(200).json({ message: 'Sell record deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};


// Get period report (e.g., 2024 to 2025)
exports.getPeriodReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query; // Expecting startDate and endDate as query parameters (YYYY-MM-DD)

    // Validate date formats (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
      return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // Parse and validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ message: 'Invalid date values' });
    }
    if (start > end) {
      return res.status(400).json({ message: 'Start date must be before end date' });
    }

    // Set time boundaries for the range
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 999);

    // Fetch all sales within the period
    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace
    }).populate('customer staff cylinders.cylinder');

    // Fetch all rounds within the period
    const rounds = await RoundMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace
    }).populate('sell');

    // Total report aggregation
    const totalReport = {
      totalSales: sales.reduce((sum, sale) => sum + sale.total, 0),
      totalDiscount: sales.reduce((sum, sale) => sum + (sale.discount || 0), 0),
      netTotalSales: 0, // Calculated below
      totalQuantity: sales.reduce((sum, sale) => sum + sale.quantity, 0),
      totalReturns: sales.reduce((sum, sale) => sum + (sale.returnedCylinders ? sale.returnedCylinders.length : 0), 0),
      totalTransactions: sales.length,
      totalRounds: rounds.length,
      factorySales: sales.filter(sale => sale.customer?.name === 'Other').length,
      totalCashSales: sales.reduce((sum, sale) => (sale.payment === 'Cash' ? sum + sale.total : sum), 0),
      totalCreditSales: sales.reduce((sum, sale) => (sale.payment === 'Credit' ? sum + sale.total : sum), 0)
    };
    totalReport.netTotalSales = totalReport.totalSales - totalReport.totalDiscount;

    // Daily report list
    const dailyReports = [];
    let currentDate = new Date(start);

    while (currentDate <= end) {
      const dayStart = new Date(currentDate);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(currentDate);
      dayEnd.setHours(23, 59, 59, 999);

      // Filter sales and rounds for this specific day
      const daySales = sales.filter(sale => sale.createdAt >= dayStart && sale.createdAt <= dayEnd);
      const dayRounds = rounds.filter(round => round.createdAt >= dayStart && round.createdAt <= dayEnd);

      // Group sales by customer for this day
      const customerSales = daySales.reduce((acc, sale) => {
        if (!sale.customer || !sale.customer._id) return acc;

        const customerId = sale.customer._id.toString();
        if (!acc[customerId]) {
          acc[customerId] = {
            customer: sale.customer,
            totalSales: 0,
            totalDiscount: 0,
            netSales: 0,
            totalQuantity: 0,
            totalTransactions: 0,
            totalReturns: 0,
            sales: [],
            cashSales: 0,
            creditSales: 0
          };
        }
        acc[customerId].totalSales += sale.total;
        acc[customerId].totalDiscount += sale.discount || 0;
        acc[customerId].netSales += (sale.total - (sale.discount || 0));
        acc[customerId].totalQuantity += sale.quantity;
        acc[customerId].totalTransactions += 1;
        acc[customerId].totalReturns += sale.returnedCylinders ? sale.returnedCylinders.length : 0;
        acc[customerId].sales.push(sale);
        if (sale.payment === 'Cash') acc[customerId].cashSales += sale.total;
        else if (sale.payment === 'Credit') acc[customerId].creditSales += sale.total;
        return acc;
      }, {});

      const customerSaleReport = Object.values(customerSales);

      const dailyReport = {
        date: moment(dayStart).format('YYYY-MM-DD'),
        totalSales: daySales.reduce((sum, sale) => sum + sale.total, 0),
        totalDiscount: daySales.reduce((sum, sale) => sum + (sale.discount || 0), 0),
        netTotalSales: 0, // Calculated below
        totalQuantity: daySales.reduce((sum, sale) => sum + sale.quantity, 0),
        totalReturns: daySales.reduce((sum, sale) => sum + (sale.returnedCylinders ? sale.returnedCylinders.length : 0), 0),
        totalTransactions: daySales.length,
        totalRounds: dayRounds.length,
        factorySales: daySales.filter(sale => sale.customer?.name === 'Other').length,
        customerSaleReport,
        totalCashSales: daySales.reduce((sum, sale) => (sale.payment === 'Cash' ? sum + sale.total : sum), 0),
        totalCreditSales: daySales.reduce((sum, sale) => (sale.payment === 'Credit' ? sum + sale.total : sum), 0)
      };
      dailyReport.netTotalSales = dailyReport.totalSales - dailyReport.totalDiscount;

      dailyReports.push(dailyReport);

      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Final response structure
    const periodReport = {
      period: {
        startDate: moment(start).format('YYYY-MM-DD'),
        endDate: moment(end).format('YYYY-MM-DD')
      },
      totalReport,
      dailyReportList: dailyReports
    };

    res.status(200).json(periodReport);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};