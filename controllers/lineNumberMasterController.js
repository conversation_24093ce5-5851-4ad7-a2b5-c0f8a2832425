const LineNumberMaster = require('../models/LineNumberMaster');
const LogModel = require('../models/LogModel');
// Controller to create a new LineNumberMaster record
const createLineNumber = async (req, res) => {
  try {
    const { lineNumber, status ,type} = req.body;
    const owner = req.user.workingPlace;
    const hasLineNumber = await LineNumberMaster.findOne({ lineNumber , owner });
    if(hasLineNumber){
      return res.status(400).json({
        message: `Line number ${lineNumber} already exists`,
      });
    }

    const newLineNumber = new LineNumberMaster({
      lineNumber,
      status,
      owner,
      type

    });

    await newLineNumber.save();
    const log = new LogModel({
      logType: 'Create Line Number',
      logBy: req.user._id,
      logDate: new Date(),owner:req.user.workingPlace,
      remarks: `Line Number Created with Line Number ${newLineNumber.lineNumber}`,
    });
    await log.save();
    res.status(201).json({
      message: 'Line number created successfully!',
      lineNumber: newLineNumber,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error creating line number',
      error: err.message,
    });
  }
};

// Controller to fetch all LineNumberMaster records
const getAllLineNumbers = async (req, res) => {
  try {
    const lineNumbers = await LineNumberMaster.find({owner: req.user.workingPlace});
    res.status(200).json(lineNumbers);
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error fetching line numbers',
      error: err.message,
    });
  }
};

// Controller to fetch a line number record by ID
const getLineNumberById = async (req, res) => {
  try {
    const { id } = req.params;

    const lineNumber = await LineNumberMaster.findById(id);

    if (!lineNumber) {
      return res.status(404).json({
        message: 'Line number not found',
      });
    }

    res.status(200).json(lineNumber);
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error fetching line number',
      error: err.message,
    });
  }
};

// Controller to update a line number record by ID
const updateLineNumber = async (req, res) => {
  try {
    const { id } = req.params;
    const { lineNumber, status } = req.body;

    const updatedLineNumber = await LineNumberMaster.findByIdAndUpdate(
      id,
      { lineNumber, status },
      { new: true } // Return the updated document
    );

    if (!updatedLineNumber) {
      return res.status(404).json({
        message: 'Line number not found',
      });
    }

    res.status(200).json({
      message: 'Line number updated successfully',
      lineNumber: updatedLineNumber,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error updating line number',
      error: err.message,
    });
  }
};

// Controller to delete a line number record by ID
const deleteLineNumber = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedLineNumber = await LineNumberMaster.findByIdAndDelete(id);

    if (!deletedLineNumber) {
      return res.status(404).json({
        message: 'Line number not found',
      });
    }

    res.status(200).json({
      message: 'Line number deleted successfully',
      lineNumber: deletedLineNumber,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error deleting line number',
      error: err.message,
    });
  }
};

module.exports = {
  createLineNumber,
  getAllLineNumbers,
  getLineNumberById,
  updateLineNumber,
  deleteLineNumber,
};
