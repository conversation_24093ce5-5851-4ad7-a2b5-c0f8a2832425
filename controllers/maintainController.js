const MaintainMaster = require('../models/MaintainMaster');

// Get all maintenance records
exports.getAllMaintenances = async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        const owner = req.user.workingPlace;

        // Build query dynamically based on provided filters
        const query = { owner };
        if (startDate && endDate) {
            query.createdAt = { $gte: new Date(startDate), $lte: new Date(endDate) };
        }

        const maintenances = await MaintainMaster.find(query)
            .populate('fromCustomer', 'name') // Adjust fields as per your CustomerMaster schema
            .populate('fixedBy', 'name') // Adjust fields as per your StaffMaster schema
            .populate('cylinder', 'cylinderSize serialNumber'); // Adjust fields as per your CylinderMaster schema

        res.status(200).json(maintenances);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching maintenance records', error });
    }
};

// Get a single maintenance record by ID
exports.getMaintenanceById = async (req, res) => {
    try {
        const maintenance = await MaintainMaster.findById(req.params.id)
            .populate('fromCustomer', 'name')
            .populate('fixedBy', 'name');
        if (!maintenance) {
            return res.status(404).json({ message: 'Maintenance record not found' });
        }
        res.status(200).json(maintenance);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching maintenance record', error });
    }
};

// Create a new maintenance record
exports.createMaintenance = async (req, res) => {
    try {
        const newMaintenance = new MaintainMaster(req.body);
        const savedMaintenance = await newMaintenance.save();
        res.status(201).json(savedMaintenance);
    } catch (error) {
        res.status(400).json({ message: 'Error creating maintenance record', error });
    }
};

// Update an existing maintenance record
exports.updateMaintenance = async (req, res) => {
    try {
        const updatedMaintenance = await MaintainMaster.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true, runValidators: true }
        );
        if (!updatedMaintenance) {
            return res.status(404).json({ message: 'Maintenance record not found' });
        }
        res.status(200).json(updatedMaintenance);
    } catch (error) {
        res.status(400).json({ message: 'Error updating maintenance record', error });
    }
};

// Delete a maintenance record
exports.deleteMaintenance = async (req, res) => {
    try {
        const deletedMaintenance = await MaintainMaster.findByIdAndDelete(req.params.id);
        if (!deletedMaintenance) {
            return res.status(404).json({ message: 'Maintenance record not found' });
        }
        res.status(200).json({ message: 'Maintenance record deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting maintenance record', error });
    }
};

