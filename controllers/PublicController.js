const jwt = require("jsonwebtoken");
const StaffMaster = require("../models/StaffMaster");
const AnalyticsController = require("./AnalyticsController");
const LogController = require("./logController");
const PriceController = require("./PriceController");
const CustomerController = require("./CustomerController");

// Simple authentication middleware for public routes
const authenticatePublic = async (req, res, next) => {
  try {
    const token = req.headers["authorization"];
    if (!token) {
      return res
        .status(401)
        .json({ message: "Access denied. No token provided." });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET).staff;
    const staff = await StaffMaster.findOne({
      email: decoded.email,
      password: decoded.password,
    });

    if (!staff) {
      return res.status(404).json({ message: "Invalid user!" });
    }

    if (!staff.verified) {
      return res.status(403).json({ message: "Unverified!" });
    }

    if (staff.banned) {
      return res.status(403).json({ message: "Banned user!" });
    }

    req.user = decoded;
    next();
  } catch (error) {
    res.status(400).json({ message: "Invalid token." });
  }
};

// Public login endpoint
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "Email and password are required" });
    }

    const staff = await StaffMaster.findOne({ email });
    if (!staff || staff.password !== password) {
      return res.status(401).json({ message: "Invalid email or password" });
    }

    if (!staff.verified) {
      return res.status(403).json({ message: "Account not verified" });
    }

    if (staff.banned) {
      return res.status(403).json({ message: "Account is banned" });
    }

    const token = jwt.sign({ staff }, process.env.JWT_SECRET, {
      expiresIn: "24h", // Shorter expiry for public access
    });

    res.status(200).json({
      success: true,
      token,
      user: {
        id: staff._id,
        name: staff.name,
        email: staff.email,
        role: staff.role,
        workingPlace: staff.workingPlace,
      },
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Verify token endpoint
exports.verifyToken = async (req, res) => {
  try {
    const token = req.headers["authorization"];
    if (!token) {
      return res
        .status(401)
        .json({ valid: false, message: "No token provided" });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET).staff;
    const staff = await StaffMaster.findOne({
      email: decoded.email,
      password: decoded.password,
    });

    if (!staff || !staff.verified || staff.banned) {
      return res.status(401).json({ valid: false, message: "Invalid token" });
    }

    res.status(200).json({
      valid: true,
      user: {
        id: staff._id,
        name: staff.name,
        email: staff.email,
        role: staff.role,
      },
    });
  } catch (error) {
    res.status(401).json({ valid: false, message: "Invalid token" });
  }
};

// Daily Overview Report (with authentication)
exports.getDailyOverview = [
  authenticatePublic,
  AnalyticsController.getDailyOverview,
];

// Daily By Customer Report (with authentication)
exports.getDailyByCustomer = [
  authenticatePublic,
  AnalyticsController.getDailyOverviewByCustomer,
];

// Daily By Customer Type Report (with authentication)
exports.getDailyByCustomerType = [
  authenticatePublic,
  AnalyticsController.getDailyOverviewByCustomerType,
];

// Export Daily Overview to Excel (with authentication)
exports.exportDailyOverview = [
  authenticatePublic,
  AnalyticsController.exportDailyOverviewToExcel,
];

// Export Daily By Customer to Excel (with authentication)
exports.exportDailyByCustomer = [
  authenticatePublic,
  AnalyticsController.exportDailyOverviewByCustomerToExcel,
];

// Export Daily By Customer Type to Excel (with authentication)
exports.exportDailyByCustomerType = [
  authenticatePublic,
  AnalyticsController.exportDailyOverviewByCustomerTypeToExcel,
];

// ==================== FILLING PROCESS REPORTS ====================

// Filling Overview Report (with authentication)
exports.getFillingOverview = [
  authenticatePublic,
  AnalyticsController.getFillingOverview,
];

// Filling By Line Report (with authentication)
exports.getFillingByLine = [
  authenticatePublic,
  AnalyticsController.getFillingOverviewByLine,
];

// Filling By Staff Report (with authentication)
exports.getFillingByStaff = [
  authenticatePublic,
  AnalyticsController.getFillingOverviewByStaff,
];

// Export Filling Overview to Excel (with authentication)
exports.exportFillingOverview = [
  authenticatePublic,
  AnalyticsController.exportFillingOverviewToExcel,
];

// ==================== LOGS ====================

// Get Logs (with authentication and role check)
exports.getLogs = [
  authenticatePublic,
  (req, res, next) => {
    // Check if user has permission to view logs
    const allowedRoles = ["root", "admin", "manager", "fill"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Insufficient privileges to view logs.",
      });
    }
    next();
  },
  LogController.getAllLogs,
];

// ==================== PRICING MANAGEMENT ====================

// Get Prices (with authentication and role check)
exports.getPrices = [
  authenticatePublic,
  (req, res, next) => {
    // Check if user has permission to manage prices
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Price management requires admin privileges.",
      });
    }
    next();
  },
  PriceController.getAllPrices,
];

// Create Price (with authentication and role check)
exports.createPrice = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Price creation requires admin privileges.",
      });
    }
    next();
  },
  PriceController.createPrice,
];

// Update Price (with authentication and role check)
exports.updatePrice = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Price updates require admin privileges.",
      });
    }
    next();
  },
  PriceController.updatePrice,
];

// Delete Price (with authentication and role check)
exports.deletePrice = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message:
          "Access denied. Price deletion requires root/admin privileges.",
      });
    }
    next();
  },
  PriceController.deletePrice,
];

// Add Price Entry (with authentication and role check)
exports.addPriceEntry = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Price management requires admin privileges.",
      });
    }
    next();
  },
  PriceController.addPrice,
];

// Remove Price Entry (with authentication and role check)
exports.removePriceEntry = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Price management requires admin privileges.",
      });
    }
    next();
  },
  PriceController.removePrice,
];

// ==================== CUSTOMER PRICING ====================

// Get Customers (with authentication and role check)
exports.getCustomers = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message:
          "Access denied. Customer management requires admin privileges.",
      });
    }
    next();
  },
  CustomerController.getAllCustomers,
];

// Add Customer Price (with authentication and role check)
exports.addCustomerPrice = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Customer pricing requires admin privileges.",
      });
    }
    next();
  },
  CustomerController.handleCustomerPrice,
];

// Remove Customer Price (with authentication and role check)
exports.removeCustomerPrice = [
  authenticatePublic,
  (req, res, next) => {
    const allowedRoles = ["root", "admin", "manager"];
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        message: "Access denied. Customer pricing requires admin privileges.",
      });
    }
    next();
  },
  CustomerController.removeCustomerPrice,
];
