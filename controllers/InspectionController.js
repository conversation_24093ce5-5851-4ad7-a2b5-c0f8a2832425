const FillingProcess = require('../models/FillingProcessModel');
const CylinderMaster = require('../models/CylinderMaster');
const LogModel = require('../models/LogModel');
const StatusEnum = ['Full', 'Available', 'Empty', 'Filling', 'Leakage Error', '<PERSON>ve Error', 'Key Error', 'Damaged Error', 'Other Error'];

const isValidStatus = (status) => StatusEnum.includes(status);

const updateCylinderStatus = async (cylinder, status,pressure) => {
    if (isValidStatus(status)) {
        cylinder.status = status;
        cylinder.pressure= pressure;
        await cylinder.save();
    } else {
        throw new Error('Invalid status');
    }
};


const handleCylinderStatusUpdate = async ({ cylinders, status, condition = () => true, pressure }) => {
    for (const cydId of cylinders) {
        const cyd = await CylinderMaster.findById(cydId);
        if (condition(cyd)) {
            await updateCylinderStatus(cyd, status, pressure); // Pass the pressure
        }
    }
    const log = new LogModel({
        logType: 'Inspection',
        logBy: req.user._id,
        logDate: new Date(),owner:req.user.workingPlace,
        remarks: `Cylinder status updated to ${status} ${cylinders.length} cylinders`,
    });
    await log.save();
};
exports.inspectCylinder = async (req, res) => {
    const { serialNumber, type, status,pressure } = req.body;
    if(!pressure){
        return res.status(400).json({message:'Pressure is needed!'})
    }

    try {
        const cylinder = await CylinderMaster.findOne({ serialNumber })
            .populate('owner')
            .populate('takenCustomer')
            .populate('latestFilling');

        if (!cylinder) {
            return res.status(404).json({ message: 'Cylinder record not found' });
        }
        if (cylinder.status === 'Available') {
            return res.status(403).json({ message: 'Cylinder is already available' });
        }
        if (cylinder.status === 'Empty') {
            return res.status(403).json({ message: 'Cylinder is empty' });
        }
        

        const cylindersInFillingProcess = await FillingProcess.findById(cylinder.latestFilling);

        // if (!cylindersInFillingProcess) {
        //     return res.status(403).json({ message: 'There is no cylinder in filling process' });
        // }

       


        switch (type) {
            case 'Pass':
                await updateCylinderStatus(cylinder, 'Available', pressure);
                break;
            case 'Fail':
                await updateCylinderStatus(cylinder, status, pressure);
                break;
            case 'Fail All':
                await handleCylinderStatusUpdate({
                    cylinders: cylindersInFillingProcess.cylinders,
                    status,
                    pressure
                });
                break;
            case 'Pass All':
                await handleCylinderStatusUpdate({
                    cylinders: cylindersInFillingProcess.cylinders,
                    status: 'Available',
                    pressure
                });
                break;
            case 'Pass Except Error':
                await handleCylinderStatusUpdate({
                    cylinders: cylindersInFillingProcess.cylinders,
                    status: 'Available',
                    condition: (cyd) => !cyd.status.includes('Error'),
                    pressure
                });
                break;
            default:
                return res.status(400).json({ message: 'Invalid type' });
        }
        

        res.status(200).json(cylindersInFillingProcess);
    } catch (err) {
        res.status(500).json({
            message: 'Error processing request',
            error: err.message,
        });
    }
};