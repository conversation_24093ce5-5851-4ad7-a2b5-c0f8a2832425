const FillingProcess = require('../models/FillingProcessModel');

const getFillReport = async (req, res) => {
  const { startDate, endDate } = req.query;
  const owner = req.user.workingPlace;

  try {
    // Validate input dates
    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'Start date and end date are required' });
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    const fillingProcess = await FillingProcess.find({
      createdAt: { $gte: start, $lte: end },
      owner
    })
      .populate('startedBy', 'name')
      .populate('endedBy', 'name')
      .populate('lineNumber', 'lineNumber type')
      .populate('cylinders', 'cylinderSize serialNumber');

    if (!fillingProcess.length) {
      return res.status(404).json({ message: 'No filling processes found for the specified period' });
    }

    const groupedReport = fillingProcess.reduce((acc, item) => {
      const date = item.createdAt.toISOString().split('T')[0];
      
      if (!acc[date]) {
        acc[date] = {
          date,
          total_cylinders: 0,
          filled_cylinders: 0,
          rejected_cylinders: 0,
          total_process: 0,
          cylinder_sizes: {},  // Track cylinder sizes
          line_numbers: {},    // Track fills by line number
          processes: []        // Store process details
        };
      }

      const current = acc[date];
      
      // Basic counts
      current.total_cylinders += item.cylinderQty || item.cylinders?.length || 0;
      current.filled_cylinders += item.filledCylinders || 0;
      current.rejected_cylinders += item.rejectedCylinders || 0;
      current.total_process += 1;

      // Track cylinder sizes
      item.cylinders?.forEach(cylinder => {
        const size = cylinder.cylinderSize;
        current.cylinder_sizes[size] = (current.cylinder_sizes[size] || 0) + 1;
      });

      // Track line numbers
      const lineNum = item.lineNumber?.lineNumber;
      if (lineNum) {
        current.line_numbers[lineNum] = (current.line_numbers[lineNum] || 0) + (item.filledCylinders || 0);
      }

      // Store process details
      current.processes.push({
        id: item._id,
        lineNumber: item.lineNumber?.lineNumber,
        lineType: item.lineNumber?.type,
        cylinderQty: item.cylinderQty,
        filledCylinders: item.filledCylinders,
        rejectedCylinders: item.rejectedCylinders,
        startedBy: item.startedBy?.name,
        endedBy: item.endedBy?.name,
        status: item.status,
        createdAt: item.createdAt
      });

      return acc;
    }, {});

    res.status(200).json(Object.values(groupedReport));

  } catch (error) {
    console.error('Error fetching report:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

module.exports = { getFillReport };