const CylinderMaster = require('../models/CylinderMaster');
const UUID = require('uuid');
const FillingProcess = require('../models/FillingProcessModel');
const LineNumberMaster = require('../models/LineNumberMaster');
const SellMaster = require('../models/SellMaster');
const CustomerMaster = require('../models/CustomerMaster');
const PriceMaster = require('../models/PriceMaster');
const LogModel = require('../models/LogModel');
const createOtherFilling = async (req, res) => {    
    const { customerId, cylinders, lineNumber, discount = 0, payment } = req.body;
    const owner = req.user.workingPlace;

    try {
        console.log(req.body)
        // Validate customer existence
        const customer = await CustomerMaster.findById(customerId);
        if (!customer) {
            return res.status(404).json({ message: 'Customer not found' });
        }
        const priceMaster = await PriceMaster.findById(customer.priceList);
        if(!priceMaster){
            return res.status(404).json({ message: 'Price not found' });
        }

        // Validate line number
        const line = await LineNumberMaster.findById(lineNumber);
        if (!line) {
            return res.status(404).json({ message: 'Line not found' });
        }

        // Initialize filling process
        let fillingProcess = new FillingProcess({
            customer: customerId,
            owner,
            lineNumber,
            cylinders: [],
            cylinderQty: 0,
            filledCylinders: 0,
            startedBy: req.user._id,
            endedBy: req.user._id,
            status: "Filling Finished"
        });

        // Initialize sell master
        let sellMaster = new SellMaster({
            owner,
            cylinders: [],
            customer: customerId,
            discount,
            payment,
            returnedCylinders: [],
            staff: req.user._id,
            quantity: 0,
            total: 0
        });

        // Process cylinders
        for (const [cylinderSize, count] of Object.entries(cylinders)) {
            for (let i = 0; i < count; i++) {
                let cylinder = new CylinderMaster({
                    owner,
                    importDate: new Date(),
                    status: 'Empty',
                    productionDate: new Date(),
                    originalNumber: UUID.v4().substring(0, 5),
                    workingPressure: 1000,
                    designPressure: 1500,
                    cylinderSize,
                    valueType: 'Japan',
                    owned: false
                });

                await cylinder.save();
                fillingProcess.cylinders.push(cylinder);
                fillingProcess.cylinderQty++;
                console.log("Price Master", priceMaster);
               

                const priceEntry=  priceMaster.prices.find(p => p.size === Number(cylinderSize.replaceAll('L', '')));
                console.log("Price Entry", priceEntry);
                if (priceEntry) {
                    sellMaster.cylinders.push({ cylinder: cylinder._id, price: priceEntry.price });
                    sellMaster.total += priceEntry.price;
                }
              
            }
        }

        // Save filling process and sell master
        fillingProcess.fillingEndedAt = new Date();
        fillingProcess.filledCylinders = fillingProcess.cylinderQty;
        await fillingProcess.save();

        sellMaster.quantity = sellMaster.cylinders.length;
        sellMaster.total = sellMaster.total - discount; // Ensure total is not negative
        await sellMaster.save();
        customer.purchasedHistory.push(sellMaster._id);
        await customer.save();

        const log = new LogModel({
            logType: 'Other Filling',
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            remarks: `${fillingProcess.cylinderQty} cylinders by ${customer.name}`
        });
        await log.save();

        res.status(201).json({
            message: 'Filling process created successfully',
            sellMaster
        });

    } catch (err) {
        console.error('Error:', err);
        res.status(500).json({ message: 'Error creating filling process', error: err.message });
    }
};

module.exports = { createOtherFilling };
