const SellMaster = require('../models/SellMaster');

const getSellPreport = async (req, res) => {
    const { startDate, endDate } = req.query;
    const owner = req.user.workingPlace;

    try {
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);

        const sellReport = await SellMaster.find({
            createdAt: { 
                $gte: start, 
                $lte: end 
            },
            owner
        }).populate('customer', 'name phone')
          .populate('staff', 'name')
          .populate('cylinders.cylinder', 'cylinderSize serialNumber');

        const groupedReport = sellReport.reduce((acc, item) => {
            const date = item.createdAt.toISOString().split('T')[0];

            if (!acc[date]) {
                acc[date] = { 
                    date, 
                    total: 0, 
                    total_cylinders: 0,
                    cylinderSizes: {},
                    data: [] 
                };
            }

            acc[date].total += item.total;
            acc[date].total_cylinders += item.cylinders.length;
            
            item.cylinders.forEach(({ cylinder }) => {
                const size = cylinder.cylinderSize;
                acc[date].cylinderSizes[size] = (acc[date].cylinderSizes[size] || 0) + 1;
            });

            acc[date].data.push(item);
            return acc;
        }, {});

        res.status(200).json(Object.values(groupedReport));
    } catch (error) {
        console.error('Error fetching report:', error);
        res.status(500).json({ message: error.message });
    }
};

module.exports = { getSellPreport };
