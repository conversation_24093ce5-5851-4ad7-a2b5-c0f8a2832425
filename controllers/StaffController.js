const StaffMaster = require("../models/StaffMaster");
const jwt = require("jsonwebtoken");
const FactoryMaster = require("../models/FactoryMaster");
const LogModel = require("../models/LogModel");
// Get all staff members
exports.getAllStaff = async (req, res) => {
  try {
    const staff = await StaffMaster.find().populate("workingPlace");
    res.status(200).json(staff);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
exports.checkStaff = async (req, res) => {
  try {
    const staff = await StaffMaster.findOne({
      email: req.user.email,
      password: req.user.password,
    });
    if (!staff) return res.status(404).json({ message: "Staff not found" });
    if (staff.verified === false)
      return res.status(401).json({ message: "Staff not verified" });
    if (staff.banned === true)
      return res.status(401).json({ message: "Staff is banned" });
    const token = jwt.sign({ staff }, process.env.JWT_SECRET, {
      expiresIn: "30d",
    });
    const log = new LogModel({
      logType: "Staff Login",
      logDate: new Date(),
      owner: req.user.workingPlace,
      logBy: staff._id,
      remarks: `${staff.name} logged in`,
    });
    await log.save();
    res.status(200).json({
      token,
      role: staff.role,
      id: staff._id,
      name: staff.name,
      verified: staff.verified,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
// Get a single staff member by ID
exports.getStaffById = async (req, res) => {
  try {
    const staff = await StaffMaster.findById(req.params.id).populate(
      "workingPlace"
    );
    if (!staff) return res.status(404).json({ message: "Staff not found" });
    res.status(200).json(staff);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Staff signup
exports.signupStaff = async (req, res) => {
  const staff = new StaffMaster(req.body);
  try {
    const newStaff = await staff.save();
    const log = new LogModel({
      logType: "Staff Create",
      logDate: new Date(),
      owner: req.user.workingPlace,
      logBy: newStaff._id,
      remarks: `${newStaff.name} created`,
    });
    await log.save();
    res.status(201).json(newStaff);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Update a staff member
exports.updateStaff = async (req, res) => {
  try {
    const updatedStaff = await StaffMaster.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true }
    );
    if (!updatedStaff)
      return res.status(404).json({ message: "Staff not found" });
    res.status(200).json(updatedStaff);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// Delete a staff member
exports.deleteStaff = async (req, res) => {
  try {
    const deletedStaff = await StaffMaster.findByIdAndDelete(req.params.id);
    if (!deletedStaff)
      return res.status(404).json({ message: "Staff not found" });
    res.status(200).json({ message: "Staff deleted" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Staff login
exports.loginStaff = async (req, res) => {
  try {
    const { email, password } = req.body;
    const staff = await StaffMaster.findOne({ email });
    if (!staff || staff.password !== password) {
      return res.status(401).json({ message: "Invalid email or password" });
    }
    const token = jwt.sign({ staff }, process.env.JWT_SECRET, {
      expiresIn: "30d",
    });
    const log = new LogModel({
      logType: "Staff Login",
      logDate: new Date(),
      owner: staff.workingPlace,
      logBy: staff._id,
      remarks: `${staff.name} logged in`,
    });
    await log.save();
    res.status(200).json({
      token,
      role: staff.role,
      id: staff._id,
      name: staff.name,
      verified: staff.verified,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Refresh token
exports.refreshToken = (req, res) => {
  const token = req.headers["authorization"];
  if (!token) {
    return res.status(401).json({ message: "Token is required" });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(401).json({ message: "Invalid token" });
    }

    const newToken = jwt.sign({ decoded }, process.env.JWT_SECRET, {
      expiresIn: "1d",
    });
    res.status(200).json({ token: newToken });
  });
};

// Manually create an admin
exports.createAdmin = async () => {
  const factory = new FactoryMaster({
    address: "Yangon",
    phone: "555-5557",
    factoryInCharge: "Tech Portal",
  });
  const newFactory = await factory.save();

  const adminData = {
    name: "Tech Portal",
    email: "<EMAIL>",
    password: "root",
    workingPlace: newFactory._id,
    contact: "000-000",
    address: "Mandalay",
    emergencyContact: "999-999",
    verified: true,
    role: "admin",
  };
  const admin = new StaffMaster(adminData);
  try {
    const newAdmin = await admin.save();
    console.log("Admin created:", newAdmin);
  } catch (error) {
    console.error("Error creating admin:", error.message);
  }
};

// Verify new staff registration
exports.verifyStaff = async (req, res) => {
  console.log("Verify staff request:", req.params.id);
  try {
    const staff = await StaffMaster.findById(req.params.id);
    if (!staff) {
      console.log("Staff not found");
      return res.status(404).json({ message: "Staff not found" });
    }

    staff.verified = true;
    await staff.save();
    console.log("Staff verified:", staff);
    res.status(200).json({ message: "Staff verified" });
  } catch (error) {
    console.log("Error verifying staff:", error.message);
    res.status(500).json({ message: error.message });
  }
};

// Change password (for authenticated users)
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const staffId = req.params.id;

    // Verify the staff exists
    const staff = await StaffMaster.findById(staffId);
    if (!staff) {
      return res.status(404).json({ message: "Staff not found" });
    }

    // Check if current password matches
    if (staff.password !== currentPassword) {
      return res.status(401).json({ message: "Current password is incorrect" });
    }

    // Update password
    staff.password = newPassword;
    await staff.save();

    // Log the password change
    const log = new LogModel({
      logType: "Password Change",
      logDate: new Date(),
      owner: staff.workingPlace,
      logBy: staff._id,
      remarks: `${staff.name} changed their password`,
    });
    await log.save();

    // Generate new token with updated password
    const token = jwt.sign({ staff }, process.env.JWT_SECRET, {
      expiresIn: "30d",
    });

    res.status(200).json({
      message: "Password changed successfully",
      token,
      role: staff.role,
      id: staff._id,
      name: staff.name,
      verified: staff.verified,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Request password reset (for users who forgot their password)
exports.requestPasswordReset = async (req, res) => {
  try {
    const { email } = req.body;

    // Find the staff by email
    const staff = await StaffMaster.findOne({ email });
    if (!staff) {
      // For security reasons, don't reveal that the email doesn't exist
      return res.status(200).json({
        message: "If your email is registered, you will receive a reset token",
      });
    }

    // Generate a simple reset token (in a production app, use a more secure method)
    const resetToken =
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);

    // Store the reset token and expiration time
    staff.resetToken = resetToken;
    staff.resetTokenExpires = Date.now() + 3600000; // Token valid for 1 hour
    await staff.save();

    // In a real application, you would send an email with the reset token
    // For this example, we'll just return it in the response
    res.status(200).json({
      message: "Password reset token generated",
      resetToken: resetToken,
      note: "In a production app, this token would be sent via email and not exposed in the API response",
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Reset password using token
exports.resetPassword = async (req, res) => {
  try {
    const { resetToken, newPassword } = req.body;

    // Find staff with the provided reset token and ensure it's not expired
    const staff = await StaffMaster.findOne({
      resetToken,
      resetTokenExpires: { $gt: Date.now() },
    });

    if (!staff) {
      return res
        .status(400)
        .json({ message: "Invalid or expired reset token" });
    }

    // Update password and clear reset token fields
    staff.password = newPassword;
    staff.resetToken = undefined;
    staff.resetTokenExpires = undefined;
    await staff.save();

    // Log the password reset
    const log = new LogModel({
      logType: "Password Reset",
      logDate: new Date(),
      owner: staff.workingPlace,
      logBy: staff._id,
      remarks: `${staff.name} reset their password`,
    });
    await log.save();

    res.status(200).json({ message: "Password has been reset successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Create a root user
exports.createRootUser = async (req, res) => {
  try {
    // Only allow admin users to create root users
    if (req.user.role !== "admin" && req.user.role !== "root") {
      return res
        .status(403)
        .json({ message: "Only admin or root users can create root users" });
    }

    const { name, email, password, contact, address, emergencyContact } =
      req.body;

    // Check if required fields are provided
    if (
      !name ||
      !email ||
      !password ||
      !contact ||
      !address ||
      !emergencyContact
    ) {
      return res.status(400).json({ message: "All fields are required" });
    }

    // Check if email already exists
    const existingStaff = await StaffMaster.findOne({ email });
    if (existingStaff) {
      return res.status(400).json({ message: "Email already in use" });
    }

    // Create the root user
    const rootUser = new StaffMaster({
      name,
      email,
      password,
      workingPlace: req.user.workingPlace, // Use the same factory as the admin
      contact,
      address,
      emergencyContact,
      verified: true, // Auto-verify root users
      role: "root",
    });

    const newRootUser = await rootUser.save();

    // Log the creation
    const log = new LogModel({
      logType: "Root User Creation",
      logDate: new Date(),
      owner: req.user.workingPlace,
      logBy: req.user._id,
      remarks: `Root user ${name} created by ${req.user.name}`,
    });
    await log.save();

    res.status(201).json({
      message: "Root user created successfully",
      user: {
        id: newRootUser._id,
        name: newRootUser.name,
        email: newRootUser.email,
        role: newRootUser.role,
      },
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Ban a staff member
exports.banStaff = async (req, res) => {
  try {
    // Only allow admin, root, or manager to ban staff
    if (
      req.user.role !== "admin" &&
      req.user.role !== "root" &&
      req.user.role !== "manager"
    ) {
      return res
        .status(403)
        .json({ message: "Only admin, root, or manager users can ban staff" });
    }

    const staffId = req.params.id;
    const { reason } = req.body;

    // Find the staff member
    const staff = await StaffMaster.findById(staffId);
    if (!staff) {
      return res.status(404).json({ message: "Staff not found" });
    }

    // Check if trying to ban a root or admin user
    if (staff.role === "root") {
      return res.status(403).json({ message: "Root users cannot be banned" });
    }

    // Check if trying to ban an admin (only root can ban admins)
    if (staff.role === "admin" && req.user.role !== "root") {
      return res
        .status(403)
        .json({ message: "Only root users can ban admin users" });
    }

    // Set the banned status to true
    staff.banned = true;
    await staff.save();

    // Log the ban action
    const log = new LogModel({
      logType: "Staff Ban",
      logDate: new Date(),
      owner: req.user.workingPlace,
      logBy: req.user._id,
      remarks: `${staff.name} was banned by ${req.user.name}${
        reason ? ` for reason: ${reason}` : ""
      }`,
    });
    await log.save();

    res.status(200).json({
      message: "Staff member banned successfully",
      staff: {
        id: staff._id,
        name: staff.name,
        email: staff.email,
        role: staff.role,
        banned: staff.banned,
      },
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Unban a staff member
exports.unbanStaff = async (req, res) => {
  try {
    // Only allow admin, root, or manager to unban staff
    if (
      req.user.role !== "admin" &&
      req.user.role !== "root" &&
      req.user.role !== "manager"
    ) {
      return res
        .status(403)
        .json({
          message: "Only admin, root, or manager users can unban staff",
        });
    }

    const staffId = req.params.id;

    // Find the staff member
    const staff = await StaffMaster.findById(staffId);
    if (!staff) {
      return res.status(404).json({ message: "Staff not found" });
    }

    // Check if the staff is already unbanned
    if (!staff.banned) {
      return res.status(400).json({ message: "Staff member is not banned" });
    }

    // Set the banned status to false
    staff.banned = false;
    await staff.save();

    // Log the unban action
    const log = new LogModel({
      logType: "Staff Unban",
      logDate: new Date(),
      owner: req.user.workingPlace,
      logBy: req.user._id,
      remarks: `${staff.name} was unbanned by ${req.user.name}`,
    });
    await log.save();

    res.status(200).json({
      message: "Staff member unbanned successfully",
      staff: {
        id: staff._id,
        name: staff.name,
        email: staff.email,
        role: staff.role,
        banned: staff.banned,
      },
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get all banned staff members
exports.getBannedStaff = async (req, res) => {
  try {
    // Only allow admin, root, or manager to view banned staff
    if (
      req.user.role !== "admin" &&
      req.user.role !== "root" &&
      req.user.role !== "manager"
    ) {
      return res
        .status(403)
        .json({
          message: "Only admin, root, or manager users can view banned staff",
        });
    }

    // Find all banned staff members
    const bannedStaff = await StaffMaster.find({ banned: true }).populate("workingPlace");

    res.status(200).json({
      count: bannedStaff.length,
      staff: bannedStaff
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
