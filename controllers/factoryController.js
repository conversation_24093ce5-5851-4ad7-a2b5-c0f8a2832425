const FactoryMaster = require('../models/FactoryMaster');

// Get all factories
exports.getAllFactories = async (req, res) => {
    try {
        const factories = await FactoryMaster.find();
        res.status(200).json(factories);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Get a single factory by ID
exports.getFactoryById = async (req, res) => {
    try {
        const factory = await FactoryMaster.findById(req.params.id);
        if (!factory) return res.status(404).json({ message: 'Factory not found' });
        res.status(200).json(factory);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Create a new factory
exports.createFactory = async (req, res) => {
    const factory = new FactoryMaster({
        address: req.body.address,
        phone: req.body.phone,
        factoryInCharge: req.body.factoryInCharge,
    });
    try {
        const newFactory = await factory.save();
        res.status(201).json(newFactory);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// Update a factory by ID
exports.updateFactory = async (req, res) => {
    try {
        const factory = await FactoryMaster.findById(req.params.id);
        if (!factory) return res.status(404).json({ message: 'Factory not found' });

        factory.address = req.body.address;
        factory.phone = req.body.phone;
        factory.factoryInCharge = req.body.factoryInCharge;

        const updatedFactory = await factory.save();
        res.status(200).json(updatedFactory);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// Delete a factory by ID
exports.deleteFactory = async (req, res) => {
    try {
        const factory = await FactoryMaster.findById(req.params.id);
        if (!factory) return res.status(404).json({ message: 'Factory not found' });

        await factory.remove();
        res.status(200).json({ message: 'Factory deleted' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};
