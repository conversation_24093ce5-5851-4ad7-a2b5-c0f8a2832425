const TruckMaster = require('../models/TruckMaster');
const LogModel = require('../models/LogModel');
// Get all trucks
exports.getAllTrucks = async (req, res) => {
    try {
        const owner =req.user.workingPlace;
        const trucks = await TruckMaster.find({owner}).populate('owner').populate('lastUsed');
        res.status(200).json(trucks);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getAvailableTrucks= async (req,res)=>{
    try{
        const owner= req.user.workingPlace;
        const trucks= await TruckMaster.find({owner,status:'Available'});
        res.status(200).json(trucks);
    }catch(error){
        res.status(500).json({message:error.message})
    }
}

// Get a single truck by ID
exports.getTruckById = async (req, res) => {
    try {
        const truck = await TruckMaster.findById(req.params.id).populate('owner').populate('lastUsed');
        if (!truck) {
            return res.status(404).json({ message: 'Truck not found' });
        }
        res.status(200).json(truck);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Create a new truck
exports.createTruck = async (req, res) => {
    const hasTruck=await TruckMaster.find({truckNumber:req.body.truckNumber});
    if(hasTruck.length>0){
        return res.status(400).json({ message: 'Truck already exists' });
    }
    const truck = new TruckMaster({
        truckType: req.body.truckType,
        truckNumber: req.body.truckNumber,
        owner: req.user.workingPlace,
    });

    try {
        const newTruck = await truck.save();
        const log = new LogModel({  
            logType: 'Truck Create',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Truck created with ID ${newTruck._id}`,
        });
        await log.save();
        res.status(201).json(newTruck);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// Update a truck by ID
exports.updateTruck = async (req, res) => {
    try {
        const truck = await TruckMaster.findById(req.params.id);
        if (!truck) {
            return res.status(404).json({ message: 'Truck not found' });
        }

        truck.truckType = req.body.truckType || truck.truckType;
        truck.truckNumber = req.body.truckNumber || truck.truckNumber;
        truck.owner = req.body.owner || truck.owner;

        const updatedTruck = await truck.save();
        const log = new LogModel({
            logType: 'Truck Update',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Truck updated with ID ${updatedTruck._id}`,
        });
        await log.save();
        res.status(200).json(updatedTruck);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// Delete a truck by ID
exports.deleteTruck = async (req, res) => {
    try {
        const truck = await TruckMaster.findById(req.params.id);
        if (!truck) {
            return res.status(404).json({ message: 'Truck not found' });
        }

        await truck.remove();
        const log = new LogModel({
            logType: 'Truck Delete',
            logDate: new Date(),owner:req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Truck deleted with ID ${req.params.id}`,
        });
        await
        log
            .save();
        res.status(200).json({ message: 'Truck deleted' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};
