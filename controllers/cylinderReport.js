const CylinderMaster = require('../models/CylinderMaster');

const getCylidnerReport = async (req, res) => {
    const owner = req.user.workingPlace;
    try {
        const cylinders = await CylinderMaster.find({ owner, owned: true })
            .populate('owner')
            .populate('takenCustomer')
            .populate({
              path: 'latestFilling',
              populate: [
                { path: 'lineNumber' },
                { path: 'startedBy' },
                { path: 'endedBy' }
              ]
            });

        

        res.status(200).json(cylinders);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

module.exports = { getCylidnerReport };
