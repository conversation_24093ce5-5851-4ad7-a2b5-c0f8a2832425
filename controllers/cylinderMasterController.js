const CylinderMaster = require('../models/CylinderMaster');
const UUID = require('uuid');
const LogModel = require('../models/LogModel');
const MaintainMaster = require('../models/MaintainMaster');

// Controller to create a new CylinderMaster record
const createCylinder = async (req, res) => {
  try {
    const {
      cylinderSize, importDate, valueType, productionDate,
      originalNumber, workingPressure, designPressure, status, takenCustomer
    } = req.body;
    const owner = req.user.workingPlace;

    // Check if originalNumber already exists
    const existingCylinder = await CylinderMaster.findOne({ originalNumber });
    if (existingCylinder) {
      return res.status(400).json(
        {
        message: `Cylinder with original number ${originalNumber} already exists`,
        }
       );
    }

    
    // Create the new CylinderMaster document and link the price
    const newCylinder = new CylinderMaster({
      cylinderSize,
      importDate,
      valueType,
      productionDate,
      originalNumber,
      workingPressure,
      designPressure,
      status,
      owner,
      takenCustomer,
    });

    await newCylinder.save();

     const log = new LogModel({
     logType: "Create Cylinder",
     logBy: req.user._id,
     logDate: new Date(),owner:req.user.workingPlace,
     owner: req.user.workingPlace,
     remarks: `Cylinder Created with Serial Number ${newCylinder.serialNumber}`,
    });
    await log.save();
    res.status(201).json({
      message: 'Cylinder created successfully!',
      cylinder: newCylinder,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error creating cylinder',
      error: err.message,
    });
  }
};

// Controller to fetch all CylinderMaster records with populated price data
const getAllCylinders = async (req, res) => {
  try {
    const userWorkingPlace = req.user.workingPlace;
    const cylinders = await CylinderMaster.find({ owner: userWorkingPlace,owned:true }).populate('owner').populate('takenCustomer').  populate({
      path: 'latestFilling',
      populate: [
        { path: 'lineNumber' },
        { path: 'startedBy' },
        { path: 'endedBy' }
      ]
    }); // Populate the price data
    res.status(200).json(cylinders);
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error fetching cylinder records',
      error: err.message,
    });
  }
};

// Controller to handle updating an existing cylinder record by ID
const updateCylinder = async (req, res) => {
  try {
    const { id } = req.params;
    const userWorkingPlace = req.user.workingPlace;

    const existingCylinder = await CylinderMaster.findById(id);
    if (!existingCylinder || existingCylinder.owner.toString() != userWorkingPlace) {
      return res.status(403).json({
        message: 'Unauthorized to update this cylinder',
      });
    }

    const {
      cylinderSize, importDate, valveType, productionDate,
      originalNumber, serialNumber, workingPressure, designPressure, owner, takenCustomer
    } = req.body;

    const updatedCylinder = await CylinderMaster.findByIdAndUpdate(
      id,
      {
        cylinderSize,
        importDate,
        valveType,
        productionDate,
        originalNumber,
        serialNumber,
        workingPressure,
        designPressure,
        owner,
        takenCustomer
      },
      { new: true } // Return the updated document
    );

    if (!updatedCylinder) {
      return res.status(404).json({
        message: 'Cylinder record not found',
      });
    }

    res.status(200).json({
      message: 'Cylinder record updated successfully',
      cylinder: updatedCylinder,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error updating cylinder record',
      error: err.message,
    });
  }
};

// Controller to fetch a cylinder record by ID
const getCylinderById = async (req, res) => {
  try {
    const { id } = req.params;
    const userWorkingPlace = req.user.workingPlace;
    
    const query = id.startsWith('CYD') ? { serialNumber: id } : { _id: id };

    const cylinder = await CylinderMaster.findOne(query)
      .populate('owner')
      .populate('takenCustomer')
      .populate({
        path: 'latestFilling',
        populate: [
          { path: 'lineNumber' },
          { path: 'startedBy' },
          { path: 'endedBy' }
        ]
      });
      console.log('Cylinder:', cylinder);

    if (!cylinder || cylinder.owner._id.toString() != userWorkingPlace) {
      return res.status(403).json({
        message: 'Unauthorized to view this cylinder',
      });
    }

    res.status(200).json(cylinder);
  } catch (err) {
    console.error(err);
     return   res.status(500).json({
      message: 'Error fetching cylinder record',
    });
  }
};

// Controller to delete a cylinder record by ID
const deleteCylinder = async (req, res) => {
  try {
    const { id } = req.params;
    const userWorkingPlace = req.user.workingPlace;

    const existingCylinder = await CylinderMaster.findById(id);
    if (!existingCylinder || existingCylinder.owner.toString() !== userWorkingPlace) {
      return res.status(403).json({
        message: 'Unauthorized to delete this cylinder',
      });
    }

    const deletedCylinder = await CylinderMaster.findByIdAndDelete(id);

    if (!deletedCylinder) {
      return res.status(404).json({
        message: 'Cylinder record not found',
      });
    }

    res.status(200).json({
      message: 'Cylinder record deleted successfully',
      cylinder: deletedCylinder,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error deleting cylinder record',
      error: err.message,
    });
  }
};

// Controller to delete a cylinder record by serial number
const deleteCylinderBySerialNumber = async (req, res) => {
  try {
    const { serialNumber } = req.params;
    const userWorkingPlace = req.user.workingPlace;

    const existingCylinder = await CylinderMaster.findOne({ serialNumber });
    if (!existingCylinder || existingCylinder.owner.toString() !== userWorkingPlace) {
      return res.status(403).json({
        message: 'Unauthorized to delete this cylinder',
      });
    }

    const deletedCylinder = await CylinderMaster.findOneAndDelete({ serialNumber });

    if (!deletedCylinder) {
      return res.status(404).json({
        message: 'Cylinder record not found',
      });
    }

    const log = new LogModel({
      logType: "Delete Cylinder",
      logBy: req.user._id,
      logDate: new Date(),owner:req.user.workingPlace,
      owner: req.user.workingPlace,
      remarks: `Cylinder Deleted with Serial Number ${deletedCylinder.serialNumber}`,
    });
    await log.save();


    res.status(200).json({
      message: 'Cylinder record deleted successfully',
      cylinder: deletedCylinder,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error deleting cylinder record',
      error: err.message,
    });
  }
};

// Controller to get a cylinder record by serial number
const getCylinderBySerialNumber = async (req, res) => {
  try {
    const { serialNumber } = req.params;
    const userWorkingPlace = req.user.workingPlace;

    const cylinder = await CylinderMaster.findOne({ serialNumber }).populate('owner')
    .populate('takenCustomer')
    .populate({
      path: 'latestFilling',
      populate: [
        { path: 'lineNumber' },
        { path: 'startedBy' },
        { path: 'endedBy' }
      ]
    }); if (!cylinder || cylinder.owner._id.toString() !== userWorkingPlace) {
      return res.status(403).json({
        message: 'Unauthorized to view this cylinder',
      });
    }

    res.status(200).json(cylinder);
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error fetching cylinder record',
      error: err.message,
    });
  }
};

const createCylinderWithoutData = async (req, res) => {
  const cylinderCounts = req.body;
  const serialNumbers = [];
  console.log('Creating cylinders:', cylinderCounts);

  for (const [cylinderSize, count] of Object.entries(cylinderCounts)) {
    for (let i = 0; i < count; i++) {
      const cylinder = new CylinderMaster();
      cylinder.owner = req.user.workingPlace;
      cylinder.importDate = new Date();
      cylinder.status = 'Empty';
      cylinder.productionDate = new Date();
      cylinder.originalNumber = UUID.v4().substring(0, 5);
      cylinder.workingPressure = 1000;
      cylinder.designPressure = 1500;
      cylinder.cylinderSize = cylinderSize;
      cylinder.valueType = 'Japan';
      cylinder.owned = false;

      await cylinder.save();
      serialNumbers.push(cylinder.serialNumber);
    }
  }

  res.status(201).json({
    message: 'Cylinders created successfully!',
    count: serialNumbers.length,
    serialNumbers,
  });
};
const maintainCylinder = async (req, res) => {
  const { serialNumber, status } = req.body;
  try {
    const cylinder = await CylinderMaster.findOne({ serialNumber });
    if (!cylinder) {
      return res.status(404).json({ message: 'Cylinder record not found' });
    }
    const maintain = await MaintainMaster.findOne({cylinder:cylinder._id});
    if(maintain){
      maintain.fixedDate = new Date();
      maintain.fixedBy = req.user._id;
      await maintain.save();
    }
    cylinder.status = status;
    cylinder.remarks = `Fixed By ${req.user.name}`;


    await cylinder.save();
    const log = new LogModel({
      logType: "Maintain Cylinder",
      logBy: req.user._id,
      logDate: new Date(),owner:req.user.workingPlace,
      remarks: `Cylinder Maintained with Serial Number ${cylinder.serialNumber}`,
    });
    await log.save();
    res.status(200).json({ message: 'Cylinder status updated successfully' });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: 'Error updating cylinder status',
      error: err.message,
    });
  }
};

module.exports = {
  createCylinder,
  updateCylinder,
  getAllCylinders,
  getCylinderById,
  deleteCylinder,
  deleteCylinderBySerialNumber,
  getCylinderBySerialNumber,
  createCylinderWithoutData,
  maintainCylinder,
};
