const CustomerMaster = require('../models/CustomerMaster');
const PriceMaster = require('../models/PriceMaster');
const LogModel = require('../models/LogModel');
// Get all customers
exports.getAllCustomers = async (req, res) => {
    try {
        const customers = await CustomerMaster.find({owner:req.user.workingPlace}).populate('purchasedHistory priceList owner');
        res.status(200).json(customers);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// Get a single customer by ID
exports.getCustomerById = async (req, res) => {
    try {
        const customer = await CustomerMaster.findById(req.params.id);
        if (!customer) return res.status(404).json({ message: 'Customer not found' });
        res.status(200).json(customer);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// Create a new customer
exports.createCustomer = async (req, res) => {
    delete req.body._id;

    const customer = new CustomerMaster(req.body);
    const priceList = await PriceMaster.findById(req.body.priceList._id);
    if (!priceList) {
        return res.status(404).json({ message: 'Price list not found' });
    }
    // customer.prices = priceList.prices;
    customer.priceList = priceList._id;
    customer.owner = req.user.workingPlace;
    
    try {
        const newCustomer = await customer.save();
        const log = new LogModel({
            logType: 'Customer Create',
            logDate: new Date(),owner:req.user.workingPlace,
            owner: req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Customer created with ID ${newCustomer._id}`,
        });
        await log.save();
        res.status(201).json(newCustomer);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
};

// Update a customer by ID
exports.updateCustomer = async (req, res) => {
    try {
        req.body.owner = req.user.workingPlace;
      
        const updatedCustomer = await CustomerMaster.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedCustomer) return res.status(404).json({ message: 'Customer not found' });
        const log = new LogModel({
            logType: 'Customer Update',
            logDate: new Date(),owner:req.user.workingPlace,
            owner: req.user.workingPlace,   
            logBy: req.user._id,
            remarks: `Customer updated with ID ${updatedCustomer._id}`,
        });
        await log.save();
        res.status(200).json(updatedCustomer);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
};

// Delete a customer by ID
exports.deleteCustomer = async (req, res) => {
    try {
        const deletedCustomer = await CustomerMaster.findByIdAndDelete(req.params.id);
        if (!deletedCustomer) return res.status(404).json({ message: 'Customer not found' });
        res.status(200).json({ message: 'Customer deleted' });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// Add/Update price for a customer
exports.handleCustomerPrice = async (req, res) => {
    try {
        const { id } = req.params;
        const { pressure, size, price } = req.body;

        // Input validation
        if (!pressure || !size || !price) {
            return res.status(400).json({ message: 'Missing required fields: pressure, size, or price' });
        }

        const customer = await CustomerMaster.findById(id);
        if (!customer) {
            return res.status(404).json({ message: 'Customer not found' });
        }

        // Check for existing pressure/size combination
        const existingPrice = customer.prices.find(p => 
            p.pressure === pressure && p.size === size
        );

        if (existingPrice) {
            // Update existing price
            existingPrice.price = price;
            await customer.save();
            return res.status(200).json({ 
                message: 'Price updated successfully', 
                data: customer 
            });
        }

        // Add new price entry
        customer.prices.push({ pressure, size, price });
        await customer.save();
        const log = new LogModel({
            logType: 'Price Add',
            logDate: new Date(),owner:req.user.workingPlace,
            owner: req.user.workingPlace,
            logBy: req.user._id,
            remarks: `Price added to ID ${customer._id}`,
        });
        await log.save();

        res.status(201).json({ 
            message: 'Price added successfully', 
            data: customer 
        });

    } catch (err) {
        res.status(500).json({ 
            message: 'Error processing price', 
            error: err.message 
        });
    }
};

// Remove price from customer
exports.removeCustomerPrice = async (req, res) => {
    try {
        const { id, priceId } = req.params;

        const customer = await CustomerMaster.findById(id);
        if (!customer) {
            return res.status(404).json({ message: 'Customer not found' });
        }

        // Find and remove the price entry
        const priceIndex = customer.prices.findIndex(p => p._id.equals(priceId));
        if (priceIndex === -1) {
            return res.status(404).json({ message: 'Price entry not found' });
        }

        customer.prices.splice(priceIndex, 1);
        await customer.save();

        res.status(200).json({ 
            message: 'Price removed successfully', 
            data: customer 
        });

    } catch (err) {
        res.status(500).json({ 
            message: 'Error removing price', 
            error: err.message 
        });
    }
};