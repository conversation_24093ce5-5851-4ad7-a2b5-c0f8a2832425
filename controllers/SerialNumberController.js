/**
 * Serial Number Controller
 * 
 * Handles serial number management, statistics, and migration operations.
 */

const serialNumberUtils = require('../utils/serialNumberUtils');
const CylinderMaster = require('../models/CylinderMaster');

/**
 * Get serial number statistics
 * GET /api/serial-numbers/stats
 */
exports.getStats = async (req, res) => {
    try {
        const stats = await serialNumberUtils.getSerialNumberStats();
        const ranges = await serialNumberUtils.getAvailableRanges();
        
        res.status(200).json({
            success: true,
            stats,
            ranges,
            message: 'Serial number statistics retrieved successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

/**
 * Generate next serial number (preview)
 * GET /api/serial-numbers/next
 */
exports.getNextSerialNumber = async (req, res) => {
    try {
        const nextSerial = await serialNumberUtils.generateNextSerialNumber();
        
        res.status(200).json({
            success: true,
            nextSerialNumber: nextSerial,
            message: 'Next serial number generated successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

/**
 * Validate serial number format
 * POST /api/serial-numbers/validate
 */
exports.validateSerialNumber = async (req, res) => {
    try {
        const { serialNumber } = req.body;
        
        if (!serialNumber) {
            return res.status(400).json({
                success: false,
                message: 'Serial number is required'
            });
        }
        
        const validation = serialNumberUtils.validateSerialNumber(serialNumber);
        const exists = await serialNumberUtils.serialNumberExists(serialNumber);
        
        res.status(200).json({
            success: true,
            validation: {
                ...validation,
                exists,
                available: validation.valid && !exists
            },
            message: 'Serial number validation completed'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

/**
 * Migrate old format serial numbers to new format
 * POST /api/serial-numbers/migrate
 */
exports.migrateSerialNumbers = async (req, res) => {
    try {
        // Only root and admin users can perform migration
        if (!['root', 'admin'].includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Only root and admin users can perform migration.'
            });
        }
        
        const { limit = 100, dryRun = false } = req.body;
        
        if (dryRun) {
            // Dry run - just show what would be migrated
            const oldFormatCylinders = await CylinderMaster
                .find({ serialNumber: { $regex: /^CYD\d+$/ } })
                .limit(limit)
                .sort({ createdAt: 1 })
                .select('serialNumber originalNumber createdAt');
            
            return res.status(200).json({
                success: true,
                dryRun: true,
                wouldMigrate: oldFormatCylinders.length,
                cylinders: oldFormatCylinders,
                message: `Dry run: ${oldFormatCylinders.length} cylinders would be migrated`
            });
        }
        
        const result = await serialNumberUtils.migrateOldSerialNumbers(limit);
        
        res.status(200).json({
            success: result.success,
            ...result
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

/**
 * Get cylinders by serial number format
 * GET /api/serial-numbers/cylinders?format=new|old
 */
exports.getCylindersByFormat = async (req, res) => {
    try {
        const { format = 'all', page = 1, limit = 50 } = req.query;
        const userWorkingPlace = req.user.workingPlace;
        
        let query = { owner: userWorkingPlace };
        
        if (format === 'new') {
            query.serialNumber = { $regex: /^[A-Z]\d{4}$/ };
        } else if (format === 'old') {
            query.serialNumber = { $regex: /^CYD\d+$/ };
        }
        
        const skip = (page - 1) * limit;
        
        const [cylinders, total] = await Promise.all([
            CylinderMaster.find(query)
                .populate('owner', 'factoryName')
                .populate('takenCustomer', 'customerName')
                .sort({ serialNumber: 1 })
                .skip(skip)
                .limit(parseInt(limit)),
            CylinderMaster.countDocuments(query)
        ]);
        
        res.status(200).json({
            success: true,
            cylinders,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(total / limit),
                totalCylinders: total,
                hasNext: skip + cylinders.length < total,
                hasPrev: page > 1
            },
            filter: { format },
            message: `Retrieved ${cylinders.length} cylinders`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

/**
 * Search cylinders by serial number pattern
 * GET /api/serial-numbers/search?pattern=A00&limit=10
 */
exports.searchByPattern = async (req, res) => {
    try {
        const { pattern, limit = 20 } = req.query;
        const userWorkingPlace = req.user.workingPlace;
        
        if (!pattern) {
            return res.status(400).json({
                success: false,
                message: 'Search pattern is required'
            });
        }
        
        // Create regex pattern for search
        const searchRegex = new RegExp(pattern, 'i');
        
        const cylinders = await CylinderMaster.find({
            owner: userWorkingPlace,
            serialNumber: { $regex: searchRegex }
        })
        .populate('owner', 'factoryName')
        .populate('takenCustomer', 'customerName')
        .sort({ serialNumber: 1 })
        .limit(parseInt(limit))
        .select('serialNumber originalNumber status cylinderSize createdAt');
        
        res.status(200).json({
            success: true,
            cylinders,
            searchPattern: pattern,
            found: cylinders.length,
            message: `Found ${cylinders.length} cylinders matching pattern "${pattern}"`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

/**
 * Generate bulk serial numbers (for testing/preview)
 * POST /api/serial-numbers/generate-bulk
 */
exports.generateBulkSerialNumbers = async (req, res) => {
    try {
        // Only admin users can generate bulk serial numbers
        if (!['root', 'admin'].includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Access denied. Only root and admin users can generate bulk serial numbers.'
            });
        }
        
        const { count = 10 } = req.body;
        
        if (count > 100) {
            return res.status(400).json({
                success: false,
                message: 'Cannot generate more than 100 serial numbers at once'
            });
        }
        
        const serialNumbers = [];
        let currentNext = await serialNumberUtils.generateNextSerialNumber();
        
        // Generate sequence without actually creating cylinders
        for (let i = 0; i < count; i++) {
            serialNumbers.push(currentNext);
            
            // Calculate next in sequence
            const currentLetter = currentNext.charAt(0);
            const currentNumber = parseInt(currentNext.substring(1));
            
            if (currentNumber < 9999) {
                const nextNumber = (currentNumber + 1).toString().padStart(4, '0');
                currentNext = `${currentLetter}${nextNumber}`;
            } else {
                const nextLetterCode = currentLetter.charCodeAt(0) + 1;
                if (nextLetterCode > 90) {
                    break; // Stop if we exceed Z
                }
                const nextLetter = String.fromCharCode(nextLetterCode);
                currentNext = `${nextLetter}0001`;
            }
        }
        
        res.status(200).json({
            success: true,
            serialNumbers,
            count: serialNumbers.length,
            startingFrom: serialNumbers[0],
            endingAt: serialNumbers[serialNumbers.length - 1],
            message: `Generated ${serialNumbers.length} serial numbers for preview`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

/**
 * Get serial number format information
 * GET /api/serial-numbers/format-info
 */
exports.getFormatInfo = async (req, res) => {
    try {
        const formatInfo = {
            newFormat: {
                pattern: 'Letter + 4 digits',
                example: 'A0001, B0002, Z9999',
                regex: '^[A-Z]\\d{4}$',
                capacity: {
                    perLetter: 9999,
                    totalLetters: 26,
                    totalCapacity: 259974
                },
                description: 'New format starting from A0001, incrementing to A9999, then B0001, etc.'
            },
            oldFormat: {
                pattern: 'CYD + date + counter',
                example: 'CYD202512271, CYD202512272',
                regex: '^CYD\\d+$',
                description: 'Legacy format with CYD prefix, date, and counter'
            },
            migration: {
                status: 'Available',
                description: 'Old format serial numbers can be migrated to new format',
                recommendation: 'Migrate existing cylinders to new format for consistency'
            }
        };
        
        res.status(200).json({
            success: true,
            formatInfo,
            message: 'Serial number format information retrieved successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};
