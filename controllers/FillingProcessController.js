const FillingProcess = require('../models/FillingProcessModel');
const CylinderMaster = require('../models/CylinderMaster');
const LineNumberMaster = require('../models/LineNumberMaster');
const StaffMaster = require('../models/StaffMaster');
const LogModel = require('../models/LogModel');
const MaintainMaster = require('../models/MaintainMaster');


const StatusEnum = ['Full', 'Available', 'Empty', 'Filling', 'Leakage Error', 'Valve Error', 'Key Error', 'Damaged Error', 'Other Error'];

// Create a new filling process
exports.createFillingProcess = async (req, res) => {
    try {
        const fillingProcess = new FillingProcess({
            ...req.body,
            owner: req.user.workingPlace // Ensure owner is set to user working place
        });
        fillingProcess.staff = req.user._id;

        await fillingProcess.save();

        // Update the status of the cylinder in CylinderMaster
        await CylinderMaster.findByIdAndUpdate(fillingProcess.cylinder, { status: fillingProcess.status, latestFilling: fillingProcess._id });
        const log = new LogModel({
            logType: "Create Filling Process",
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            owner: req.user.workingPlace,
            remarks: `Filling Process Created with Serial Number ${fillingProcess._id}`,
        });
        await log.save();
        res.status(201).json(fillingProcess);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// Get all filling processes
exports.getAllFillingProcesses = async (req, res) => {
    try {
        const userWorkingPlace = req.user.workingPlace;
        console.log('User Working Place:', userWorkingPlace);
        const fillingProcesses = await FillingProcess.find({ owner: userWorkingPlace }).populate('cylinders').populate('cylinders.takenCustomer');
        console.log('Filling Processes:', fillingProcesses);
        res.status(200).json(fillingProcesses);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Get a single filling process by ID
exports.getFillingProcessById = async (req, res) => {
    try {
        const { id } = req.params;
        const userWorkingPlace = req.user.workingPlace;
        console.log('User Working Place:', userWorkingPlace);

        const fillingProcess = await FillingProcess.findById(id).populate('cylinders').populate('cylinders.takenCustomer').populate('lineNumber');
        console.log('Filling Process:', fillingProcess);
        if (!fillingProcess || fillingProcess.owner.toString() !== userWorkingPlace) {
            return res.status(403).json({ message: 'Unauthorized to view this filling process' });
        }
        res.status(200).json(fillingProcess);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Get filling processes by line number and date
exports.getFillingProcessesByLineNumberAndDate = async (req, res) => {
    try {
        const { date, lineNumber } = req.params;
        const userWorkingPlace = req.user.workingPlace;
        console.log('User Working Place:', userWorkingPlace);

        const fillingProcesses = await FillingProcess.find({ date, lineNumber, owner: userWorkingPlace }).populate('cylinders').populate('cylinders.takenCustomer');
        console.log('Filling Processes:', fillingProcesses);
        res.status(200).json(fillingProcesses);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Get filling processes by date
exports.getFillingProcessesByDate = async (req, res) => {
    try {
        const { date } = req.params;
        const userWorkingPlace = req.user.workingPlace;
        console.log('User Working Place:', userWorkingPlace);

        const fillingProcesses = await FillingProcess.find({ date, owner: userWorkingPlace }).populate('cylinders').populate('cylinders.takenCustomer');
        console.log('Filling Processes:', fillingProcesses);
        res.status(200).json(fillingProcesses);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Update a filling process by ID
exports.updateFillingProcess = async (req, res) => {
    try {
        const { id } = req.params;
        const userWorkingPlace = req.user.workingPlace;

        const existingFillingProcess = await FillingProcess.findById(id);
        if (!existingFillingProcess || existingFillingProcess.owner.toString() !== userWorkingPlace) {
            return res.status(403).json({ message: 'Unauthorized to update this filling process' });
        }

        const fillingProcess = await FillingProcess.findByIdAndUpdate(id, req.body, { new: true, runValidators: true });
        if (!fillingProcess) {
            return res.status(404).json({ message: 'Filling process not found' });
        }

        // Update the status of the cylinder in CylinderMaster
        await CylinderMaster.findByIdAndUpdate(fillingProcess.cylinder, { status: fillingProcess.status });

        res.status(200).json(fillingProcess);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// Delete a filling process by ID
exports.deleteFillingProcess = async (req, res) => {
    try {
        const { id } = req.params;
        const userWorkingPlace = req.user.workingPlace;

        const existingFillingProcess = await FillingProcess.findById(id);
        if (!existingFillingProcess || existingFillingProcess.owner.toString() !== userWorkingPlace) {
            return res.status(403).json({ message: 'Unauthorized to delete this filling process' });
        }

        const fillingProcess = await FillingProcess.findByIdAndDelete(id);
        if (!fillingProcess) {
            return res.status(404).json({ message: 'Filling process not found' });
        }
        res.status(200).json({ message: 'Filling process deleted' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.startProcess = async (req, res) => {
    try {
        const { lineNumber } = req.body;
        const startedBy = req.user._id; // Assuming user ID is available in req.user
        const owner = req.user.workingPlace; // Ensure owner is set to user working place

        const line = await LineNumberMaster.findById(lineNumber);
        if (!line || line.status !== 'Available') {
            return res.status(400).json({ message: 'Line number is not available' });
        }

        console.log('Creating filling process with owner:', owner);

        const fillingProcess = new FillingProcess({
            lineNumber,
            startedBy,
            owner,
            cylinders: []
        });

        await fillingProcess.startProcess();
        line.status = 'NotAvailable';
        await line.save();
        const log = new LogModel({
            logType: "Start Filling Process",
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            remarks: `Filling Process Started with Serial Number ${fillingProcess._id}`,
        });
        await log.save();

        res.status(201).json(fillingProcess);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.addCylinder = async (req, res) => {
    try {
        const { cylinderId } = req.body;
        const { processId } = req.params;

        const fillingProcess = await FillingProcess.findById(processId);
        if (!fillingProcess) {
            return res.status(404).json({ message: 'Filling process not found' });
        }
        const cylinder = await CylinderMaster.findOne({serialNumber:cylinderId});
        if (!cylinder) {
            throw new Error('Cylinder not found');
            // return res.status(404).json({ message: 'Cylinder not found' });
        }
        if(cylinder.status!=='Empty'){
            // return res.status(400).json({ message: 'Cylinder is not Empty' });
            throw new Error('Cylinder is not Empty');
        }
        await fillingProcess.addCylinder(cylinderId);
        const log = new LogModel({
            logType: "Add Cylinder",
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            remarks: `Cylinder Added to Filling Process with Serial Number ${fillingProcess._id}`,
        });
        await log.save();
        res.status(200).json(fillingProcess);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.updateCylinderStatus = async (req, res) => {
    try {
        const { cylinderId, status } = req.body;
        const { processId } = req.params;

        if (!StatusEnum.includes(status)) {
            return res.status(400).json({ message: 'Invalid status' });
        }

        const fillingProcess = await FillingProcess.findById(processId);
        if (!fillingProcess) {
            return res.status(404).json({ message: 'Filling process not found' });
        }

        await fillingProcess.updateCylinderStatus(cylinderId, status);

        res.status(200).json(fillingProcess);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.removeCylinder = async (req, res) => {
    try {
        const { cylinderId, status } = req.body;
        const { processId } = req.params;

        const fillingProcess = await FillingProcess.findById(processId);
        if (!fillingProcess) {
            return res.status(404).json({ message: 'Filling process not found' });
        }

        await fillingProcess.removeCylinder(cylinderId, status);
        res.status(200).json(fillingProcess);
    } catch (error) {
        res.status (500).json({ message: error.message });
    }
};

exports.endProcess = async (req, res) => {
    try {
        const { remark, rejectedCylinders } = req.body;
        const { processId } = req.params;
        const endedBy = req.user._id; // Assuming user ID is available in req.user

        const fillingProcess = await FillingProcess.findById(processId);
        if (!fillingProcess) {
            return res.status(404).json({ message: 'Filling process not found' });
        }

        await fillingProcess.endProcess(endedBy, remark, rejectedCylinders);
        for (const cylinder of rejectedCylinders){
            console.log('Cylinder:',cylinder);
            const cylinderMaster = await CylinderMaster.findById(cylinder.id).populate('usedCustomerList');
            const m = new MaintainMaster({
                errorType: cylinderMaster.status,
                fromCustomer: cylinderMaster.usedCustomerList.at(-1),
                startedDate: new Date(),
                remarks:remark,
                owner:req.user.workingPlace,
                cylinder:cylinderMaster._id,
            });
            await m.save();
            cylinderMaster.status='Maintain';


        }

        const line = await LineNumberMaster.findById(fillingProcess.lineNumber);
        if (line) {
            line.status = 'Available';
            await line.save();
        }

        const log = new LogModel({  
            logType: "End Filling Process",
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            remarks: `Filling Process Ended with Serial Number ${fillingProcess._id}`,
        });
        await log.save();

        res.status(200).json(fillingProcess);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

const getStartAndEndDates = (period) => {
    const now = new Date();
    let startDate, endDate;

    switch (period) {
        case 'daily':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
            break;
        case 'weekly':
            startDate = new Date(now.setDate(now.getDate() - now.getDay()));
            endDate = new Date(now.setDate(now.getDate() - now.getDay() + 7));
            break;
        case 'monthly':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            break;
        case 'yearly':
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear() + 1, 0, 1);
            break;
        default:
            startDate = new Date(0);
            endDate = new Date();
            break;
    }

    return { startDate, endDate };
};

exports.getFillingProcessReport = async (req, res) => {
    try {
        const { period } = req.params;
        const { startDate, endDate } = getStartAndEndDates(period);
        const userWorkingPlace = req.user.workingPlace;

        const fillingProcesses = await FillingProcess.find({
            owner: userWorkingPlace,
            createdAt: { $gte: startDate, $lt: endDate }
        }).populate('cylinders').populate('cylinders.takenCustomer').populate('startedBy').populate('endedBy');

        const staffSet = new Set();
        fillingProcesses.forEach(process => {
            if (process.startedBy) staffSet.add(process.startedBy._id.toString());
            if (process.endedBy) staffSet.add(process.endedBy._id.toString());
        });

        const staffList = await StaffMaster.find({ _id: { $in: Array.from(staffSet) } });

        const report = {
            totalProcesses: fillingProcesses.length,
            totalCylinders: fillingProcesses.reduce((sum, process) => sum + process.cylinderQty, 0),
            filledCylinders: fillingProcesses.reduce((sum, process) => sum + process.filledCylinders, 0),
            rejectedCylinders: fillingProcesses.reduce((sum, process) => sum + process.rejectedCylinders, 0),
            staffCount: staffSet.size,
            staffs: staffList,
            processes: fillingProcesses.map(process => ({
                processId: process._id,
                startedBy: process.startedBy,
                endedBy: process.endedBy,
                cylinderQty: process.cylinderQty,
                filledCylinders: process.filledCylinders,
                rejectedCylinders: process.rejectedCylinders,
                status: process.status,
                remark: process.remark,
                createdAt: process.createdAt,
                updatedAt: process.updatedAt
            }))
        };

        res.status(200).json(report);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getFillingProcessReportByDate = async (req, res) => {
    try {
        const { date } = req.params;
        const userWorkingPlace = req.user.workingPlace;

        const startDate = new Date(date);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 1);

        const fillingProcesses = await FillingProcess.find({
            owner: userWorkingPlace,
            createdAt: { $gte: startDate, $lt: endDate }
        }).populate('cylinders').populate('cylinders.takenCustomer').populate('startedBy').populate('endedBy');

        const staffSet = new Set();
        fillingProcesses.forEach(process => {
            if (process.startedBy) staffSet.add(process.startedBy._id.toString());
            if (process.endedBy) staffSet.add(process.endedBy._id.toString());
        });

        const staffList = await StaffMaster.find({ _id: { $in: Array.from(staffSet) } });

        const report = {
            totalProcesses: fillingProcesses.length,
            totalCylinders: fillingProcesses.reduce((sum, process) => sum + process.cylinderQty, 0),
            filledCylinders: fillingProcesses.reduce((sum, process) => sum + process.filledCylinders, 0),
            rejectedCylinders: fillingProcesses.reduce((sum, process) => sum + process.rejectedCylinders, 0),
            staffCount: staffSet.size,
            staffs: staffList,
            processes: fillingProcesses.map(process => ({
                processId: process._id,
                startedBy: process.startedBy,
                endedBy: process.endedBy,
                cylinderQty: process.cylinderQty,
                filledCylinders: process.filledCylinders,
                rejectedCylinders: process.rejectedCylinders,
                status: process.status,
                remark: process.remark,
                createdAt: process.createdAt,
                updatedAt: process.updatedAt
            }))
        };

        res.status(200).json(report);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};
exports.getFillingProcessesByLineNumberToday = async (req, res) => {
    try {
        const { lineNumber,date } = req.params;
        const userWorkingPlace = req.user.workingPlace;

        console.log('Picked Date',date)

        const startDate = new Date(date);
        startDate.setHours(0, 0, 0, 0);
        const endDate = new Date(date);
        endDate.setHours(23, 59, 59, 999);

        const fillingProcesses = await FillingProcess.find({
            owner: userWorkingPlace,
            lineNumber: lineNumber,
            createdAt: { $gte: startDate, $lt: endDate }
        }).populate('cylinders').populate('cylinders.takenCustomer').populate('startedBy').populate('endedBy');

        res.status(200).json(fillingProcesses);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

exports.getFillingReport = async (req, res) => {
    try {
      const { date } = req.params; // Expecting date as a URL parameter (YYYY-MM-DD)
  
      // Validate date format (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
      }
      
      // Use moment-timezone (or plain Date) to build start and end of the day.
      // For timezone consistency, you might use moment-timezone. Here we'll use plain Date:
      const startDate = new Date(date);
      if (isNaN(startDate.getTime())) {
        return res.status(400).json({ message: 'Invalid date' });
      }
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(startDate);
      endDate.setHours(23, 59, 59, 999);
  
      // Query filling processes created within the day
      const fillingProcesses = await FillingProcess.find({
        createdAt: { $gte: startDate, $lte: endDate },
        owner: req.user.workingPlace,
      })
        .populate('lineNumber')
        .populate('startedBy')
        .populate('endedBy');
  
      // Group by process status
      const byStatus = fillingProcesses.reduce((acc, process) => {
        const status = process.status;
        if (!acc[status]) {
          acc[status] = { count: 0, cylinderQty: 0, filled: 0, rejected: 0 };
        }
        acc[status].count++;
        acc[status].cylinderQty += process.cylinderQty || 0;
        acc[status].filled += process.filledCylinders || 0;
        acc[status].rejected += process.rejectedCylinders || 0;
        return acc;
      }, {});
  
      // Group by lineNumber
      const byLineNumber = fillingProcesses.reduce((acc, process) => {
        const lineNumberId = process.lineNumber?._id?.toString() || 'Unknown'; // Ensure line number and ID exist
        if (!acc[lineNumberId]) {
          acc[lineNumberId] = {
            lineNumber: process.lineNumber, // Store the populated lineNumber object
            totalProcesses: 0,
            totalCylinders: 0,
            totalFilled: 0,
            totalRejected: 0,
          };
        }
        acc[lineNumberId].totalProcesses++;
        acc[lineNumberId].totalCylinders += process.cylinderQty || 0;
        acc[lineNumberId].totalFilled += process.filledCylinders || 0;
        acc[lineNumberId].totalRejected += process.rejectedCylinders || 0;
        return acc;
      }, {});
  
      const lineNumberReport = Object.values(byLineNumber);
  
      // Build summary statistics
      const totalProcesses = fillingProcesses.length;
      const totalCylinders = fillingProcesses.reduce((acc, process) => acc + (process.cylinderQty || 0), 0);
      const totalFilled = fillingProcesses.reduce((acc, process) => acc + (process.filledCylinders || 0), 0);
      const totalRejected = fillingProcesses.reduce((acc, process) => acc + (process.rejectedCylinders || 0), 0);
      const allLine = await LineNumberMaster.find({ owner: req.user.workingPlace });
      const avaliableLines=allLine.filter((line)=>line.status=='Available').length;
      const unAvaliableLines=allLine.filter((line)=>line.status=='NotAvailable').length;
      const totalLines=allLine.length;
      // Build the report
      const report = {
        date,
        totalLines,
        avaliableLines,
        unAvaliableLines,
        totalProcesses,
        totalCylinders,
        totalFilled,
        totalRejected,
        byStatus, // Detailed breakdown by status
        lineNumberReport, // Grouped report by line number
      };
  
      res.status(200).json(report);
    } catch (error) {
      console.error("Error generating filling report:", error);
      res.status(500).json({ message: error.message });
    }
  };
  