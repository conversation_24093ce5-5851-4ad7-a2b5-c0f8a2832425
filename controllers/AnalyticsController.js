const SellMaster = require("../models/SellMaster");
const CylinderMaster = require("../models/CylinderMaster");
const CustomerMaster = require("../models/CustomerMaster");
const moment = require("moment-timezone");
const ExcelJS = require("exceljs");

// Helper function to get date range
const getDateRange = (period, startDate, endDate) => {
  const timezone = "Asia/Yangon";
  let start, end;

  switch (period) {
    case "daily":
      start = moment.tz(startDate, timezone).startOf("day").toDate();
      end = moment.tz(startDate, timezone).endOf("day").toDate();
      break;
    case "monthly":
      start = moment.tz(startDate, timezone).startOf("month").toDate();
      end = moment.tz(startDate, timezone).endOf("month").toDate();
      break;
    case "yearly":
      start = moment.tz(startDate, timezone).startOf("year").toDate();
      end = moment.tz(startDate, timezone).endOf("year").toDate();
      break;
    case "period":
      start = moment.tz(startDate, timezone).startOf("day").toDate();
      end = moment.tz(endDate, timezone).endOf("day").toDate();
      break;
    default:
      throw new Error("Invalid period type");
  }

  return { start, end };
};

// Helper function to calculate analytics
const calculateAnalytics = (sales) => {
  const analytics = {
    totalSales: 0,
    totalDiscount: 0,
    netSales: 0,
    totalQuantitySold: 0,
    totalQuantityReturned: 0,
    totalTransactions: sales.length,
    cashSales: {
      amount: 0,
      transactions: 0,
      quantity: 0,
    },
    creditSales: {
      amount: 0,
      transactions: 0,
      quantity: 0,
    },
    cylinderSizes: {},
    customerTypes: {},
    topCustomers: {},
    dailyBreakdown: {},
  };

  sales.forEach((sale) => {
    // Basic totals
    analytics.totalSales += sale.total;
    analytics.totalDiscount += sale.discount || 0;
    analytics.totalQuantitySold += sale.quantity;
    analytics.totalQuantityReturned += sale.returnedCylinders
      ? sale.returnedCylinders.length
      : 0;

    // Payment type analysis
    if (sale.payment === "Cash") {
      analytics.cashSales.amount += sale.total;
      analytics.cashSales.transactions += 1;
      analytics.cashSales.quantity += sale.quantity;
    } else if (sale.payment === "Credit") {
      analytics.creditSales.amount += sale.total;
      analytics.creditSales.transactions += 1;
      analytics.creditSales.quantity += sale.quantity;
    }

    // Cylinder size analysis
    if (sale.cylinders) {
      sale.cylinders.forEach((cyl) => {
        if (cyl.cylinder && cyl.cylinder.cylinderSize) {
          const size = cyl.cylinder.cylinderSize;
          if (!analytics.cylinderSizes[size]) {
            analytics.cylinderSizes[size] = { quantity: 0, amount: 0 };
          }
          analytics.cylinderSizes[size].quantity += 1;
          analytics.cylinderSizes[size].amount += cyl.price;
        }
      });
    }

    // Customer type analysis
    if (sale.customer && sale.customer.customerType) {
      const type = sale.customer.customerType;
      if (!analytics.customerTypes[type]) {
        analytics.customerTypes[type] = {
          transactions: 0,
          amount: 0,
          quantity: 0,
        };
      }
      analytics.customerTypes[type].transactions += 1;
      analytics.customerTypes[type].amount += sale.total;
      analytics.customerTypes[type].quantity += sale.quantity;
    }

    // Top customers analysis
    if (sale.customer) {
      const customerId = sale.customer._id.toString();
      const customerName = sale.customer.name;
      if (!analytics.topCustomers[customerId]) {
        analytics.topCustomers[customerId] = {
          name: customerName,
          transactions: 0,
          amount: 0,
          quantity: 0,
        };
      }
      analytics.topCustomers[customerId].transactions += 1;
      analytics.topCustomers[customerId].amount += sale.total;
      analytics.topCustomers[customerId].quantity += sale.quantity;
    }

    // Daily breakdown
    const day = moment(sale.createdAt).tz("Asia/Yangon").format("YYYY-MM-DD");
    if (!analytics.dailyBreakdown[day]) {
      analytics.dailyBreakdown[day] = {
        transactions: 0,
        amount: 0,
        quantity: 0,
        returns: 0,
        cashAmount: 0,
        creditAmount: 0,
      };
    }
    analytics.dailyBreakdown[day].transactions += 1;
    analytics.dailyBreakdown[day].amount += sale.total;
    analytics.dailyBreakdown[day].quantity += sale.quantity;
    analytics.dailyBreakdown[day].returns += sale.returnedCylinders
      ? sale.returnedCylinders.length
      : 0;

    if (sale.payment === "Cash") {
      analytics.dailyBreakdown[day].cashAmount += sale.total;
    } else if (sale.payment === "Credit") {
      analytics.dailyBreakdown[day].creditAmount += sale.total;
    }
  });

  analytics.netSales = analytics.totalSales - analytics.totalDiscount;

  // Convert top customers to array and sort
  analytics.topCustomers = Object.values(analytics.topCustomers)
    .sort((a, b) => b.amount - a.amount)
    .slice(0, 10);

  return analytics;
};

// Get Daily Sales Analytics
exports.getDailyAnalytics = async (req, res) => {
  try {
    const { date } = req.params;

    if (!moment(date, "YYYY-MM-DD", true).isValid()) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM-DD" });
    }

    const { start, end } = getDateRange("daily", date);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    res.status(200).json({
      period: "daily",
      date: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      analytics,
      transactions: sales,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Monthly Sales Analytics
exports.getMonthlyAnalytics = async (req, res) => {
  try {
    const { date } = req.params; // Format: YYYY-MM

    if (!moment(date, "YYYY-MM", true).isValid()) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM" });
    }

    const { start, end } = getDateRange("monthly", date + "-01");

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    res.status(200).json({
      period: "monthly",
      month: moment(start).tz("Asia/Yangon").format("YYYY-MM"),
      analytics,
      transactionCount: sales.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Yearly Sales Analytics
exports.getYearlyAnalytics = async (req, res) => {
  try {
    const { year } = req.params;

    if (!moment(year, "YYYY", true).isValid()) {
      return res.status(400).json({ message: "Invalid year format. Use YYYY" });
    }

    const { start, end } = getDateRange("yearly", year + "-01-01");

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    // Monthly breakdown for the year
    const monthlyBreakdown = {};
    for (let month = 0; month < 12; month++) {
      const monthKey = moment().month(month).format("MMMM");
      monthlyBreakdown[monthKey] = {
        transactions: 0,
        amount: 0,
        quantity: 0,
        returns: 0,
        cashAmount: 0,
        creditAmount: 0,
      };
    }

    sales.forEach((sale) => {
      const monthKey = moment(sale.createdAt).format("MMMM");
      monthlyBreakdown[monthKey].transactions += 1;
      monthlyBreakdown[monthKey].amount += sale.total;
      monthlyBreakdown[monthKey].quantity += sale.quantity;
      monthlyBreakdown[monthKey].returns += sale.returnedCylinders
        ? sale.returnedCylinders.length
        : 0;

      if (sale.payment === "Cash") {
        monthlyBreakdown[monthKey].cashAmount += sale.total;
      } else if (sale.payment === "Credit") {
        monthlyBreakdown[monthKey].creditAmount += sale.total;
      }
    });

    res.status(200).json({
      period: "yearly",
      year: parseInt(year),
      analytics,
      monthlyBreakdown,
      transactionCount: sales.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Period Sales Analytics (Custom Date Range)
exports.getPeriodAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    if (
      !moment(startDate, "YYYY-MM-DD", true).isValid() ||
      !moment(endDate, "YYYY-MM-DD", true).isValid()
    ) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM-DD" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    res.status(200).json({
      period: "custom",
      startDate: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      endDate: moment(end).tz("Asia/Yangon").format("YYYY-MM-DD"),
      analytics,
      transactionCount: sales.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Cylinder Return Analytics
exports.getCylinderReturnAnalytics = async (req, res) => {
  try {
    const { period, startDate, endDate } = req.query;

    if (!period) {
      return res.status(400).json({
        message: "Period is required (daily, monthly, yearly, or period)",
      });
    }

    let dateRange;
    if (period === "period") {
      if (!startDate || !endDate) {
        return res.status(400).json({
          message:
            "Both startDate and endDate are required for period analytics",
        });
      }
      dateRange = getDateRange("period", startDate, endDate);
    } else {
      if (!startDate) {
        return res.status(400).json({ message: "startDate is required" });
      }
      dateRange = getDateRange(period, startDate);
    }

    const { start, end } = dateRange;

    // Get all sales with returned cylinders
    const salesWithReturns = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
      returnedCylinders: { $exists: true, $not: { $size: 0 } },
    })
      .populate("customer", "name customerType")
      .populate("returnedCylinders", "cylinderSize status")
      .populate("staff", "name");

    const returnAnalytics = {
      totalReturns: 0,
      returnsBySize: {},
      returnsByCustomerType: {},
      returnsByDay: {},
      topReturningCustomers: {},
    };

    salesWithReturns.forEach((sale) => {
      const returnCount = sale.returnedCylinders.length;
      returnAnalytics.totalReturns += returnCount;

      // Returns by cylinder size
      sale.returnedCylinders.forEach((cylinder) => {
        const size = cylinder.cylinderSize;
        if (!returnAnalytics.returnsBySize[size]) {
          returnAnalytics.returnsBySize[size] = 0;
        }
        returnAnalytics.returnsBySize[size] += 1;
      });

      // Returns by customer type
      if (sale.customer && sale.customer.customerType) {
        const type = sale.customer.customerType;
        if (!returnAnalytics.returnsByCustomerType[type]) {
          returnAnalytics.returnsByCustomerType[type] = 0;
        }
        returnAnalytics.returnsByCustomerType[type] += returnCount;
      }

      // Returns by day
      const day = moment(sale.createdAt).tz("Asia/Yangon").format("YYYY-MM-DD");
      if (!returnAnalytics.returnsByDay[day]) {
        returnAnalytics.returnsByDay[day] = 0;
      }
      returnAnalytics.returnsByDay[day] += returnCount;

      // Top returning customers
      if (sale.customer) {
        const customerId = sale.customer._id.toString();
        const customerName = sale.customer.name;
        if (!returnAnalytics.topReturningCustomers[customerId]) {
          returnAnalytics.topReturningCustomers[customerId] = {
            name: customerName,
            returns: 0,
          };
        }
        returnAnalytics.topReturningCustomers[customerId].returns +=
          returnCount;
      }
    });

    // Convert top returning customers to array and sort
    returnAnalytics.topReturningCustomers = Object.values(
      returnAnalytics.topReturningCustomers
    )
      .sort((a, b) => b.returns - a.returns)
      .slice(0, 10);

    res.status(200).json({
      period,
      startDate: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      endDate: moment(end).tz("Asia/Yangon").format("YYYY-MM-DD"),
      returnAnalytics,
      transactionsWithReturns: salesWithReturns.length,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Combined Sales and Returns Summary
exports.getCombinedSummary = async (req, res) => {
  try {
    const { period, startDate, endDate } = req.query;

    if (!period) {
      return res.status(400).json({
        message: "Period is required (daily, monthly, yearly, or period)",
      });
    }

    let dateRange;
    if (period === "period") {
      if (!startDate || !endDate) {
        return res.status(400).json({
          message:
            "Both startDate and endDate are required for period analytics",
        });
      }
      dateRange = getDateRange("period", startDate, endDate);
    } else {
      if (!startDate) {
        return res.status(400).json({ message: "startDate is required" });
      }
      dateRange = getDateRange(period, startDate);
    }

    const { start, end } = dateRange;

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    // Additional summary metrics
    const summary = {
      ...analytics,
      averageTransactionValue:
        analytics.totalTransactions > 0
          ? analytics.totalSales / analytics.totalTransactions
          : 0,
      returnRate:
        analytics.totalQuantitySold > 0
          ? (analytics.totalQuantityReturned / analytics.totalQuantitySold) *
            100
          : 0,
      cashPercentage:
        analytics.totalSales > 0
          ? (analytics.cashSales.amount / analytics.totalSales) * 100
          : 0,
      creditPercentage:
        analytics.totalSales > 0
          ? (analytics.creditSales.amount / analytics.totalSales) * 100
          : 0,
    };

    res.status(200).json({
      period,
      startDate: moment(start).tz("Asia/Yangon").format("YYYY-MM-DD"),
      endDate: moment(end).tz("Asia/Yangon").format("YYYY-MM-DD"),
      summary,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Daily Overview Report (Tabular format like your example)
exports.getDailyOverview = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    if (
      !moment(startDate, "YYYY-MM-DD", true).isValid() ||
      !moment(endDate, "YYYY-MM-DD", true).isValid()
    ) {
      return res
        .status(400)
        .json({ message: "Invalid date format. Use YYYY-MM-DD" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    // Get all sales data for the period
    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Get total cylinders count from CylinderMaster
    const totalCylinders = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
    });

    // Create daily data structure
    const dailyData = {};
    const dateHeaders = [];

    // Generate all dates in the range
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      const dateKey = currentDate.format("YYYY-MM-DD");
      const dayNumber = currentDate.format("D");
      const monthYear = currentDate.format("MMM-YY");

      dateHeaders.push({
        date: dateKey,
        day: dayNumber,
        monthYear: monthYear,
        fullDate: currentDate.format("DD MMM YYYY"),
      });

      dailyData[dateKey] = {
        deliver: 0,
        return: 0,
        balanceInCirculation: 0,
        balanceAtFactory: 0,
        totalCylinders: totalCylinders,
      };

      currentDate.add(1, "day");
    }

    // Process sales data
    sales.forEach((sale) => {
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (dailyData[saleDate]) {
        // Count delivered cylinders
        dailyData[saleDate].deliver += sale.quantity;

        // Count returned cylinders
        if (sale.returnedCylinders) {
          dailyData[saleDate].return += sale.returnedCylinders.length;
        }
      }
    });

    // Calculate running balances
    let runningCirculation = 0;

    // Get initial circulation count (cylinders that are 'Taken' before start date)
    const initialCirculation = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
      status: "Taken",
    });

    runningCirculation = initialCirculation;

    // Calculate balances for each day
    Object.keys(dailyData)
      .sort()
      .forEach((dateKey) => {
        const dayData = dailyData[dateKey];

        // Update circulation: add delivered, subtract returned
        runningCirculation =
          runningCirculation + dayData.deliver - dayData.return;

        dayData.balanceInCirculation = runningCirculation;
        dayData.balanceAtFactory = totalCylinders - runningCirculation;
      });

    // Format response similar to your table structure
    const overview = {
      period: {
        from: moment(start).tz("Asia/Yangon").format("Do MMMM"),
        to: moment(end).tz("Asia/Yangon").format("Do MMMM"),
        fromDate: startDate,
        toDate: endDate,
      },
      headers: dateHeaders,
      dailyOverall: {
        deliver: dateHeaders.map((header) => dailyData[header.date].deliver),
        return: dateHeaders.map((header) => dailyData[header.date].return),
        balanceInCirculation: dateHeaders.map(
          (header) => dailyData[header.date].balanceInCirculation
        ),
        balanceAtFactory: dateHeaders.map(
          (header) => dailyData[header.date].balanceAtFactory
        ),
        totalCylinders: dateHeaders.map(
          (header) => dailyData[header.date].totalCylinders
        ),
      },
      dailyData: dailyData,
      summary: {
        totalDelivered: Object.values(dailyData).reduce(
          (sum, day) => sum + day.deliver,
          0
        ),
        totalReturned: Object.values(dailyData).reduce(
          (sum, day) => sum + day.return,
          0
        ),
        netDelivered: Object.values(dailyData).reduce(
          (sum, day) => sum + day.deliver - day.return,
          0
        ),
        totalCylinders: totalCylinders,
      },
    };

    res.status(200).json(overview);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Daily Overview by Customer
exports.getDailyOverviewByCustomer = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Generate date headers
    const dateHeaders = [];
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      dateHeaders.push({
        date: currentDate.format("YYYY-MM-DD"),
        day: currentDate.format("D"),
        monthYear: currentDate.format("MMM-YY"),
      });
      currentDate.add(1, "day");
    }

    // Group by customer and calculate running balances
    const customerData = {};

    sales.forEach((sale) => {
      if (!sale.customer) return;

      const customerId = sale.customer._id.toString();
      const customerName = sale.customer.name;
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (!customerData[customerId]) {
        customerData[customerId] = {
          name: customerName,
          customerType: sale.customer.customerType,
          dailyDelivery: {},
          dailyReturn: {},
          dailyBalance: {},
          totalDelivered: 0,
          totalReturned: 0,
        };

        // Initialize all dates with 0
        dateHeaders.forEach((header) => {
          customerData[customerId].dailyDelivery[header.date] = 0;
          customerData[customerId].dailyReturn[header.date] = 0;
          customerData[customerId].dailyBalance[header.date] = 0;
        });
      }

      // Add delivery data
      customerData[customerId].dailyDelivery[saleDate] += sale.quantity;
      customerData[customerId].totalDelivered += sale.quantity;

      // Add return data
      if (sale.returnedCylinders) {
        customerData[customerId].dailyReturn[saleDate] +=
          sale.returnedCylinders.length;
        customerData[customerId].totalReturned += sale.returnedCylinders.length;
      }
    });

    // Calculate running balances for each customer
    Object.values(customerData).forEach((customer) => {
      let runningBalance = 0;
      dateHeaders.forEach((header) => {
        const delivered = customer.dailyDelivery[header.date];
        const returned = customer.dailyReturn[header.date];
        runningBalance = runningBalance + delivered - returned;
        customer.dailyBalance[header.date] = runningBalance;
      });
    });

    // Format for your requested table display
    const customerOverview = Object.values(customerData).map(
      (customer, index) => ({
        sr: index + 1,
        customerName: customer.name,
        customerType: customer.customerType,
        delivered: dateHeaders.map(
          (header) => customer.dailyDelivery[header.date]
        ),
        returned: dateHeaders.map(
          (header) => customer.dailyReturn[header.date]
        ),
        balance: dateHeaders.map(
          (header) => customer.dailyBalance[header.date]
        ),
        totalDelivered: customer.totalDelivered,
        totalReturned: customer.totalReturned,
        finalBalance:
          customer.dailyBalance[dateHeaders[dateHeaders.length - 1].date] || 0,
      })
    );

    res.status(200).json({
      period: {
        from: moment(start).tz("Asia/Yangon").format("Do MMMM"),
        to: moment(end).tz("Asia/Yangon").format("Do MMMM"),
        fromDate: startDate,
        toDate: endDate,
      },
      headers: dateHeaders,
      customerOverview: customerOverview.sort(
        (a, b) => b.totalDelivered - a.totalDelivered
      ),
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Get Daily Overview by Customer Type (Your requested format)
exports.getDailyOverviewByCustomerType = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Get total cylinders for overall calculations
    const totalCylinders = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
    });

    // Generate date headers
    const dateHeaders = [];
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      dateHeaders.push({
        date: currentDate.format("YYYY-MM-DD"),
        day: currentDate.format("D"),
        monthYear: currentDate.format("MMM-YY"),
      });
      currentDate.add(1, "day");
    }

    // Group by customer type and calculate running balances
    const customerTypeData = {};
    const customerTypes = [
      "Hospital",
      "Individual",
      "Shop",
      "Factory",
      "Workshop",
    ];

    // Initialize customer types
    customerTypes.forEach((type) => {
      customerTypeData[type] = {
        dailyDelivery: {},
        dailyReturn: {},
        dailyBalance: {},
        totalDelivered: 0,
        totalReturned: 0,
      };

      dateHeaders.forEach((header) => {
        customerTypeData[type].dailyDelivery[header.date] = 0;
        customerTypeData[type].dailyReturn[header.date] = 0;
        customerTypeData[type].dailyBalance[header.date] = 0;
      });
    });

    // Process sales data
    sales.forEach((sale) => {
      if (!sale.customer || !sale.customer.customerType) return;

      const customerType = sale.customer.customerType;
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (customerTypeData[customerType]) {
        // Add delivery data
        customerTypeData[customerType].dailyDelivery[saleDate] += sale.quantity;
        customerTypeData[customerType].totalDelivered += sale.quantity;

        // Add return data
        if (sale.returnedCylinders) {
          customerTypeData[customerType].dailyReturn[saleDate] +=
            sale.returnedCylinders.length;
          customerTypeData[customerType].totalReturned +=
            sale.returnedCylinders.length;
        }
      }
    });

    // Calculate running balances for each customer type
    Object.values(customerTypeData).forEach((typeData) => {
      let runningBalance = 0;
      dateHeaders.forEach((header) => {
        const delivered = typeData.dailyDelivery[header.date];
        const returned = typeData.dailyReturn[header.date];
        runningBalance = runningBalance + delivered - returned;
        typeData.dailyBalance[header.date] = runningBalance;
      });
    });

    // Calculate totals for each day
    const dailyTotals = {};
    dateHeaders.forEach((header) => {
      dailyTotals[header.date] = {
        delivered: 0,
        returned: 0,
        balanceInCirculation: 0,
        balanceAtFactory: 0,
        totalCylinders: totalCylinders,
      };
    });

    // Sum up all customer types for daily totals
    Object.values(customerTypeData).forEach((typeData) => {
      dateHeaders.forEach((header) => {
        dailyTotals[header.date].delivered +=
          typeData.dailyDelivery[header.date];
        dailyTotals[header.date].returned += typeData.dailyReturn[header.date];
      });
    });

    // Calculate running circulation balance
    const initialCirculation = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
      status: "Taken",
    });

    let runningCirculation = initialCirculation;
    dateHeaders.forEach((header) => {
      const delivered = dailyTotals[header.date].delivered;
      const returned = dailyTotals[header.date].returned;
      runningCirculation = runningCirculation + delivered - returned;
      dailyTotals[header.date].balanceInCirculation = runningCirculation;
      dailyTotals[header.date].balanceAtFactory =
        totalCylinders - runningCirculation;
    });

    // Format for your requested table display
    const customerTypeOverview = customerTypes.map((type) => ({
      customerType: type,
      delivered: dateHeaders.map(
        (header) => customerTypeData[type].dailyDelivery[header.date]
      ),
      returned: dateHeaders.map(
        (header) => customerTypeData[type].dailyReturn[header.date]
      ),
      balance: dateHeaders.map(
        (header) => customerTypeData[type].dailyBalance[header.date]
      ),
      totalDelivered: customerTypeData[type].totalDelivered,
      totalReturned: customerTypeData[type].totalReturned,
      finalBalance:
        customerTypeData[type].dailyBalance[
          dateHeaders[dateHeaders.length - 1].date
        ] || 0,
    }));

    // Add totals section
    const totals = {
      delivered: dateHeaders.map(
        (header) => dailyTotals[header.date].delivered
      ),
      returned: dateHeaders.map((header) => dailyTotals[header.date].returned),
      balanceInCirculation: dateHeaders.map(
        (header) => dailyTotals[header.date].balanceInCirculation
      ),
      balanceAtFactory: dateHeaders.map(
        (header) => dailyTotals[header.date].balanceAtFactory
      ),
      totalCylinders: dateHeaders.map(
        (header) => dailyTotals[header.date].totalCylinders
      ),
    };

    res.status(200).json({
      period: {
        from: moment(start).tz("Asia/Yangon").format("Do MMMM"),
        to: moment(end).tz("Asia/Yangon").format("Do MMMM"),
        fromDate: startDate,
        toDate: endDate,
      },
      headers: dateHeaders,
      customerTypeOverview,
      totals,
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Helper function to create Excel workbook with styling
const createStyledWorkbook = () => {
  const workbook = new ExcelJS.Workbook();
  workbook.creator = "Cylinder Management System";
  workbook.lastModifiedBy = "Analytics System";
  workbook.created = new Date();
  workbook.modified = new Date();
  return workbook;
};

// Helper function to style headers
const styleHeaders = (worksheet, headerRow) => {
  headerRow.eachCell((cell) => {
    cell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF4472C4" },
    };
    cell.font = {
      color: { argb: "FFFFFFFF" },
      bold: true,
      size: 12,
    };
    cell.alignment = {
      vertical: "middle",
      horizontal: "center",
    };
    cell.border = {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    };
  });
};

// Helper function to style data rows
const styleDataRows = (worksheet, startRow, endRow) => {
  for (let i = startRow; i <= endRow; i++) {
    const row = worksheet.getRow(i);
    row.eachCell((cell) => {
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
      cell.alignment = {
        vertical: "middle",
        horizontal: "center",
      };
    });

    // Alternate row colors
    if (i % 2 === 0) {
      row.eachCell((cell) => {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF2F2F2" },
        };
      });
    }
  }
};

// Export Daily Overview to Excel
exports.exportDailyOverviewToExcel = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    // Get the data (reuse the logic from getDailyOverview)
    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    const totalCylinders = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
    });

    // Process data (same logic as getDailyOverview)
    const dailyData = {};
    const dateHeaders = [];

    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      const dateKey = currentDate.format("YYYY-MM-DD");
      const dayNumber = currentDate.format("D");
      const monthYear = currentDate.format("MMM-YY");

      dateHeaders.push({
        date: dateKey,
        day: dayNumber,
        monthYear: monthYear,
        fullDate: currentDate.format("DD MMM YYYY"),
      });

      dailyData[dateKey] = {
        deliver: 0,
        return: 0,
        balanceInCirculation: 0,
        balanceAtFactory: 0,
        totalCylinders: totalCylinders,
      };

      currentDate.add(1, "day");
    }

    // Process sales data
    sales.forEach((sale) => {
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (dailyData[saleDate]) {
        dailyData[saleDate].deliver += sale.quantity;
        if (sale.returnedCylinders) {
          dailyData[saleDate].return += sale.returnedCylinders.length;
        }
      }
    });

    // Calculate running balances
    const initialCirculation = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
      status: "Taken",
    });

    let runningCirculation = initialCirculation;

    Object.keys(dailyData)
      .sort()
      .forEach((dateKey) => {
        const dayData = dailyData[dateKey];
        runningCirculation =
          runningCirculation + dayData.deliver - dayData.return;
        dayData.balanceInCirculation = runningCirculation;
        dayData.balanceAtFactory = totalCylinders - runningCirculation;
      });

    // Create Excel workbook
    const workbook = createStyledWorkbook();
    const worksheet = workbook.addWorksheet("Daily Delivery Report");

    // Set column widths
    worksheet.columns = [
      { width: 20 },
      ...dateHeaders.map(() => ({ width: 12 })),
    ];

    // Add title
    const titleRow = worksheet.addRow(["Daily Delivery Reports"]);
    titleRow.getCell(1).font = { bold: true, size: 16 };
    worksheet.mergeCells(1, 1, 1, dateHeaders.length + 1);
    titleRow.getCell(1).alignment = { horizontal: "center" };

    // Add period info
    worksheet.addRow([]);
    const periodRow = worksheet.addRow(["Period"]);
    periodRow.getCell(1).font = { bold: true, size: 14 };

    const fromRow = worksheet.addRow([
      `From: ${moment(start).tz("Asia/Yangon").format("Do MMMM")}`,
    ]);
    const toRow = worksheet.addRow([
      `To: ${moment(end).tz("Asia/Yangon").format("Do MMMM")}`,
    ]);

    // Add empty row
    worksheet.addRow([]);

    // Add month headers
    const monthHeaders = [""];
    let currentMonth = "";
    dateHeaders.forEach((header) => {
      if (header.monthYear !== currentMonth) {
        monthHeaders.push(header.monthYear);
        currentMonth = header.monthYear;
      } else {
        monthHeaders.push("");
      }
    });

    const monthRow = worksheet.addRow(monthHeaders);
    styleHeaders(worksheet, monthRow);

    // Add day headers
    const dayHeaders = ["", ...dateHeaders.map((h) => h.day)];
    const dayRow = worksheet.addRow(dayHeaders);
    styleHeaders(worksheet, dayRow);

    // Add data rows
    const dataStartRow = worksheet.lastRow.number + 1;

    const deliverRow = worksheet.addRow([
      "Deliver",
      ...dateHeaders.map((h) => dailyData[h.date].deliver),
    ]);
    const returnRow = worksheet.addRow([
      "Return",
      ...dateHeaders.map((h) => dailyData[h.date].return),
    ]);
    const circulationRow = worksheet.addRow([
      "Balance in Circulation",
      ...dateHeaders.map((h) => dailyData[h.date].balanceInCirculation),
    ]);
    const factoryRow = worksheet.addRow([
      "Balance at Factory",
      ...dateHeaders.map((h) => dailyData[h.date].balanceAtFactory),
    ]);
    const totalRow = worksheet.addRow([
      "Total Cylinders",
      ...dateHeaders.map((h) => dailyData[h.date].totalCylinders),
    ]);

    const dataEndRow = worksheet.lastRow.number;

    // Style data rows
    styleDataRows(worksheet, dataStartRow, dataEndRow);

    // Style first column (labels)
    for (let i = dataStartRow; i <= dataEndRow; i++) {
      const cell = worksheet.getCell(i, 1);
      cell.font = { bold: true };
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFE7E6E6" },
      };
    }

    // Set response headers for file download
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=Daily_Overview_${startDate}_to_${endDate}.xlsx`
    );

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Export Daily Overview by Customer to Excel (Your requested format)
exports.exportDailyOverviewByCustomerToExcel = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Generate date headers
    const dateHeaders = [];
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      dateHeaders.push({
        date: currentDate.format("YYYY-MM-DD"),
        day: currentDate.format("D"),
        monthYear: currentDate.format("MMM-YY"),
      });
      currentDate.add(1, "day");
    }

    // Group by customer and calculate running balances
    const customerData = {};

    sales.forEach((sale) => {
      if (!sale.customer) return;

      const customerId = sale.customer._id.toString();
      const customerName = sale.customer.name;
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (!customerData[customerId]) {
        customerData[customerId] = {
          name: customerName,
          customerType: sale.customer.customerType,
          dailyDelivery: {},
          dailyReturn: {},
          dailyBalance: {},
          totalDelivered: 0,
          totalReturned: 0,
        };

        dateHeaders.forEach((header) => {
          customerData[customerId].dailyDelivery[header.date] = 0;
          customerData[customerId].dailyReturn[header.date] = 0;
          customerData[customerId].dailyBalance[header.date] = 0;
        });
      }

      customerData[customerId].dailyDelivery[saleDate] += sale.quantity;
      customerData[customerId].totalDelivered += sale.quantity;

      if (sale.returnedCylinders) {
        customerData[customerId].dailyReturn[saleDate] +=
          sale.returnedCylinders.length;
        customerData[customerId].totalReturned += sale.returnedCylinders.length;
      }
    });

    // Calculate running balances for each customer
    Object.values(customerData).forEach((customer) => {
      let runningBalance = 0;
      dateHeaders.forEach((header) => {
        const delivered = customer.dailyDelivery[header.date];
        const returned = customer.dailyReturn[header.date];
        runningBalance = runningBalance + delivered - returned;
        customer.dailyBalance[header.date] = runningBalance;
      });
    });

    // Create Excel workbook
    const workbook = createStyledWorkbook();
    const worksheet = workbook.addWorksheet("Daily By Customer");

    // Set column widths
    worksheet.columns = [
      { width: 5 }, // Sr
      { width: 20 }, // Customer
      { width: 12 }, // Type (Delivered/Returned/Balance)
      ...dateHeaders.map(() => ({ width: 8 })), // Date columns
    ];

    // Add title
    const titleRow = worksheet.addRow(["Daily By Customer"]);
    titleRow.getCell(1).font = { bold: true, size: 16 };
    worksheet.mergeCells(1, 1, 1, dateHeaders.length + 3);
    titleRow.getCell(1).alignment = { horizontal: "center" };

    // Add empty row
    worksheet.addRow([]);

    // Add month headers
    const monthHeaders = ["", "", ""];
    let currentMonth = "";
    dateHeaders.forEach((header) => {
      if (header.monthYear !== currentMonth) {
        monthHeaders.push(header.monthYear);
        currentMonth = header.monthYear;
      } else {
        monthHeaders.push("");
      }
    });

    const monthRow = worksheet.addRow(monthHeaders);
    styleHeaders(worksheet, monthRow);

    // Add day headers
    const dayHeaders = ["Sr", "Customer", "", ...dateHeaders.map((h) => h.day)];
    const dayRow = worksheet.addRow(dayHeaders);
    styleHeaders(worksheet, dayRow);

    // Add customer data in the requested format
    const dataStartRow = worksheet.lastRow.number + 1;
    const customerOverview = Object.values(customerData).sort(
      (a, b) => b.totalDelivered - a.totalDelivered
    );

    let srNo = 1;
    customerOverview.forEach((customer) => {
      // Delivered row
      const deliveredRow = [
        srNo,
        customer.name,
        "Delivered",
        ...dateHeaders.map((header) => customer.dailyDelivery[header.date]),
      ];
      worksheet.addRow(deliveredRow);

      // Returned row
      const returnedRow = [
        "",
        "",
        "Returned",
        ...dateHeaders.map((header) => customer.dailyReturn[header.date]),
      ];
      worksheet.addRow(returnedRow);

      // Balance row
      const balanceRow = [
        "",
        "",
        "Balance",
        ...dateHeaders.map((header) => customer.dailyBalance[header.date]),
      ];
      worksheet.addRow(balanceRow);

      srNo++;
    });

    const dataEndRow = worksheet.lastRow.number;

    // Style data rows with special formatting for customer groups
    for (let i = dataStartRow; i <= dataEndRow; i++) {
      const row = worksheet.getRow(i);
      row.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
        cell.alignment = {
          vertical: "middle",
          horizontal: "center",
        };
      });

      // Different styling for each row type
      const rowType = row.getCell(3).value;
      if (rowType === "Delivered") {
        // Light blue background for delivered rows
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFE6F3FF" },
          };
        });
        // Bold for customer name and sr number
        row.getCell(1).font = { bold: true };
        row.getCell(2).font = { bold: true };
      } else if (rowType === "Returned") {
        // Light orange background for returned rows
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFFFF2E6" },
          };
        });
      } else if (rowType === "Balance") {
        // Light green background for balance rows
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFE6F7E6" },
          };
        });
        // Bold for balance values
        row.eachCell((cell, colNumber) => {
          if (colNumber > 3) {
            cell.font = { bold: true };
          }
        });
      }

      // Left align customer names and row types
      row.getCell(2).alignment = { vertical: "middle", horizontal: "left" };
      row.getCell(3).alignment = { vertical: "middle", horizontal: "left" };
    }

    // Set response headers
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=Daily_By_Customer_${startDate}_to_${endDate}.xlsx`
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Export Daily Overview by Customer Type to Excel (Your requested format)
exports.exportDailyOverviewByCustomerTypeToExcel = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res
        .status(400)
        .json({ message: "Both startDate and endDate are required" });
    }

    const { start, end } = getDateRange("period", startDate, endDate);

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize");

    // Get total cylinders for overall calculations
    const totalCylinders = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
    });

    // Generate date headers
    const dateHeaders = [];
    let currentDate = moment(start).tz("Asia/Yangon");
    const endMoment = moment(end).tz("Asia/Yangon");

    while (currentDate.isSameOrBefore(endMoment, "day")) {
      dateHeaders.push({
        date: currentDate.format("YYYY-MM-DD"),
        day: currentDate.format("D"),
        monthYear: currentDate.format("MMM-YY"),
      });
      currentDate.add(1, "day");
    }

    // Group by customer type and calculate running balances
    const customerTypeData = {};
    const customerTypes = [
      "Hospital",
      "Individual",
      "Shop",
      "Factory",
      "Workshop",
    ];

    customerTypes.forEach((type) => {
      customerTypeData[type] = {
        dailyDelivery: {},
        dailyReturn: {},
        dailyBalance: {},
        totalDelivered: 0,
        totalReturned: 0,
      };

      dateHeaders.forEach((header) => {
        customerTypeData[type].dailyDelivery[header.date] = 0;
        customerTypeData[type].dailyReturn[header.date] = 0;
        customerTypeData[type].dailyBalance[header.date] = 0;
      });
    });

    // Process sales data
    sales.forEach((sale) => {
      if (!sale.customer || !sale.customer.customerType) return;

      const customerType = sale.customer.customerType;
      const saleDate = moment(sale.createdAt)
        .tz("Asia/Yangon")
        .format("YYYY-MM-DD");

      if (customerTypeData[customerType]) {
        customerTypeData[customerType].dailyDelivery[saleDate] += sale.quantity;
        customerTypeData[customerType].totalDelivered += sale.quantity;

        if (sale.returnedCylinders) {
          customerTypeData[customerType].dailyReturn[saleDate] +=
            sale.returnedCylinders.length;
          customerTypeData[customerType].totalReturned +=
            sale.returnedCylinders.length;
        }
      }
    });

    // Calculate running balances for each customer type
    Object.values(customerTypeData).forEach((typeData) => {
      let runningBalance = 0;
      dateHeaders.forEach((header) => {
        const delivered = typeData.dailyDelivery[header.date];
        const returned = typeData.dailyReturn[header.date];
        runningBalance = runningBalance + delivered - returned;
        typeData.dailyBalance[header.date] = runningBalance;
      });
    });

    // Calculate totals for each day
    const dailyTotals = {};
    dateHeaders.forEach((header) => {
      dailyTotals[header.date] = {
        delivered: 0,
        returned: 0,
        balanceInCirculation: 0,
        balanceAtFactory: 0,
        totalCylinders: totalCylinders,
      };
    });

    // Sum up all customer types for daily totals
    Object.values(customerTypeData).forEach((typeData) => {
      dateHeaders.forEach((header) => {
        dailyTotals[header.date].delivered +=
          typeData.dailyDelivery[header.date];
        dailyTotals[header.date].returned += typeData.dailyReturn[header.date];
      });
    });

    // Calculate running circulation balance
    const initialCirculation = await CylinderMaster.countDocuments({
      owner: req.user.workingPlace,
      status: "Taken",
    });

    let runningCirculation = initialCirculation;
    dateHeaders.forEach((header) => {
      const delivered = dailyTotals[header.date].delivered;
      const returned = dailyTotals[header.date].returned;
      runningCirculation = runningCirculation + delivered - returned;
      dailyTotals[header.date].balanceInCirculation = runningCirculation;
      dailyTotals[header.date].balanceAtFactory =
        totalCylinders - runningCirculation;
    });

    // Create Excel workbook
    const workbook = createStyledWorkbook();
    const worksheet = workbook.addWorksheet("Daily By Customer Type");

    // Set column widths
    worksheet.columns = [
      { width: 15 }, // Customer Type
      { width: 12 }, // Type (Delivered/Returned/Balance)
      ...dateHeaders.map(() => ({ width: 8 })), // Date columns
    ];

    // Add title
    const titleRow = worksheet.addRow(["Daily By Customer Type"]);
    titleRow.getCell(1).font = { bold: true, size: 16 };
    worksheet.mergeCells(1, 1, 1, dateHeaders.length + 2);
    titleRow.getCell(1).alignment = { horizontal: "center" };

    // Add empty row
    worksheet.addRow([]);

    // Add month headers
    const monthHeaders = ["", ""];
    let currentMonth = "";
    dateHeaders.forEach((header) => {
      if (header.monthYear !== currentMonth) {
        monthHeaders.push(header.monthYear);
        currentMonth = header.monthYear;
      } else {
        monthHeaders.push("");
      }
    });

    const monthRow = worksheet.addRow(monthHeaders);
    styleHeaders(worksheet, monthRow);

    // Add day headers
    const dayHeaders = ["Customer Type", "", ...dateHeaders.map((h) => h.day)];
    const dayRow = worksheet.addRow(dayHeaders);
    styleHeaders(worksheet, dayRow);

    // Add customer type data in the requested format
    const dataStartRow = worksheet.lastRow.number + 1;

    customerTypes.forEach((type) => {
      const typeData = customerTypeData[type];

      // Delivered row
      const deliveredRow = [
        type,
        "Delivered",
        ...dateHeaders.map((header) => typeData.dailyDelivery[header.date]),
      ];
      worksheet.addRow(deliveredRow);

      // Returned row
      const returnedRow = [
        "",
        "Returned",
        ...dateHeaders.map((header) => typeData.dailyReturn[header.date]),
      ];
      worksheet.addRow(returnedRow);

      // Balance row
      const balanceRow = [
        "",
        "Balance",
        ...dateHeaders.map((header) => typeData.dailyBalance[header.date]),
      ];
      worksheet.addRow(balanceRow);
    });

    // Add Total section
    const totalDeliveredRow = [
      "Total",
      "Delivered",
      ...dateHeaders.map((header) => dailyTotals[header.date].delivered),
    ];
    worksheet.addRow(totalDeliveredRow);

    const totalReturnedRow = [
      "",
      "Returned",
      ...dateHeaders.map((header) => dailyTotals[header.date].returned),
    ];
    worksheet.addRow(totalReturnedRow);

    const balanceInCirculationRow = [
      "",
      "Balance in Circulation",
      ...dateHeaders.map(
        (header) => dailyTotals[header.date].balanceInCirculation
      ),
    ];
    worksheet.addRow(balanceInCirculationRow);

    const balanceInFactoryRow = [
      "",
      "Balance in Factory",
      ...dateHeaders.map((header) => dailyTotals[header.date].balanceAtFactory),
    ];
    worksheet.addRow(balanceInFactoryRow);

    const totalCylindersRow = [
      "",
      "Total Cylinders",
      ...dateHeaders.map((header) => dailyTotals[header.date].totalCylinders),
    ];
    worksheet.addRow(totalCylindersRow);

    const dataEndRow = worksheet.lastRow.number;

    // Style data rows with special formatting for customer type groups
    for (let i = dataStartRow; i <= dataEndRow; i++) {
      const row = worksheet.getRow(i);
      row.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
        cell.alignment = {
          vertical: "middle",
          horizontal: "center",
        };
      });

      // Different styling for each row type
      const rowType = row.getCell(2).value;
      const customerType = row.getCell(1).value;

      if (rowType === "Delivered") {
        // Light blue background for delivered rows
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFE6F3FF" },
          };
        });
        // Bold for customer type name
        if (customerType && customerType !== "") {
          row.getCell(1).font = { bold: true };
        }
      } else if (rowType === "Returned") {
        // Light orange background for returned rows
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFFFF2E6" },
          };
        });
      } else if (rowType === "Balance") {
        // Light green background for balance rows
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFE6F7E6" },
          };
        });
        // Bold for balance values
        row.eachCell((cell, colNumber) => {
          if (colNumber > 2) {
            cell.font = { bold: true };
          }
        });
      } else if (
        customerType === "Total" ||
        rowType === "Balance in Circulation" ||
        rowType === "Balance in Factory" ||
        rowType === "Total Cylinders"
      ) {
        // Special styling for total rows
        row.eachCell((cell) => {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: "FFFFD700" }, // Gold background
          };
          cell.font = { bold: true };
        });
      }

      // Left align customer type names and row types
      row.getCell(1).alignment = { vertical: "middle", horizontal: "left" };
      row.getCell(2).alignment = { vertical: "middle", horizontal: "left" };
    }

    // Set response headers
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=Daily_By_Customer_Type_${startDate}_to_${endDate}.xlsx`
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// Export Analytics Summary to Excel
exports.exportAnalyticsSummaryToExcel = async (req, res) => {
  try {
    const { period, startDate, endDate } = req.query;

    if (!period) {
      return res.status(400).json({
        message: "Period is required (daily, monthly, yearly, or period)",
      });
    }

    let dateRange;
    if (period === "period") {
      if (!startDate || !endDate) {
        return res.status(400).json({
          message:
            "Both startDate and endDate are required for period analytics",
        });
      }
      dateRange = getDateRange("period", startDate, endDate);
    } else {
      if (!startDate) {
        return res.status(400).json({ message: "startDate is required" });
      }
      dateRange = getDateRange(period, startDate);
    }

    const { start, end } = dateRange;

    const sales = await SellMaster.find({
      createdAt: { $gte: start, $lte: end },
      owner: req.user.workingPlace,
    })
      .populate("customer", "name customerType")
      .populate("cylinders.cylinder", "cylinderSize")
      .populate("returnedCylinders", "cylinderSize")
      .populate("staff", "name");

    const analytics = calculateAnalytics(sales);

    // Create Excel workbook
    const workbook = createStyledWorkbook();
    const worksheet = workbook.addWorksheet("Analytics Summary");

    // Set column widths
    worksheet.columns = [{ width: 25 }, { width: 15 }, { width: 15 }];

    // Add title
    const titleRow = worksheet.addRow(["Analytics Summary Report"]);
    titleRow.getCell(1).font = { bold: true, size: 16 };
    worksheet.mergeCells(1, 1, 1, 3);
    titleRow.getCell(1).alignment = { horizontal: "center" };

    // Add period info
    worksheet.addRow([]);
    worksheet.addRow([`Period: ${period.toUpperCase()}`]);
    worksheet.addRow([
      `From: ${moment(start).tz("Asia/Yangon").format("DD MMM YYYY")}`,
    ]);
    worksheet.addRow([
      `To: ${moment(end).tz("Asia/Yangon").format("DD MMM YYYY")}`,
    ]);
    worksheet.addRow([]);

    // Sales Summary
    const salesHeaderRow = worksheet.addRow(["Sales Summary", "", ""]);
    styleHeaders(worksheet, salesHeaderRow);

    worksheet.addRow(["Total Sales", analytics.totalSales, "MMK"]);
    worksheet.addRow(["Total Discount", analytics.totalDiscount, "MMK"]);
    worksheet.addRow(["Net Sales", analytics.netSales, "MMK"]);
    worksheet.addRow([
      "Total Transactions",
      analytics.totalTransactions,
      "Count",
    ]);
    worksheet.addRow([
      "Average Transaction Value",
      analytics.netSales / analytics.totalTransactions || 0,
      "MMK",
    ]);
    worksheet.addRow([]);

    // Quantity Summary
    const quantityHeaderRow = worksheet.addRow(["Quantity Summary", "", ""]);
    styleHeaders(worksheet, quantityHeaderRow);

    worksheet.addRow([
      "Total Delivered",
      analytics.totalQuantitySold,
      "Cylinders",
    ]);
    worksheet.addRow([
      "Total Returned",
      analytics.totalQuantityReturned,
      "Cylinders",
    ]);
    worksheet.addRow([
      "Net Delivered",
      analytics.totalQuantitySold - analytics.totalQuantityReturned,
      "Cylinders",
    ]);
    worksheet.addRow([
      "Return Rate",
      (analytics.totalQuantityReturned / analytics.totalQuantitySold) * 100 ||
        0,
      "%",
    ]);
    worksheet.addRow([]);

    // Payment Analysis
    const paymentHeaderRow = worksheet.addRow(["Payment Analysis", "", ""]);
    styleHeaders(worksheet, paymentHeaderRow);

    worksheet.addRow(["Cash Sales Amount", analytics.cashSales.amount, "MMK"]);
    worksheet.addRow([
      "Cash Sales Transactions",
      analytics.cashSales.transactions,
      "Count",
    ]);
    worksheet.addRow([
      "Credit Sales Amount",
      analytics.creditSales.amount,
      "MMK",
    ]);
    worksheet.addRow([
      "Credit Sales Transactions",
      analytics.creditSales.transactions,
      "Count",
    ]);
    worksheet.addRow([
      "Cash Percentage",
      (analytics.cashSales.amount / analytics.totalSales) * 100 || 0,
      "%",
    ]);
    worksheet.addRow([
      "Credit Percentage",
      (analytics.creditSales.amount / analytics.totalSales) * 100 || 0,
      "%",
    ]);
    worksheet.addRow([]);

    // Top Customers
    if (analytics.topCustomers.length > 0) {
      const customersHeaderRow = worksheet.addRow(["Top Customers", "", ""]);
      styleHeaders(worksheet, customersHeaderRow);

      analytics.topCustomers.slice(0, 10).forEach((customer, index) => {
        worksheet.addRow([
          `${index + 1}. ${customer.name}`,
          customer.amount,
          "MMK",
        ]);
      });
    }

    // Style all data rows
    const dataStartRow = 7;
    const dataEndRow = worksheet.lastRow.number;
    styleDataRows(worksheet, dataStartRow, dataEndRow);

    // Set response headers
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=Analytics_Summary_${period}_${
        startDate || "current"
      }.xlsx`
    );

    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
