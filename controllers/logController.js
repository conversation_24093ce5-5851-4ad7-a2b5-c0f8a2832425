const LogModel = require('../models/LogModel');

const getAllLogs = async (req, res) => {
    try {
        const owner = req.user.workingPlace;
        // Default limit to 10 if not specified in query, max limit of 100
        const limit = Math.min(parseInt(req.query.limit) || 10, 100);
        const sortOrder = req.query.sort === 'aes' ? 1 : -1; // Ascending or descending

        const logs = await LogModel.find({ owner })
            .populate('logBy')
            .sort({ createdAt: sortOrder }) // Sort by createdAt field
            .limit(limit);
            
        res.status(200).json(logs);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};


const getLogById = async (req, res) => {
    try {
        const log = await LogModel.findById(req.params.id).populate('logBy');
        if (!log) {
            return res.status(404).json({ message: 'Log not found' });
        }
        res.status(200).json(log);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

const deleteLog = async (req, res) => {
    try {
        const log = await LogModel.findById(req.params.id);
        if (!log) return res.status(404).json({ message: 'Log not found' });
        await log.remove();
        res.status(200).json({ message: 'Log deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
}

module.exports = { getAllLogs, getLogById, deleteLog };