/**
 * Admin Controller
 * 
 * Handles admin user management operations including creation, updates, and user management.
 * Only accessible by root and admin users.
 */

const adminUtils = require('../utils/adminUtils');

/**
 * Create a new admin user
 * POST /api/admin/create-user
 */
exports.createUser = async (req, res) => {
  try {
    // Only root and admin users can create other users
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can create users.' 
      });
    }

    const { name, email, password, role, phoneNumber, address } = req.body;

    // Validate required fields
    if (!name || !email || !password || !role) {
      return res.status(400).json({ 
        message: 'Name, email, password, and role are required' 
      });
    }

    // Only root users can create other root users
    if (role === 'root' && req.user.role !== 'root') {
      return res.status(403).json({ 
        message: 'Only root users can create other root users' 
      });
    }

    const userData = {
      name,
      email,
      password,
      role,
      phoneNumber,
      address,
      verified: true, // Auto-verify admin-created users
      banned: false
    };

    const newUser = await adminUtils.createAdminUser(userData);

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      user: newUser
    });

  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Get all users with optional filtering
 * GET /api/admin/users
 */
exports.getAllUsers = async (req, res) => {
  try {
    // Only root and admin users can view all users
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can view all users.' 
      });
    }

    const { role, verified, banned, page = 1, limit = 10 } = req.query;

    const filters = {};
    if (role) filters.role = role;
    if (verified !== undefined) filters.verified = verified === 'true';
    if (banned !== undefined) filters.banned = banned === 'true';

    const users = await adminUtils.getAllUsers(filters);

    // Implement pagination
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedUsers = users.slice(startIndex, endIndex);

    res.status(200).json({
      success: true,
      users: paginatedUsers,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(users.length / limit),
        totalUsers: users.length,
        hasNext: endIndex < users.length,
        hasPrev: startIndex > 0
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Update user role
 * PUT /api/admin/users/:userId/role
 */
exports.updateUserRole = async (req, res) => {
  try {
    // Only root and admin users can update roles
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can update roles.' 
      });
    }

    const { userId } = req.params;
    const { role } = req.body;

    if (!role) {
      return res.status(400).json({ 
        message: 'Role is required' 
      });
    }

    // Only root users can assign root role
    if (role === 'root' && req.user.role !== 'root') {
      return res.status(403).json({ 
        message: 'Only root users can assign root role' 
      });
    }

    const updatedUser = await adminUtils.updateUserRole(userId, role);

    res.status(200).json({
      success: true,
      message: 'User role updated successfully',
      user: updatedUser
    });

  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Verify user account
 * PUT /api/admin/users/:userId/verify
 */
exports.verifyUser = async (req, res) => {
  try {
    // Only root and admin users can verify users
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can verify users.' 
      });
    }

    const { userId } = req.params;
    const updatedUser = await adminUtils.verifyUser(userId);

    res.status(200).json({
      success: true,
      message: 'User verified successfully',
      user: updatedUser
    });

  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Ban/unban user
 * PUT /api/admin/users/:userId/ban
 */
exports.setBanStatus = async (req, res) => {
  try {
    // Only root and admin users can ban/unban users
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can ban/unban users.' 
      });
    }

    const { userId } = req.params;
    const { banned } = req.body;

    if (banned === undefined) {
      return res.status(400).json({ 
        message: 'Banned status is required' 
      });
    }

    const updatedUser = await adminUtils.setBanStatus(userId, banned);

    res.status(200).json({
      success: true,
      message: `User ${banned ? 'banned' : 'unbanned'} successfully`,
      user: updatedUser
    });

  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Reset user password
 * PUT /api/admin/users/:userId/reset-password
 */
exports.resetUserPassword = async (req, res) => {
  try {
    // Only root and admin users can reset passwords
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can reset passwords.' 
      });
    }

    const { userId } = req.params;
    const { newPassword } = req.body;

    if (!newPassword) {
      return res.status(400).json({ 
        message: 'New password is required' 
      });
    }

    const updatedUser = await adminUtils.resetUserPassword(userId, newPassword);

    res.status(200).json({
      success: true,
      message: 'Password reset successfully',
      user: updatedUser
    });

  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Delete user (soft delete by banning)
 * DELETE /api/admin/users/:userId
 */
exports.deleteUser = async (req, res) => {
  try {
    // Only root users can delete users
    if (req.user.role !== 'root') {
      return res.status(403).json({ 
        message: 'Access denied. Only root users can delete users.' 
      });
    }

    const { userId } = req.params;

    // Prevent self-deletion
    if (userId === req.user._id.toString()) {
      return res.status(400).json({ 
        message: 'Cannot delete your own account' 
      });
    }

    const deletedUser = await adminUtils.deleteUser(userId);

    res.status(200).json({
      success: true,
      message: 'User deleted successfully',
      user: deletedUser
    });

  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Check if user exists
 * GET /api/admin/check-user/:email
 */
exports.checkUserExists = async (req, res) => {
  try {
    // Only root and admin users can check user existence
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can check user existence.' 
      });
    }

    const { email } = req.params;
    const exists = await adminUtils.userExists(email);

    res.status(200).json({
      success: true,
      exists: exists,
      email: email
    });

  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message
    });
  }
};

/**
 * Get user statistics
 * GET /api/admin/stats
 */
exports.getUserStats = async (req, res) => {
  try {
    // Only root and admin users can view stats
    if (!['root', 'admin'].includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'Access denied. Only root and admin users can view statistics.' 
      });
    }

    const [
      totalUsers,
      verifiedUsers,
      bannedUsers,
      rootUsers,
      adminUsers,
      managerUsers,
      saleUsers,
      fillUsers
    ] = await Promise.all([
      adminUtils.getAllUsers(),
      adminUtils.getAllUsers({ verified: true }),
      adminUtils.getAllUsers({ banned: true }),
      adminUtils.getAllUsers({ role: 'root' }),
      adminUtils.getAllUsers({ role: 'admin' }),
      adminUtils.getAllUsers({ role: 'manager' }),
      adminUtils.getAllUsers({ role: 'sale' }),
      adminUtils.getAllUsers({ role: 'fill' })
    ]);

    const stats = {
      total: totalUsers.length,
      verified: verifiedUsers.length,
      banned: bannedUsers.length,
      unverified: totalUsers.length - verifiedUsers.length,
      active: totalUsers.length - bannedUsers.length,
      roleBreakdown: {
        root: rootUsers.length,
        admin: adminUsers.length,
        manager: managerUsers.length,
        sale: saleUsers.length,
        fill: fillUsers.length
      }
    };

    res.status(200).json({
      success: true,
      stats: stats
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};
