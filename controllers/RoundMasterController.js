const RoundMaster = require('../models/RoundMaster');
const CylinderMaster = require('../models/CylinderMaster');
const TruckMaster = require('../models/TruckMaster')
const Customer= require('../models/CustomerMaster')
const LogModel = require('../models/LogModel');
// Create a new round
exports.createRound = async (req, res) => {
    try {
        const roundData = { ...req.body };
        roundData.owner=req.user.workingPlace;
        // delete roundData.serialNumber; // Ensure serialNumber is not included in the request body
        const truck = await TruckMaster.findOne({truckNumber:req.body.truck});
        console.log(truck)
    if(!truck){
        return res.status(404).json({message:'No truck found!'})
    }
       if(truck.status!='Available'){
        return res.status(400).json({message:'Truck is not available'})
       }
       roundData.truck=truck._id;
       

        const round = new RoundMaster(roundData);
        await round.save();
        truck.status='NotAvailable';
        truck.lastUsed = req.user._id;

        await truck.save();
        const log = new LogModel({
            logType: 'Create Round',
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            remarks: `Round Created with Serial Number ${round.serialNumber}`,
        });
        await log.save();
        // Add logging
        res.status(201).send(round);
    } catch (error) {
        res.status(400).send(error);
    }
};
exports.addCylinderToRound = async (req, res) => {
    try {
        const round = await RoundMaster.findOne({ serialNumber: req.params.roundId });
        if (!round) {
            return res.status(404).json({ message: 'Round not found' });

        }

    if(round.status!='Loading'){
        return res.status(400).json({message:'Round is not in loading state'})
    }
        
    if(!req.body.cylinderId){
        return res.status(400).json({ message: 'Cylinder ID is required' });
    }
        const cylinder = await CylinderMaster.findOne({ serialNumber: req.body.cylinderId}
);
        if(!cylinder){
            return res.status(404).json({ message: 'Cylinder not found' });
        }
    if(cylinder.status!='Available'){
        if(cylinder.status=='In Transit'){
            return res.status(400).json({message:'Cylinder is already in transit'});
        }
        return res.status(400).json({message:'Cylinder is not ready to deliver'});
    }


        cylinder.status = 'In Transit';
        cylinder.takenTruck= round.truck;
        cylinder.latestDelivery=round._id;
        await cylinder.save();
        round.deliveringCylinders.push(cylinder._id);
        await round.save();
        const log = new LogModel({
            logType: 'Add Cylinder to Round',
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            remarks: `Cylinder added to round with Serial Number ${round.serialNumber}`,
        });
        await log.save();
        res.status(200).send(round);
    } catch (error) {
        res.status(400).send(error);
    }
}
exports.sellCylinder = async(req,res)=>{
    const customerId = req.body.customer;
    const cylinderId = req.body.cylinderId;

    try{

        const round = await RoundMaster.findById(req.params.roundId);
        if (!round) {
            return res.status(404).json({ message: 'Round not found' });
        }
        const customer = await Customer.findById(customerId);
        if(!customer){
            return res.status(404).json({ message: 'Customer not found' });
        }
        const cylinder = await CylinderMaster.findById(cylinderId);
        if(!cylinder){
            return res.status(404).json({message:"Cylinder not found!"})
        }
        if(cylinder.status!='In Transit'){
            return res.status(400).json({message:"Cylinder is not able to sell"});
        }
        if (!round.deliveringCylinders.includes(cylinderId)) {
            return res.status(400).json({ message: 'Cylinder is not part of this round' });
        }
        round.sell.push(cylinder._id);
        await round.save();
        customer.purchasedHistory.push(cylinder._id);
        await customer.save();
        const log = new LogModel({
            logType: 'Sell Cylinder',
            logBy: req.user._id,
            logDate: new Date(),owner:req.user.workingPlace,
            remarks: `Cylinder sold to customer with ID ${customer._id}`,
        });
        await log.save();
        res.status(200).send(round);

    }catch(error){
        res.status(500).json({message:error})
    }
}
exports.completeRound = async(req,res)=>{
    try{
        let round;
        
        if(req.params.roundId.startsWith('R')){
          round = await RoundMaster.findOne({serialNumber:req.params.roundId}).populate("truck deliveringCylinders")

        }else{
         round   = await RoundMaster.findById(req.params.roundId);
        }
        if(!round){
            return res.status(404).json({message:"Round not found"});
        }
        const truck= await TruckMaster.findById(round.truck);
        if(!truck){
            return res.status(404).json({message:"Truck not found"})
        }
        truck.status = "Available"
        truck.lastUsed = req.user._id;
        await truck.save()

        round.status="Delivery Completed";
        await round.save();
        for( let i=0;i<round.deliveringCylinders.length;i++){
            const cylinder = await CylinderMaster.findById(round.deliveringCylinders[i]);
            if(!cylinder){
                return res.status(404).json({message:"Cylinder not found"})
            }
            if(cylinder.status=='Delivered'){
            cylinder.status='Available';
            cylinder.takenTruck=null;
            cylinder.latestDelivery=null;
            await cylinder.save();
            }
        }
       const log = new LogModel({
        logType: 'Complete Round',
        logBy: req.user._id,
        logDate: new Date(),owner:req.user.workingPlace,
        remarks: `Round Completed with Serial Number ${round.serialNumber} Truck Number ${truck.truckNumber}`,
    });
    await log.save();
        

        res.status(200).json({message:"Round Completed Successfully!"})




    }catch(e){
        return res.status(400).json({message:e.message});
    }
}

// Get all rounds
exports.getAllRounds = async (req, res) => {
    try {
        const rounds = await RoundMaster.find().populate("truck deliveringCylinders sell");
        res.status(200).send(rounds);
    } catch (error) {
        res.status(500).send(error);
    }
};

// Get a single round by ID
exports.getRoundById = async (req, res) => {
    try {
        let round;
        if(req.params.id.length!=24){
            round = await RoundMaster.findOne({serialNumber:req.params.id}).populate("truck deliveringCylinders sell");
            if (!round) {
                return res.status(404).send();
            }
        } else {
            round = await RoundMaster.findById(req.params.id);
        }
        if (!round) {
            return res.status(404).send();
        }
        res.status(200).send(round);
    } catch (error) {
        res.status(500).send(error);
    }
};

// Update a round by ID
exports.updateRoundById = async (req, res) => {
    try {
        const round = await RoundMaster.findByIdAndUpdate(req.params.id, req.body, { new: true, runValidators: true });
        if (!round) {
            return res.status(404).send();
        }
        res.status(200).send(round);
    } catch (error) {
        res.status(400).send(error);
    }
};

// Delete a round by ID
exports.deleteRoundById = async (req, res) => {
    try {
        const round = await RoundMaster.findByIdAndDelete(req.params.id);
        if (!round) {
            return res.status(404).send();
        }
        res.status(200).send(round);
    } catch (error) {
        res.status(500).send(error);
    }
};

// Get rounds by date
exports.getRoundsByDate = async (req, res) => {
    try {
        const rounds = await RoundMaster.find({ date: req.params.date }).populate("truck deliveringCylinders sell");
        res.status(200).send(rounds);
    } catch (error) {
        res.status(500).send(error);
    }
};

// Get rounds by truck
exports.getRoundsByTruck = async (req, res) => {
    try {
        const rounds = await RoundMaster.find({ truck: req.params.truckId });
        res.status(200).send(rounds);
    } catch (error) {
        res.status(500).send(error);
    }
};

//  report
const generateReport = (rounds) => {
    const report = {
        totalRounds: rounds.length,
        roundsByTruck: {},
        totalDeliveringCylinders: 0,
        totalReturningCylinders: 0
    };

    rounds.forEach(round => {
        if (!report.roundsByTruck[round.truck]) {
            report.roundsByTruck[round.truck] = {
                totalRounds: 0,
                deliveringCylinders: 0,
                returningCylinders: 0
            };
        }
        report.roundsByTruck[round.truck].totalRounds++;
        report.roundsByTruck[round.truck].deliveringCylinders += round.deliveringCylinders.length;
        report.roundsByTruck[round.truck].returningCylinders += round.returnedCylinders.length;
        report.totalDeliveringCylinders += round.deliveringCylinders.length;
        report.totalReturningCylinders += round.returnedCylinders.length;
    });

    return report;
};

//  daily report
exports.getDailyReport = async (req, res) => {
    try {
        const date = req.params.date;
        const rounds = await RoundMaster.find({ date }).populate('deliveringCylinders returnedCylinders');
        const report = generateReport(rounds);
        report.date = date;
        res.status(200).send(report);
    } catch (error) {
        res.status(500).send(error);
    }
};

//  weekly report
exports.getWeeklyReport = async (req, res) => {
    try {
        const { startDate, endDate } = req.params;
        const rounds = await RoundMaster.find({ date: { $gte: startDate, $lte: endDate } }).populate('deliveringCylinders returnedCylinders');
        const report = generateReport(rounds);
        report.startDate = startDate;
        report.endDate = endDate;
        res.status(200).send(report);
    } catch (error) {
        res.status(500).send(error);
    }
};

//  monthly report
exports.getMonthlyReport = async (req, res) => {
    try {
        const { month, year } = req.params;
        const rounds = await RoundMaster.find({ date: { $regex: `^${year}-${month}` } }).populate('deliveringCylinders returnedCylinders');
        const report = generateReport(rounds);
        report.month = month;
        report.year = year;
        res.status(200).send(report);
    } catch (error) {
        res.status(500).send(error);
    }
};

//  yearly report
exports.getYearlyReport = async (req, res) => {
    try {
        const { year } = req.params;
        const rounds = await RoundMaster.find({ date: { $regex: `^${year}` } }).populate('deliveringCylinders returnedCylinders');
        const report = generateReport(rounds);
        report.year = year;
        res.status(200).send(report);
    } catch (error) {
        res.status(500).send(error);
    }
};

//  all-time report
exports.getAllTimeReport = async (req, res) => {
    try {
        const rounds = await RoundMaster.find().populate('deliveringCylinders returnedCylinders');
        const report = generateReport(rounds);
        res.status(200).send(report);
    } catch (error) {
        res.status(500).send(error);
    }
};

exports.setRoundStatus = async(req,res)=>{
    try{
        const round = await RoundMaster.findById(req.params.id);
        if (!round) {
            return res.status(404).json({ message: 'Round not found' });
        }
        if(req.body.status=='Completed'){
            round.status='Completed';
            round.deliveringCylinders.forEach(async(cylinderId)=>{
                const cylinder = await CylinderMaster.findById(cylinderId);
                cylinder.status='Available';
                cylinder.takenTruck=null;
                cylinder.latestDelivery=null;
                await cylinder.save();
            })
            round.deliveringCylinders=[];
            round.returnedCylinders.forEach(async(cylinderId)=>{
                const cylinder = await CylinderMaster.findById(cylinderId);
                cylinder.status='Available';
                cylinder.takenTruck=null;
                cylinder.latestDelivery=null;
                await cylinder.save();
            })
            round.returnedCylinders=[];
        }
        if(req.body.status=='Delivery Started'){
            round.status='Delivery Started';
            round.deliveringCylinders.forEach(async(cylinderId)=> {
                const cylinder = await CylinderMaster.findById(cylinderId);
                cylinder.status='Delivered';
                await cylinder.save();

            })

        } 

        await round.save();
        res.status(200).send(round);

    }catch(error){
        res.status(500).json({message:error})
    }
}

